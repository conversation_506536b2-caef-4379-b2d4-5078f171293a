## Simple License

简单的授权文件生成和注册

### 程序注册的业务流程

1、客户运行待注册软件
2、程序启动后，显示当前的“机器识别码”，提示客户导入授权文件。
3、客户复制“机器识别码”，提交给程序提供商。
4、程序提供商收到客户发来的“机器识别码”，运行【授权文件制作工具】，输入“机器识别码”，生成相应的授权文件发送给客户。
5、客户收到授权文件后，导入即可。


### 客户程序

>  客户程序中包含公钥，用于校验授权文件。

### 授权文件

>  授权文件中包含“机器识别码”和签名数据，客户程序在校验时采用公钥进行核对。

### 授权文件制作工具

>  授权文件制作工具中包含私钥，用于对授权文件进行签名。

### 授权文件校验工具

>  授权文件校验工具中包含公钥，用于对授权文件进行校验。

### 授权文件制作工具

>  授权文件制作工具采用公钥私钥算法对授权文件进行签名和校验， 生成的授权文件中包含“机器识别码”和签名数据，客户程序在校验时采用公钥进行核对。

### 授权文件校验工具

>  授权文件校验工具采用公钥私钥算法对授权文件进行校验， 生成的授权文件中包含“机器识别码”和签名数据，客户程序在校验时采用公钥进行核对。