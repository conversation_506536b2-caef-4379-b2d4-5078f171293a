import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
import time
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.machine_code import get_machine_code
from src.license_verifier import SecureLicenseVerifier

# 添加常量定义
PEM_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'pem')
PUBLIC_KEY_PATH = os.path.join(PEM_DIR, 'public_key.pem')

class LicenseClientDemo:
    def __init__(self, root):
        self.root = root
        self.root.title('授权管理')
        self.root.geometry('600x400')
        self.root.resizable(False, False)
        
        # 创建主框架
        self.main_frame = ttk.Frame(root, padding="20")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 设置样式
        style = ttk.Style()
        style.configure('Title.TLabel', font=('Arial', 14, 'bold'))
        style.configure('Info.TLabel', font=('Arial', 10))
        style.configure('Status.TLabel', font=('Arial', 10, 'bold'))
        
        # 标题
        ttk.Label(
            self.main_frame, 
            text="软件授权管理系统", 
            style='Title.TLabel'
        ).grid(row=0, column=0, columnspan=3, pady=20)
        
        # 创建各个区域
        self.create_machine_code_section()
        self.create_license_status_section()
        self.create_action_buttons()
        
        # 初始化验证器
        try:
            self.verifier = SecureLicenseVerifier(PUBLIC_KEY_PATH)
            # 获取机器码
            self.machine_code = get_machine_code()
            self.machine_code_var.set(self.machine_code)
            # 自动检查授权状态
            self.check_license_status()
        except Exception as e:
            messagebox.showerror("错误", f"初始化失败: {str(e)}")
    
    def create_machine_code_section(self):
        """创建机器码显示区域"""
        # 机器码标签
        ttk.Label(
            self.main_frame,
            text="机器码:",
            style='Info.TLabel'
        ).grid(row=1, column=0, sticky=tk.W, pady=(20,5))
        
        # 机器码显示框和复制按钮的容器
        code_frame = ttk.Frame(self.main_frame)
        code_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E))
        code_frame.columnconfigure(0, weight=1)
        
        # 机器码显示框
        self.machine_code_var = tk.StringVar()
        machine_code_entry = ttk.Entry(
            code_frame,
            textvariable=self.machine_code_var,
            width=50,
            state='readonly'
        )
        machine_code_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 复制按钮
        ttk.Button(
            code_frame,
            text="复制",
            command=self.copy_machine_code,
            width=10
        ).grid(row=0, column=1)
    
    def create_license_status_section(self):
        """创建授权状态显示区域"""
        # 状态框架
        status_frame = ttk.LabelFrame(
            self.main_frame,
            text="授权状态",
            padding=10
        )
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=20)
        
        # 状态显示
        self.status_var = tk.StringVar(value="未授权")
        self.status_label = ttk.Label(
            status_frame,
            textvariable=self.status_var,
            style='Status.TLabel'
        )
        self.status_label.grid(row=0, column=0, sticky=tk.W)
    
    def create_action_buttons(self):
        """创建操作按钮区域"""
        button_frame = ttk.Frame(self.main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=20)
        
        # 导入授权文件按钮
        ttk.Button(
            button_frame,
            text="导入授权文件",
            command=self.import_license,
            width=20
        ).grid(row=0, column=0, padx=10)
        
        # 刷新状态按钮
        ttk.Button(
            button_frame,
            text="刷新状态",
            command=self.check_license_status,
            width=20
        ).grid(row=0, column=1, padx=10)
    
    def copy_machine_code(self):
        """复制机器码到剪贴板"""
        self.root.clipboard_clear()
        self.root.clipboard_append(self.machine_code_var.get())
        messagebox.showinfo("提示", "机器码已复制到剪贴板！")
    
    def import_license(self):
        """导入授权文件"""
        file_path = filedialog.askopenfilename(
            title='选择授权文件',
            filetypes=[('License Files', '*.license'), ('All Files', '*.*')]
        )
        
        if file_path:
            try:
                result, message = self.verifier.verify_license(
                    file_path,
                    self.machine_code
                )
                
                if result:
                    # 保存授权文件到程序目录
                    license_dir = os.path.join(os.path.expanduser('~'), '.licenses')
                    os.makedirs(license_dir, exist_ok=True)
                    new_path = os.path.join(license_dir, os.path.basename(file_path))
                    
                    with open(file_path, 'rb') as src, open(new_path, 'wb') as dst:
                        dst.write(src.read())
                    
                    messagebox.showinfo("成功", message)
                    self.check_license_status()
                else:
                    messagebox.showerror("错误", message)
            
            except Exception as e:
                messagebox.showerror("错误", f"导入失败: {str(e)}")
    
    def check_license_status(self):
        """检查授权状态"""
        license_dir = os.path.join(os.path.expanduser('~'), '.licenses')
        if not os.path.exists(license_dir):
            self.status_var.set("未授权")
            return
        
        # 检查所有授权文件
        valid_licenses = []
        for filename in os.listdir(license_dir):
            if filename.endswith('.license'):
                file_path = os.path.join(license_dir, filename)
                try:
                    result, message = self.verifier.verify_license(
                        file_path,
                        self.machine_code
                    )
                    if result:
                        valid_licenses.append(message)
                except:
                    continue
        
        if valid_licenses:
            # 显示最长有效期的授权信息
            self.status_var.set(valid_licenses[0])
        else:
            self.status_var.set("未授权")

def main():
    root = tk.Tk()
    app = LicenseClientDemo(root)
    root.mainloop()

if __name__ == '__main__':
    main() 