import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
import time
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.generate_keys import generate_key_pair

from src.license_generator import SecureLicenseGenerator, LicenseType

def get_pem_dir():
    base_path = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(base_path, "pem")

# 替换原有的PEM_DIR常量定义
PEM_DIR = get_pem_dir()
PRIVATE_KEY_PATH = os.path.join(PEM_DIR, "private_key.pem")
PUBLIC_KEY_PATH = os.path.join(PEM_DIR, "public_key.pem")


class LicenseGeneratorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("灵象授权工具")
        self.root.geometry("900x600")
        self.root.resizable(<PERSON>alse, False)

        # 确保pem目录存在
        os.makedirs(PEM_DIR, exist_ok=True)

        # 检查密钥文件
        if not self.check_key_files():
            self.show_key_generation_dialog()
            return

        # 创建主框架并添加padding
        self.main_frame = ttk.Frame(root, padding="20")
        self.main_frame.pack(expand=True, fill="both", padx=20, pady=20)

        # 设置样式
        style = ttk.Style()
        style.configure("Title.TLabel", font=("Microsoft YaHei", 20, "bold"))
        style.configure("Info.TLabel", font=("Microsoft YaHei", 10))
        style.configure("Section.TLabelframe", padding=15)
        style.configure("Action.TButton", padding=(10, 5), font=("Microsoft YaHei", 10))

        # 标题居中显示
        title_frame = ttk.Frame(self.main_frame)
        title_frame.pack(fill="x", pady=(0, 20))
        ttk.Label(title_frame, text="灵象授权工具", style="Title.TLabel").pack(
            anchor="center"
        )

        # 创建各个区域
        self.create_machine_code_section()
        self.create_license_type_section()
        self.create_expiry_section()
        self.create_action_buttons()

        # 修改生成器初始化
        self.generator = SecureLicenseGenerator(PRIVATE_KEY_PATH)

    def check_key_files(self):
        """检查密钥文件是否存在"""
        return os.path.exists(PRIVATE_KEY_PATH) and os.path.exists(PUBLIC_KEY_PATH)

    def show_key_generation_dialog(self):
        """显示密钥生成对话框"""
        # 清空主窗口现有内容
        for widget in self.root.winfo_children():
            widget.destroy()

        # 创建提示标签和生成按钮
        frame = ttk.Frame(self.root, padding="20")
        frame.pack(expand=True)

        # 调整标签样式，确保文字不会换行
        ttk.Label(
            frame,
            text="未检测到密钥文件，请先生成密钥",
            style="Title.TLabel",
            wraplength=600,  # 设置文字换行宽度
        ).pack(pady=(0, 20))

        # 增加按钮宽度，与其他按钮保持一致
        ttk.Button(
            frame,
            text="生成密钥",
            command=self.generate_keys,
            style="Action.TButton",
            width=30,
        ).pack()

    def generate_keys(self):
        """生成密钥对"""
        try:
            generate_key_pair(PRIVATE_KEY_PATH, PUBLIC_KEY_PATH)
            messagebox.showinfo("成功", "密钥生成成功！")

            # 不再使用 __init__ 重新初始化，而是直接创建主界面
            # 清空当前窗口
            for widget in self.root.winfo_children():
                widget.destroy()

            # 创建主框架并添加padding
            self.main_frame = ttk.Frame(self.root, padding="20")
            self.main_frame.pack(expand=True, fill="both", padx=20, pady=20)

            # 设置样式
            style = ttk.Style()
            style.configure("Title.TLabel", font=("Microsoft YaHei", 20, "bold"))
            style.configure("Info.TLabel", font=("Microsoft YaHei", 10))
            style.configure("Section.TLabelframe", padding=15)
            style.configure(
                "Action.TButton", padding=(10, 5), font=("Microsoft YaHei", 10)
            )

            # 标题居中显示
            title_frame = ttk.Frame(self.main_frame)
            title_frame.pack(fill="x", pady=(0, 20))
            ttk.Label(title_frame, text="授权文件生成器", style="Title.TLabel").pack(
                anchor="center"
            )

            # 创建各个区域
            self.create_machine_code_section()
            self.create_license_type_section()
            self.create_expiry_section()
            self.create_action_buttons()

            # 初始化生成器
            self.generator = SecureLicenseGenerator(PRIVATE_KEY_PATH)

        except Exception as e:
            messagebox.showerror("错误", f"生成密钥失败: {str(e)}")

    def create_machine_code_section(self):
        """创建机器码输入区域"""
        frame = ttk.LabelFrame(
            self.main_frame, text="机器码", style="Section.TLabelframe"
        )
        frame.pack(fill="x", pady=10)

        input_frame = ttk.Frame(frame)
        input_frame.pack(fill="x", padx=10, pady=10)

        # 机器码输入框和按钮水平排列
        entry_button_frame = ttk.Frame(input_frame)
        entry_button_frame.pack(fill="x")

        self.machine_code_var = tk.StringVar()
        self.machine_code_entry = ttk.Entry(
            entry_button_frame, textvariable=self.machine_code_var, width=100
        )
        self.machine_code_entry.pack(side="left", expand=True, fill="x", padx=(0, 10))

        ttk.Button(
            entry_button_frame, text="粘贴", command=self.paste_machine_code, width=10
        ).pack(side="right")

    def create_license_type_section(self):
        """创建授权类型选择区域"""
        frame = ttk.LabelFrame(
            self.main_frame, text="授权类型", style="Section.TLabelframe"
        )
        frame.pack(fill="x", pady=10)

        # 授权类型选择按钮居中显示
        radio_frame = ttk.Frame(frame)
        radio_frame.pack(pady=5)

        self.license_type_var = tk.StringVar(value="permanent")
        ttk.Radiobutton(
            radio_frame,
            text="永久授权",
            value="permanent",
            variable=self.license_type_var,
            command=self.on_license_type_change,
        ).pack(side="left", padx=20)

        ttk.Radiobutton(
            radio_frame,
            text="临时授权",
            value="time_limited",
            variable=self.license_type_var,
            command=self.on_license_type_change,
        ).pack(side="left", padx=20)

    def create_expiry_section(self):
        """创建过期时间设置区域"""
        self.expiry_frame = ttk.LabelFrame(
            self.main_frame, text="过期时间", style="Section.TLabelframe"
        )
        self.expiry_frame.pack(fill="x", pady=10)

        # 预设时间选择按钮居中显示
        presets_frame = ttk.Frame(self.expiry_frame)
        presets_frame.pack(pady=10)

        self.preset_buttons = []
        presets = [
            ("1个月", 30),
            ("3个月", 90),
            ("1年", 365),
            ("3年", 1095),
            ("5年", 1825),
        ]

        for text, days in presets:
            btn = ttk.Button(
                presets_frame,
                text=text,
                command=lambda d=days: self.set_expiry_days(d),
                width=12,
            )
            btn.pack(side="left", padx=8)
            self.preset_buttons.append(btn)

        # 自定义日期选择居中显示
        custom_frame = ttk.Frame(self.expiry_frame)
        custom_frame.pack(pady=10)

        ttk.Label(custom_frame, text="自定义日期:").pack(side="left", padx=5)

        self.expiry_var = tk.StringVar()
        self.expiry_entry = ttk.Entry(
            custom_frame, textvariable=self.expiry_var, width=20
        )
        self.expiry_entry.pack(side="left", padx=5)

        ttk.Label(custom_frame, text="格式: YYYY-MM-DD").pack(side="left", padx=5)

        # 默认禁用过期时间设置
        self.toggle_expiry_widgets(False)

    def create_action_buttons(self):
        """创建操作按钮区域"""
        button_frame = ttk.Frame(self.main_frame)
        button_frame.pack(pady=20)

        ttk.Button(
            button_frame,
            text="生成授权文件",
            command=self.generate_license,
            style="Action.TButton",
            width=30,
        ).pack(side="left", padx=10)

        ttk.Button(
            button_frame,
            text="清除",
            command=self.clear_form,
            style="Action.TButton",
            width=30,
        ).pack(side="left", padx=10)

    def paste_machine_code(self):
        """粘贴机器码"""
        try:
            machine_code = self.root.clipboard_get().strip()
            # 只验证基本格式
            if not all(c in "0123456789abcdef-" for c in machine_code.lower()):
                messagebox.showwarning("警告", "机器码格式不正确！")
                return
            self.machine_code_var.set(machine_code)
        except:
            messagebox.showwarning("警告", "剪贴板中没有可用的机器码！")

    def on_license_type_change(self):
        """授权类型改变时的处理"""
        is_time_limited = self.license_type_var.get() == "time_limited"
        self.toggle_expiry_widgets(is_time_limited)

    def toggle_expiry_widgets(self, enabled):
        """启用/禁用过期时间相关控件"""
        state = "normal" if enabled else "disabled"
        for btn in self.preset_buttons:
            btn["state"] = state
        self.expiry_entry["state"] = state

    def set_expiry_days(self, days):
        """设置预设的过期天数"""
        expiry_date = datetime.now() + timedelta(days=days)
        self.expiry_var.set(expiry_date.strftime("%Y-%m-%d"))

    def clear_form(self):
        """清除表单"""
        self.machine_code_var.set("")
        self.license_type_var.set("permanent")
        self.expiry_var.set("")
        self.toggle_expiry_widgets(False)

    def generate_license(self):
        """生成授权文件"""
        # 验证机器码
        machine_code = self.machine_code_var.get().strip()
        if not machine_code:
            messagebox.showerror("错误", "请输入机器码！")
            return

        # 只验证基本格式
        if not all(c in "0123456789abcdef-" for c in machine_code.lower()):
            messagebox.showerror("错误", "机器码格式不正确！")
            return

        try:
            # 修改默认保存路径
            default_dir = os.path.join(PEM_DIR, "licenses")
            os.makedirs(default_dir, exist_ok=True)

            # 使用机器码作为默认文件名
            default_filename = f"{machine_code}.license"
            default_path = os.path.join(default_dir, default_filename)

            file_path = filedialog.asksaveasfilename(
                title="保存授权文件",
                initialdir=default_dir,
                initialfile=default_filename,
                defaultextension=".license",
                filetypes=[("License Files", "*.license"), ("All Files", "*.*")],
            )

            if not file_path:
                return

            # 生成授权文件
            if self.license_type_var.get() == "permanent":
                self.generator.generate_permanent_license(machine_code, file_path)
            else:
                # 解析过期时间
                expiry_str = self.expiry_var.get().strip()
                if not expiry_str:
                    messagebox.showerror("错误", "请设置过期时间！")
                    return

                try:
                    expiry_date = datetime.strptime(expiry_str, "%Y-%m-%d")
                    expiry_timestamp = expiry_date.timestamp()

                    self.generator.generate_time_limited_license(
                        machine_code, expiry_timestamp, file_path
                    )
                except ValueError:
                    messagebox.showerror(
                        "错误", "无效的日期格式！请使用 YYYY-MM-DD 格式。"
                    )
                    return

            messagebox.showinfo("成功", "授权文件生成成功！")
            
            # 打开文件所在目录
            file_dir = os.path.dirname(file_path)
            if sys.platform == 'darwin':  # macOS
                os.system(f'open "{file_dir}"')
            elif sys.platform == 'win32':  # Windows
                os.system(f'explorer "{file_dir}"')
            else:  # Linux
                os.system(f'xdg-open "{file_dir}"')

        except Exception as e:
            messagebox.showerror("错误", f"生成授权文件失败: {str(e)}")


def main():
    root = tk.Tk()
    app = LicenseGeneratorGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
