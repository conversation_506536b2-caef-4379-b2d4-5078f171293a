from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.backends import default_backend
import os

# 添加默认路径常量
PEM_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'pem')
DEFAULT_PRIVATE_KEY_PATH = os.path.join(PEM_DIR, 'private_key.pem')
DEFAULT_PUBLIC_KEY_PATH = os.path.join(PEM_DIR, 'public_key.pem')

def generate_key_pair(private_key_path=DEFAULT_PRIVATE_KEY_PATH, public_key_path=DEFAULT_PUBLIC_KEY_PATH):
    """生成RSA密钥对"""
    # 确保目录存在
    os.makedirs(os.path.dirname(private_key_path), exist_ok=True)
    
    # 生成私钥
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
        backend=default_backend()
    )
    
    # 获取公钥
    public_key = private_key.public_key()
    
    # 保存私钥
    with open(private_key_path, 'wb') as f:
        f.write(private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        ))
    
    # 保存公钥
    with open(public_key_path, 'wb') as f:
        f.write(public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        ))

if __name__ == '__main__':
    generate_key_pair()
    print("密钥对生成完成！") 