import struct
import time
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend
import os
from enum import Enum

# 添加默认路径常量
PEM_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'pem')
DEFAULT_PRIVATE_KEY_PATH = os.path.join(PEM_DIR, 'private_key.pem')

class LicenseType(Enum):
    """授权类型"""
    PERMANENT = 0    # 永久授权
    TIME_LIMITED = 1 # 限时授权

class SecureLicenseGenerator:
    # 文件格式版本
    VERSION = 1
    # 文件魔数
    MAGIC = b'SLIC'
    
    def __init__(self, private_key_path=DEFAULT_PRIVATE_KEY_PATH):
        """初始化生成器
        :param private_key_path: 私钥文件路径
        """
        self._load_private_key(private_key_path)
    
    def _load_private_key(self, private_key_path):
        """加载私钥"""
        with open(private_key_path, 'rb') as key_file:
            self.private_key = serialization.load_pem_private_key(
                key_file.read(),
                password=None,
                backend=default_backend()
            )
    
    def _pack_license_data(self, machine_code, license_type, expiry_date=None):
        """打包授权数据"""
        # 获取当前时间戳
        created_at = int(time.time())
        
        # 如果是永久授权，过期时间设为0
        if license_type == LicenseType.PERMANENT:
            expiry_date = 0
        
        # 计算机器码长度
        machine_code_bytes = machine_code.encode('utf-8')
        machine_code_length = len(machine_code_bytes)
        
        # 打包数据
        # 格式：机器码长度(2字节) + 机器码 + 授权类型(1字节) + 创建时间(8字节) + 过期时间(8字节)
        return struct.pack(
            f'!H {machine_code_length}s B Q Q',
            machine_code_length,
            machine_code_bytes,
            license_type.value,
            created_at,
            int(expiry_date)
        )
    
    def _sign_data(self, data):
        """对数据进行签名"""
        return self.private_key.sign(
            data,
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
    
    def _write_license_file(self, signature, data, output_path):
        """写入授权文件
        文件格式：魔数(4) + 版本(2) + 保留字节(2) + 签名长度(4) + 数据长度(4) + 签名 + 数据
        """
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'wb') as f:
            # 写入文件头
            f.write(struct.pack(
                '4s H H I I',
                self.MAGIC,           # 魔数
                self.VERSION,         # 版本号
                0,                    # 保留字节
                len(signature),       # 签名长度
                len(data)            # 数据长度
            ))
            # 写入签名和数据
            f.write(signature)
            f.write(data)
    
    def generate_permanent_license(self, machine_code, output_path):
        """生成永久授权文件"""
        # 打包数据
        data = self._pack_license_data(
            machine_code,
            LicenseType.PERMANENT
        )
        
        # 签名
        signature = self._sign_data(data)
        
        # 写入文件
        self._write_license_file(signature, data, output_path)
    
    def generate_time_limited_license(self, machine_code, expiry_timestamp, output_path):
        """生成限时授权文件"""
        # 打包数据
        data = self._pack_license_data(
            machine_code,
            LicenseType.TIME_LIMITED,
            expiry_timestamp
        )
        
        # 签名
        signature = self._sign_data(data)
        
        # 写入文件
        self._write_license_file(signature, data, output_path)

if __name__ == '__main__':
    # 测试代码
    generator = SecureLicenseGenerator()
    
    # 测试生成永久授权
    generator.generate_permanent_license(
        'TEST-MACHINE-CODE-12345',
        os.path.join(PEM_DIR, 'licenses', 'permanent.license')
    )
    
    # 测试生成30天的限时授权
    generator.generate_time_limited_license(
        'TEST-MACHINE-CODE-12345',
        time.time() + 30*24*60*60,  # 30天后过期
        os.path.join(PEM_DIR, 'licenses', 'time_limited.license')
    ) 