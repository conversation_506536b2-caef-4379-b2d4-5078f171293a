import struct
import time
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import platform
import ctypes
import sys
import os
from enum import Enum

# 添加默认路径常量
PEM_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'pem')
DEFAULT_PUBLIC_KEY_PATH = os.path.join(PEM_DIR, 'public_key.pem')

class LicenseType(Enum):
    """授权类型"""
    PERMANENT = 0    # 永久授权
    TIME_LIMITED = 1 # 限时授权

class SecureLicenseVerifier:
    # 文件格式版本
    VERSION = 1
    # 文件魔数
    MAGIC = b'SLIC'
    
    def __init__(self, public_key_path=DEFAULT_PUBLIC_KEY_PATH):
        """初始化验证器
        :param public_key_path: 公钥文件路径
        """
        self._load_public_key(public_key_path)
        self._check_debugger()
        self._init_security()
    
    
    def _check_debugger(self):
        """检测调试器"""
        if platform.system() == 'Windows':
            if ctypes.windll.kernel32.IsDebuggerPresent():
                sys.exit(1)
    
    def _init_security(self):
        """初始化安全环境"""
        # 混淆关键数据
        self._key_data = bytes([x ^ 0x55 for x in self.public_key.public_bytes(
            encoding=serialization.Encoding.DER,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )])
    
    def _unpack_license_data(self, decrypted_data):
        """解包授权数据"""
        try:
            # 首先读取机器码长度（前2个字节）
            machine_code_length = struct.unpack('!H', decrypted_data[:2])[0]
            print(f"机器码长度: {machine_code_length}")
            
            # 计算完整的格式字符串
            format_str = f'!H {machine_code_length}s B Q Q'
            total_size = struct.calcsize(format_str)
            print(f"需要的总字节数: {total_size}, 实际数据长度: {len(decrypted_data)}")
            
            # 解析所有数据
            length, machine_code, license_type, created_at, expiry = struct.unpack(
                format_str,
                decrypted_data[:total_size]
            )
            
            return {
                'machine_code': machine_code.decode('utf-8'),
                'license_type': LicenseType(license_type),
                'created_at': created_at,
                'expiry_date': expiry
            }
        except Exception as e:
            print(f"解包数据时出错: {str(e)}")
            print(f"数据内容: {decrypted_data.hex()}")
            raise
    
    def _load_public_key(self, public_key_path):
        """加载公钥"""
        with open(public_key_path, 'rb') as key_file:
            self.public_key = serialization.load_pem_public_key(
                key_file.read(),
                backend=default_backend()
            )
    
    def verify_license(self, license_data, machine_code):
        """验证授权文件"""
        try:
            print("开始验证过程...")
            # 如果输入是文件路径，读取文件内容
            if isinstance(license_data, str):
                print(f"读取授权文件: {license_data}")
                with open(license_data, 'rb') as f:
                    license_data = f.read()
                print(f"文件大小: {len(license_data)} 字节")
            
            # 解析文件头
            header_format = '4s H H I I'  # 修改格式：魔数(4) + 版本(2) + 保留字节(2) + 签名长度(4) + 数据长度(4)
            header_size = struct.calcsize(header_format)
            print(f"解析文件头，头部大小: {header_size} 字节")
            
            magic, version, reserved, signature_size, data_size = struct.unpack(
                header_format,
                license_data[:header_size]
            )
            print(f"文件头信息:")
            print(f"- 魔数: {magic}")
            print(f"- 版本: {version}")
            print(f"- 签名大小: {signature_size}")
            print(f"- 数据大小: {data_size}")
            
            # 验证文件格式
            if magic != self.MAGIC:
                return False, "无效的授权文件格式"
            if version != self.VERSION:
                return False, "不支持的授权文件版本"
            
            # 提取签名和数据
            pos = header_size
            signature = license_data[pos:pos+signature_size]
            pos += signature_size
            license_content = license_data[pos:pos+data_size]
            
            print("\n数据段信息:")
            print(f"- 签名长度: {len(signature)}")
            print(f"- 数据长度: {len(license_content)}")
            
            # 使用公钥验证签名
            try:
                print("\n开始验证签名...")
                self.public_key.verify(
                    signature,
                    license_content,
                    padding.PSS(
                        mgf=padding.MGF1(hashes.SHA256()),
                        salt_length=padding.PSS.MAX_LENGTH
                    ),
                    hashes.SHA256()
                )
                print("签名验证成功")
            except Exception as e:
                print(f"签名验证失败: {str(e)}")
                return False, "签名验证失败，授权文件可能被篡改"
            
            # 解析授权数据
            print("\n解析授权数据...")
            license_info = self._unpack_license_data(license_content)
            print("授权信息:")
            print(f"- 机器码: {license_info['machine_code']}")
            print(f"- 授权类型: {license_info['license_type']}")
            print(f"- 创建时间: {time.ctime(license_info['created_at'])}")
            print(f"- 过期时间: {time.ctime(license_info['expiry_date']) if license_info['expiry_date'] else '永久'}")
            
            # 验证机器码
            print(f"\n验证机器码...")
            print(f"当前机器码: {machine_code}")
            if license_info['machine_code'] != machine_code:
                return False, "机器码不匹配"
            
            # 根据授权类型验证
            print("\n验证授权有效期...")
            if license_info['license_type'] == LicenseType.TIME_LIMITED:
                if time.time() > license_info['expiry_date'] or int(license_info['created_at']) > time.time():
                    return False, "授权已过期"
                remaining_days = (license_info['expiry_date'] - time.time()) / (24*60*60)
                return True, f"验证通过，剩余 {int(remaining_days)} 天"
            else:
                return True, "验证通过（永久授权）"
            
        except Exception as e:
            print(f"\n验证过程出现异常: {str(e)}")
            return False, f"验证过程出错: {str(e)}"
    
    def _log_verification(self, result, machine_code):
        """记录验证结果"""
        try:
            log_dir = os.path.join(os.path.expanduser('~'), '.license_logs')
            os.makedirs(log_dir, exist_ok=True)
            
            log_file = os.path.join(log_dir, 'verification.log')
            with open(log_file, 'a') as f:
                f.write(f"{time.time()},{machine_code},{result}\n")
        except:
            pass

if __name__ == '__main__':
    import os
    from license_generator import SecureLicenseGenerator
    from machine_code import get_machine_code
    
    # 获取当前机器码
    machine_code = 'TEST-MACHINE-CODE-12345'
    
    # 如果授权文件不存在，先生成测试用的授权文件
    if not os.path.exists(os.path.join(PEM_DIR, 'licenses', 'permanent.license')) or not os.path.exists(os.path.join(PEM_DIR, 'licenses','time_limited.license')):
        print("\n生成测试授权文件...")
        generator = SecureLicenseGenerator()
        
        # 生成永久授权
        generator.generate_permanent_license(
            machine_code,
            os.path.join(PEM_DIR, 'licenses', 'permanent.license')
        )
        
        # 生成30天的限时授权
        generator.generate_time_limited_license(
            machine_code,
            time.time() + 30*24*60*60,  # 30天后过期
            os.path.join(PEM_DIR, 'licenses','time_limited.license')
        )
    
    print("\n开始验证授权...")
    # 测试代码
    verifier = SecureLicenseVerifier()  # 使用公钥进行验证
    
    # 测试永久授权
    print("\n验证永久授权:")
    result, message = verifier.verify_license(
        os.path.join(PEM_DIR, 'licenses', 'permanent.license'),
        machine_code
    )
    print(f"永久授权验证结果: {message}")
    
    # 测试限时授权
    print("\n验证限时授权:")
    result, message = verifier.verify_license(
        os.path.join(PEM_DIR, 'licenses', 'time_limited.license'),
        machine_code
    )
    print(f"限时授权验证结果: {message}") 