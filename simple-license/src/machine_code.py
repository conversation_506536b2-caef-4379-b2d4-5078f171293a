import hashlib
import platform
import subprocess
import re

def get_windows_hardware_id():
    """获取Windows系统的硬件ID"""
    try:
        import wmi
        c = wmi.WMI()
        
        # 优先获取主板序列号
        for board in c.Win32_BaseBoard():
            if board.SerialNumber:
                return f"MB:{board.SerialNumber.strip()}"
        
        # 如果主板序列号获取失败，尝试BIOS序列号
        for bios in c.Win32_BIOS():
            if bios.SerialNumber:
                return f"BIOS:{bios.SerialNumber.strip()}"
                
    except Exception:
        pass
    return "UNKNOWN_HARDWARE"

def get_mac_hardware_id():
    """获取Mac系统的硬件ID"""
    try:
        # 直接使用 ioreg 命令获取平台序列号
        cmd = ['ioreg', '-l']
        output = subprocess.check_output(cmd).decode('utf-8')
        
        # 优先使用 IOPlatformSerialNumber
        serial_match = re.search(r'"IOPlatformSerialNumber" = "([^"]+)"', output)
        if serial_match:
            return f"SYS:{serial_match.group(1).strip()}"
            
        # 备选使用 IOPlatformUUID
        uuid_match = re.search(r'"IOPlatformUUID" = "([^"]+)"', output)
        if uuid_match:
            return f"UUID:{uuid_match.group(1).strip()}"
            
    except Exception:
        pass
    return "UNKNOWN_HARDWARE"

def get_machine_code():
    """
    生成机器唯一识别码
    基于系统类型和硬件ID生成唯一的机器码
    """
    system_type = platform.system()
    
    # 根据操作系统选择合适的硬件ID获取方法
    if system_type == 'Windows':
        hardware_id = get_windows_hardware_id()
    elif system_type == 'Darwin':  # macOS
        hardware_id = get_mac_hardware_id()
    else:
        hardware_id = "UNKNOWN_HARDWARE"
    
    # 组合信息并生成hash
    hardware_str = f"OS:{system_type}#{hardware_id}".encode('utf-8')
    machine_code = hashlib.md5(hardware_str).hexdigest()
    
    # 格式化为更易读的形式 (每4个字符加一个横杠)
    formatted_code = '-'.join(machine_code[i:i+4] for i in range(0, len(machine_code), 4))
    
    return formatted_code

def get_system_info():
    """获取系统信息（用于调试）"""
    return {
        "操作系统": platform.system(),
        "系统版本": platform.version(),
        "硬件ID": get_windows_hardware_id() if platform.system() == 'Windows' else get_mac_hardware_id(),
        "机器码": get_machine_code()
    }

if __name__ == '__main__':
    # 打印详细的系统信息
    info = get_system_info()
    for key, value in info.items():
        print(f"{key}: {value}") 