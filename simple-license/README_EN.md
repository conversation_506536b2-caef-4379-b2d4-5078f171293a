## Simple License

A simple license file generation and registration system

### Program Registration Process

1. Customer runs the software pending registration
2. After program startup, it displays the current "Machine ID" and prompts the customer to import the license file
3. Customer copies the "Machine ID" and submits it to the software provider
4. Software provider receives the "Machine ID" from the customer, runs the [License File Generator], inputs the "Machine ID", and generates the corresponding license file to send to the customer
5. Customer receives and imports the license file

### Client Program

> The client program contains the public key used to verify the license file.

### License File

> The license file contains the "Machine ID" and signature data. The client program verifies it using the public key.

### License File Generator

> The license file generator contains the private key used to sign the license file.

### License File Verification Tool

> The license file verification tool contains the public key used to verify the license file.

### License File Generator Details

> The license file generator uses public-private key algorithms to sign and verify license files. The generated license file contains the "Machine ID" and signature data, which the client program verifies using the public key.

### License File Verification Tool Details

> The license file verification tool uses public-private key algorithms to verify license files. The license file contains the "Machine ID" and signature data, which the client program verifies using the public key. 