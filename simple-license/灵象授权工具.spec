# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['license_generator_gui.py'],
    pathex=['/Users/<USER>/Desktop/Study/XB-AI/simple-license'],
    binaries=[],
    datas=[('/Users/<USER>/Desktop/Study/XB-AI/simple-license/pem', 'pem')],
    hiddenimports=['src.license_generator', 'src.license_verifier', 'src.machine_code', 'cryptography', 'tools.generate_keys'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='灵象授权工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='灵象授权工具',
)
app = BUNDLE(
    coll,
    name='灵象授权工具.app',
    icon=None,
    bundle_identifier=None,
)
