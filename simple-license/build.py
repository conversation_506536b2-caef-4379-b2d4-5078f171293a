import os
import platform
import PyInstaller.__main__
import shutil

def build_app():
    """构建应用程序"""
    # 获取项目根目录
    root_dir = os.path.dirname(os.path.abspath(__file__))
    print(root_dir)
    # 确保pem目录存在
    pem_dir = os.path.join(root_dir, 'pem')
    if not os.path.exists(pem_dir):
        os.makedirs(pem_dir)
    
    # 确保licenses目录存在
    licenses_dir = os.path.join(pem_dir, 'licenses')
    if not os.path.exists(licenses_dir):
        os.makedirs(licenses_dir)
    
    # 基本配置
    common_options = [
        'license_generator_gui.py',  # 主程序文件
        '--name=灵象授权工具',         # 应用名称
        '--clean',                         # 清理临时文件
        '--noconfirm',                     # 不确认覆盖
        '--windowed',                      # GUI模式
        f'--add-data={pem_dir}{os.pathsep}pem',  # 添加pem目录
        f'--paths={root_dir}',            # 添加项目根目录到Python路径
        '--hidden-import=src.license_generator',
        '--hidden-import=src.license_verifier',
        '--hidden-import=src.machine_code',
        '--hidden-import=cryptography',
        '--hidden-import=tools.generate_keys',
    ]
    
    # 根据操作系统添加特定选项
    if platform.system() == 'Windows':
        options = [
            *common_options,
            '--hidden-import=wmi',         # Windows平台需要wmi
        ]
    else:  # macOS
        options = [
            *common_options,
        ]
    
    # 执行打包
    PyInstaller.__main__.run(options)
    
    # # 打包后处理
    # dist_dir = os.path.join(root_dir, 'dist', 'LicenseGenerator')
    # if platform.system() == 'Darwin':  # macOS
    #     dist_dir = os.path.join(root_dir, 'dist', 'LicenseGenerator.app', 'Contents', 'MacOS')
    
    # # 确保目标pem目录存在
    # dist_pem_dir = os.path.join(dist_dir, 'pem')
    # if not os.path.exists(dist_pem_dir):
    #     os.makedirs(dist_pem_dir)
    
    # # 确保目标licenses目录存在
    # dist_licenses_dir = os.path.join(dist_pem_dir, 'licenses')
    # if not os.path.exists(dist_licenses_dir):
    #     os.makedirs(dist_licenses_dir)

if __name__ == '__main__':
    build_app() 