{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "element-plus": "^2.9.2", "html2canvas": "^1.4.1", "unplugin-element-plus": "^0.9.0", "vue": "^3.4.37", "vue-konva": "^3.2.0", "vue-router": "^4.5.0", "vue3-compare-image": "^1.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.2", "sass": "^1.83.1", "unplugin-auto-import": "^0.19.0", "unplugin-vue-components": "^0.28.0", "vite": "^5.4.1", "vite-plugin-javascript-obfuscator": "^3.1.0"}}