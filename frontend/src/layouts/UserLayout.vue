<template>
  <div class="user-layout">
    <div class="user-layout-content">
      <div class="main">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// Check if current page is the user info page
const isUserInfoPage = computed(() => {
  return route.name === 'UserInfo'
})

// Navigate back to home
const goBackHome = () => {
  router.push('/home')
}
</script>

<style scoped>
.user-layout {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 90vh;
  background: #f0f2f5 url('../assets/background.svg') no-repeat 50%;
  background-size: 100%;
}

.user-layout-content {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32px 0;
}

.main {
  min-width: 260px;
  width: 368px;
}

/* Make user info page wider */
:deep(.UserInfo) .main {
  width: 600px;
  max-width: 90%;
}
</style> 