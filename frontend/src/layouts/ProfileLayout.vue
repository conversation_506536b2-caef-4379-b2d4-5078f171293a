<template>
  <div class="profile-layout">
    <div class="profile-content container">
      <el-tabs
        v-model="activeTab"
        class="profile-tabs"
        @tab-click="handleTabClick"
      >
        <el-tab-pane label="个人中心" name="profile" />
        <el-tab-pane label="订阅" name="subscription" />
        <el-tab-pane label="历史订阅" name="history" />
      </el-tabs>
      
      <div class="tab-content">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()
const activeTab = ref('profile')

// 处理标签页切换
const handleTabClick = (tab) => {
  const routes = {
    profile: '/profile/info',
    subscription: '/profile/subscription',
    history: '/profile/history'
  }
  router.push(routes[tab.props.name])
}

// 根据当前路由设置活动标签
const setActiveTabFromRoute = () => {
  const path = route.path
  if (path.includes('/profile/subscription')) {
    activeTab.value = 'subscription'
  } else if (path.includes('/profile/history')) {
    activeTab.value = 'history'
  } else {
    activeTab.value = 'profile'
  }
}

onMounted(() => {
  setActiveTabFromRoute()
})
</script>

<style scoped>
.profile-layout {
  min-height: 90vh;
  background-color: #f5f7fa;
  padding-top: 24px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.profile-tabs {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.tab-content {
  background-color: #fff;
  border-radius: 8px;
  min-height: 500px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  padding: 0 24px;
}
</style> 