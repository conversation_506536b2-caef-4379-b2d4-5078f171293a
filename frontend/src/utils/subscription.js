// Subscription plans utility functions

const SUBSCRIPTION_PLANS_KEY = 'subscription_plans'

/**
 * Store subscription plans in sessionStorage
 * @param {Array} plans - Array of subscription plan objects
 */
export function setSubscriptionPlans(plans) {
  sessionStorage.setItem(SUBSCRIPTION_PLANS_KEY, JSON.stringify(plans))
}

/**
 * Get subscription plans from sessionStorage
 * @returns {Array|null} Array of subscription plans or null if not found
 */
export function getSubscriptionPlans() {
  const plans = sessionStorage.getItem(SUBSCRIPTION_PLANS_KEY)
  return plans ? JSON.parse(plans) : null
}

/**
 * Get or fetch subscription plans
 * @param {Function} fetchFunction - Function to fetch plans from API
 * @returns {Promise<Array>} Array of subscription plans
 */
export async function getOrFetchSubscriptionPlans(fetchFunction) {
  // Try to get plans from sessionStorage first
  const cachedPlans = getSubscriptionPlans()
  
  if (cachedPlans) {
    return cachedPlans
  }

  // If no cached plans, fetch from API
  try {
    const plans = await fetchFunction()
    // Cache the fetched plans
    setSubscriptionPlans(plans)
    return plans
  } catch (error) {
    console.error('Error fetching subscription plans:', error)
    throw error
  }
}

/**
 * Clear subscription plans from sessionStorage
 */
export function clearSubscriptionPlans() {
  sessionStorage.removeItem(SUBSCRIPTION_PLANS_KEY)
} 