import { checkLicenseStatusAPI } from '@/apis/system'
import { getUserInfo } from '@/utils/auth'

const LICENSE_CACHE_KEY = 'license_status'
const LICENSE_CACHE_DURATION = 1000 * 60 * 60  // 1 hour

// 添加检查许可证状态的工具函数
export const checkLicenseStatus = async () => {
    try {
      const cachedLicense = localStorage.getItem(LICENSE_CACHE_KEY)
      if (cachedLicense) {
        const { status, timestamp } = JSON.parse(cachedLicense)
        if (Date.now() - timestamp < LICENSE_CACHE_DURATION) {
          return status
        }
      }
  
      const response = await checkLicenseStatusAPI()
      const status = response.data.status
      
      localStorage.setItem(LICENSE_CACHE_KEY, JSON.stringify({
        status,
        timestamp: Date.now()
      }))
  
      return status
    } catch (error) {
      console.error('License check failed:', error)
      return false
    }
  }

// 在组件中使用示例
// const handleAction = async () => {
//   const hasLicense = await checkLicenseStatus()
//   if (!hasLicense) {
//     // 显示提示或跳转到许可证页面
//     router.push('/license')
//     return
//   }
//   // 继续执行需要许可证的操作
// // }

export const checkIsMember = async () => {
    const userInfo = getUserInfo()
    
    // If userInfo is null or undefined, return false
    if (!userInfo) {
        return false
    }

    // If user is not a member, return false
    if (!userInfo.isMember) {
        return false
    }

    // Check if membership has expired
    const expiredTime = new Date(userInfo.memberExpiredTime)
    const now = new Date()

    // Return true if membership hasn't expired, false otherwise
    return now <= expiredTime && userInfo.isMember
}

