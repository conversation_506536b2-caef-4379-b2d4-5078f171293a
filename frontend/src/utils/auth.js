// Authentication utility functions

// Keys for storing the authentication data
const USER_INFO_KEY = 'user_info'

// Check if user is logged in
export function isLoggedIn() {
  return !!sessionStorage.getItem(USER_INFO_KEY)
}

// Store user info in sessionStorage
export function setUserInfo(userInfo) {
  sessionStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
}

// Get user info from sessionStorage
export function getUserInfo() {
  const userInfo = sessionStorage.getItem(USER_INFO_KEY)
  return userInfo ? JSON.parse(userInfo) : null
}

// Remove user info from sessionStorage
export function removeUserInfo() {
  sessionStorage.removeItem(USER_INFO_KEY)
}

// Clear all auth related data
export function clearAuthData() {
  removeUserInfo()
} 

export const setUserInfoSession = async (func) => {
  const response = await func()
  if (response.code === 200 && response.data) {
    const formattedUserInfo = {
        email: response.data.email || '',
        inviteCode: response.data.invite_code || '',
        createTime: response.data.create_time,
        isMember: !!response.data.is_member,
        memberCreatedTime: response.data.member_created_time,
        memberExpiredTime: response.data.member_expired_time,
        hasUsedTrial: response.data.has_used_trial,
        isPayUser: response.data.is_pay_user || false
      }
      // 将用户信息存入缓存
      setUserInfo(formattedUserInfo)
  }
}