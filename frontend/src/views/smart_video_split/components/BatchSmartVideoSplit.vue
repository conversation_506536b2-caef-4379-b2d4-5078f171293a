<template>
    <div class="batch-video-split">
      <div class="layout-container">
        <!-- 左侧配置面板 -->
        <div class="config-panel">
          <el-form label-position="left">
            <el-form-item label="输入目录">
              <div class="folder-select">
                <el-tooltip :content="inputFolder ? inputFolder : '选择包含视频的文件夹'" placement="top" effect="light">
                <el-input
                  v-model="inputFolder"
                  placeholder="选择包含视频的文件夹"
                >
                  <template #append>
                    <el-button @click="selectInputFolder">
                      <el-icon><Folder /></el-icon>
                      选择
                    </el-button>
                  </template>
                </el-input>
                </el-tooltip>
              </div>
            </el-form-item>
  
            <el-form-item label="输出目录">
              <div class="folder-select">
                <el-tooltip :content="outputFolder ? outputFolder : '选择结果保存位置, 默认在输入目录下创建result文件夹'" placement="bottom" effect="light">
                <el-input
                  v-model="outputFolder"
                  placeholder="选择结果保存位置, 默认在输入目录下创建result文件夹"
                >
                  <template #append>
                    <el-button @click="selectOutputFolder">
                      <el-icon><Folder /></el-icon>
                      选择
                    </el-button>
                  </template>
                </el-input>
                </el-tooltip>
              </div>
            </el-form-item>
  
            <el-form-item>
              <template #label>
              <div class="label-with-icon">
                <span>分割阈值</span>
                <el-tooltip
                  placement="top"
                  effect="light"
                >
                  <template #content>
                    <div class="shortcut-help">
                      <li>对于快速剪辑、动作频繁的视频：您可能需要适当提高threshold值，以避免因镜头内快速运动或轻微的光线变化而被错误地分割成过多的短场景</li>
                      <li>对于节奏平缓、镜头变化细微的视频：您可能需要适当降低threshold值，以捕捉到那些不太明显的场景过渡</li>
                    </div>
                  </template>
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
              <el-slider
                v-model="threshold"
                :min="10"
                :max="100"
                :step="1"
                show-input
              />
            </el-form-item>
  
            <div class="action-buttons">
              <StartProcessButton
                text="开始分割"
                :loading="processing"
                :disabled="!canStart || processing"
                @startProcess="startProcessing"
              />
              <el-button
                v-if="processing"
                @click="pauseProcessing"
                :type="isPaused ? 'success' : 'warning'"
              >
                <el-icon><VideoPause v-if="!isPaused"/><VideoPlay v-else/></el-icon>
                {{ isPaused ? '继续处理' : '暂停处理' }}
              </el-button>
              <el-button
                v-if="processing"
                type="danger"
                @click="stopProcessing"
              >
                <el-icon><CircleClose /></el-icon>
                终止处理
              </el-button>
            </div>
          </el-form>
        </div>
  
        <!-- 右侧进度和结果面板 -->
        <div class="result-panel" v-if="fileList.length > 0">
          <el-card class="progress-card">
            <template #header>
              <div class="progress-header">
                <div class="progress-info">
                  <div class="progress-title">
                    <div class="left">
                      <span class="title">处理进度</span>
                      <el-tag size="small" :type="getProgressTagType">
                        {{ processedCount }}/{{ fileList.length }}
                      </el-tag>
                    </div>
                  </div>
                  <el-progress
                    :percentage="totalProgress"
                    :status="processStatus"
                    :stroke-width="8"
                    :format="progressFormat"
                    class="total-progress"
                  />
                </div>
              </div>
            </template>
  
            <div class="file-list">
              <el-scrollbar height="400px" class="file-list-content">
                <div
                  v-for="file in fileList"
                  :key="file.path"
                  class="file-item"
                  :class="{
                    'processing': file.processing,
                    'processed': file.processed,
                    'error': file.error
                  }"
                >
                  <div class="item-content">
                    <div class="item-info">
                      <span class="file-name" :title="file.name">{{ file.name }}</span>
                      <div class="status-icon">
                        <el-icon v-if="file.processed" @click="openResult(file)" class="success clickable"><FolderOpened /></el-icon>
                        <el-icon v-else-if="file.error" class="error"><CircleCloseFilled /></el-icon>
                        <el-icon v-else-if="file.processing" class="processing"><Loading /></el-icon>
                        <el-icon v-else class="waiting"><Clock /></el-icon>
                      </div>
                    </div>
                    <el-progress
                      v-if="file.processing || file.processed"
                      :percentage="file.progress"
                      :status="file.error ? 'exception' : file.processed ? 'success' : ''"
                    />
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, onMounted } from 'vue'
  import { ElLoading } from 'element-plus'
  import {
    Folder,
    VideoPlay,
    VideoPause,
    CircleClose,
    CircleCloseFilled,
    Clock,
    Loading,
    FolderOpened
  } from '@element-plus/icons-vue'
  import message from '@/utils/message'
  import baseAPI from '@/apis/base'
  import { smartVideoSplitAPI } from '@/apis/smart_video_split'
  import StartProcessButton from '@/components/StartProcessButton.vue'
  
  const inputFolder = ref('')
  const outputFolder = ref('')
  const fileList = ref([])
  const processing = ref(false)
  const isPaused = ref(false)
  const processedCount = ref(0)
  const threshold = ref(27.0)
  
  const canStart = computed(() => {
    return inputFolder.value && fileList.value.length > 0
  })
  
  const totalProgress = computed(() => {
    if (fileList.value.length === 0) return 0
    return Math.round((processedCount.value / fileList.value.length) * 100)
  })
  
  const processStatus = computed(() => {
    if (isPaused.value) return 'warning'
    if (processing.value) return ''
    return totalProgress.value === 100 ? 'success' : ''
  })
  
  const resetState = () => {
    processedCount.value = 0
    processing.value = false
    isPaused.value = false
    fileList.value = []
  }
  
  const selectInputFolder = async () => {
    const loadingInstance = ElLoading.service({
      lock: true,
      background: 'rgba(255, 255, 255, 0.9)',
      fullscreen: true
    })
  
    try {
      resetState()
      const response = await baseAPI('open_folder_dialog', '')
      if (response.code === 200 && response.data.folder_path) {
        inputFolder.value = response.data.folder_path
        const filesResponse = await smartVideoSplitAPI('get_folder_smart_video_split', response.data.folder_path)
        if (filesResponse.code === 200) {
          loadFileList(filesResponse.data.split_videos)
          message.success(`已加载 ${fileList.value.length} 个视频`)
        }
      }
    } catch (error) {
      console.error(error)
      message.error('获取视频列表失败')
    } finally {
      loadingInstance.close()
    }
  }
  
  const selectOutputFolder = async () => {
    try {
      const response = await baseAPI('open_folder_dialog', '')
      if (response.code === 200 && response.data.folder_path) {
        outputFolder.value = response.data.folder_path
      }
    } catch (error) {
      message.error('选择文件夹失败')
    }
  }
  
  const startProcessing = async () => {
    if (processing.value) return
    processing.value = true
    processedCount.value = 0
  
    for (const file of fileList.value) {
      if (isPaused.value) {
        await new Promise(resolve => {
          const checkPause = () => {
            if (!isPaused.value) resolve()
            else setTimeout(checkPause, 100)
          }
          checkPause()
        })
      }
  
      if (!processing.value) break
  
      try {
        file.processing = true
        file.progress = 50
  
        const params = {
          input_path: file.path,
          folder_path: inputFolder.value,
          threshold: threshold.value,
          save_folder: outputFolder.value
        }
  
        const response = await smartVideoSplitAPI('smart_video_split_from_folder', params)
  
        if (response.code === 200) {
          file.progress = 100
          file.processed = true
          file.resultPath = response.data.output_dir
          processedCount.value++
        } else {
          throw new Error(response.msg || '处理失败')
        }
      } catch (error) {
        file.error = true
        file.progress = 100
        message.error(`处理文件 ${file.name} 失败: ${error.message}`)
      } finally {
        file.processing = false
      }
    }
  
    processing.value = false
    isPaused.value = false
    message.success('批量处理完成')
  }
  
  const pauseProcessing = () => {
    isPaused.value = !isPaused.value
  }
  
  const stopProcessing = () => {
    processing.value = false
    isPaused.value = false
    message.warning('已终止处理')
  }
  
  const loadFileList = (files) => {
    fileList.value = files.map(file => ({
      name: file.video_name,
      path: file.video_path,
      processing: false,
      processed: false,
      error: false,
      progress: 0,
      resultPath: ''
    }))
  }
  
  const progressFormat = (percentage) => {
    if (percentage === 100) return '已完成'
    if (isPaused.value) return '已暂停'
    if (processing.value) return `${percentage}%`
    return '等待开始'
  }
  
  const getProgressTagType = computed(() => {
    if (totalProgress.value === 100) return 'success'
    if (isPaused.value) return 'warning'
    if (processing.value) return 'primary'
    return 'info'
  })
  
  const openResult = async (file) => {
    if (!file.resultPath) return
    try {
       await baseAPI('open_directory', file.resultPath)
    } catch (error) {
      message.error(error.message)
    }
  }
  
  onMounted(() => {
    resetState()
  })
  </script>
  
  <style lang="scss" scoped>
  .batch-video-split {
    height: 100%;
    .layout-container {
      height: 100%;
      display: flex;
      gap: 24px;
  
      .config-panel {
        flex: 0 0 400px;
  
        :deep(.el-form) {
          .el-form-item {
            margin-bottom: 16px;
  
            .el-form-item__label {
              font-weight: 500;
            }
          }
        }
  
        .folder-select {
          .el-input {
            :deep(.el-input-group__append) {
              .el-button {
                padding: 8px 16px;
                .el-icon {
                  margin-right: 4px;
                }
              }
            }
          }
        }
  
        .action-buttons {
          display: flex;
          gap: 12px;
          margin-top: 32px;
          padding-top: 24px;
          border-top: 1px solid var(--el-border-color-lighter);
        }
      }
  
      .result-panel {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
  
        .progress-card {
          height: 100%;
          display: flex;
          flex-direction: column;
  
          :deep(.el-card__body) {
            flex: 1;
            overflow: auto;
            padding: 0;
          }
        }
  
        .progress-header {
          padding: 12px 16px;
  
          .progress-info {
            .progress-title {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;
  
              .title {
                font-size: 14px;
                font-weight: 500;
              }
            }
          }
        }
  
        .file-list {
          padding: 16px;
  
          .file-list-content {
            border: 1px solid var(--el-border-color-lighter);
            border-radius: 4px;
  
            .file-item {
              display: flex;
              align-items: center;
              gap: 16px;
              padding: 12px 16px;
              border-bottom: 1px solid var(--el-border-color-lighter);
  
              &:last-child {
                border-bottom: none;
              }
  
              .item-content {
                flex: 1;
                .item-info {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
  
                  .file-name {
                    font-weight: 500;
                  }
  
                  .status-icon .el-icon {
                    margin-left: 8px;
                    &.clickable {
                      cursor: pointer;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .label-with-icon {
  display: flex;
  align-items: center;
  gap: 4px;

  .help-icon {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    cursor: help;
    
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
  </style>