<template>
    <div class="single-video-split" v-loading="loading" element-loading-text="处理中..." element-loading-lock>
      <div class="layout-container">
        <!-- 左侧配置面板 -->
        <div class="config-panel">
          <el-form label-position="left">
            <el-form-item label="选择视频">
              <div class="upload-content">
                <UploadButton text="选择文件" size="default" @upload="handleClickUpload" />
                <StartProcessButton
                  text="开始分割"
                  :loading="processing"
                  :disabled="!videoUrl || processing"
                  @startProcess="startProcess"
                />
                <el-button
                  v-if="videoUrl"
                  @click="resetVideo"
                >
                  <el-icon><Back /></el-icon>
                  重新选择
                </el-button>
              </div>
            </el-form-item>
            <el-form-item label="输出目录">
              <div class="folder-select">
                <el-tooltip :content="saveFolder ? saveFolder : '默认在输入目录下创建result文件夹'" placement="top" effect="light">
                <el-input
                  v-model="saveFolder"
                  placeholder="默认在输入目录下创建result文件夹"
                >
                  <template #append>
                    <el-button @click="selectSaveFolder">
                      <el-icon><Folder /></el-icon>
                      选择
                    </el-button>
                  </template>
                </el-input>
                </el-tooltip>
              </div>
            </el-form-item>
  
            <el-form-item>
              <template #label>
              <div class="label-with-icon">
                <span>分割阈值</span>
                <el-tooltip
                  placement="top"
                  effect="light"
                >
                  <template #content>
                    <div class="shortcut-help">
                      <li>对于快速剪辑、动作频繁的视频：您可能需要适当提高threshold值，以避免因镜头内快速运动或轻微的光线变化而被错误地分割成过多的短场景</li>
                      <li>对于节奏平缓、镜头变化细微的视频：您可能需要适当降低threshold值，以捕捉到那些不太明显的场景过渡</li>
                    </div>
                  </template>
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
              <el-slider
                v-model="threshold"
                :min="10"
                :max="100"
                :step="1"
                show-input
              />
            </el-form-item>
          </el-form>
        </div>
  
        <!-- 右侧预览面板 -->
        <div class="preview-panel" v-if="videoUrl">
          <el-card class="preview-card">
            <template #header>
              <div class="preview-header">
                <span>{{ outputDir ? '分割结果' : '视频预览' }}</span>
              </div>
            </template>
  
            <div class="preview-content">
              <div v-if="!outputDir" class="single-preview">
                <div class="preview-video">
                  <video :src="videoUrl" controls style="max-width: 100%; max-height: 400px;"></video>
                </div>
                <div class="video-info">
                  <div class="info-item">
                    <span class="label">路径:</span>
                    <span class="value">{{ videoPath }}</span>
                  </div>
                </div>
              </div>
  
              <div v-else class="result-preview">
                <div class="video-info">
                  <div class="info-item">
                    <span class="label">保存路径:</span>
                    <span class="value path">{{ outputDir }}</span>
                    <el-button size="small" @click="openResultFolder" class="open-btn">
                      <el-icon><FolderOpened /></el-icon>
                      打开
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </template>
  
<script setup>
import { ref } from 'vue'
import {
Back,
FolderOpened
} from '@element-plus/icons-vue'
import message from '@/utils/message'
import baseAPI from '@/apis/base'
import { smartVideoSplitAPI } from '@/apis/smart_video_split'
import UploadButton from '@/components/UploadButton.vue'
import StartProcessButton from '@/components/StartProcessButton.vue'

const videoUrl = ref('')
const videoPath = ref('')
const outputDir = ref('')
const processing = ref(false)
const loading = ref(false)
const threshold = ref(27.0)
const videoObjectUrl = ref('')
const saveFolder = ref('')

const loadVideo = async (url) => {
try {
loading.value = true
const response = await fetch(url)
const blob = await response.blob()
if (videoObjectUrl.value) {
    URL.revokeObjectURL(videoObjectUrl.value)
}
videoObjectUrl.value = URL.createObjectURL(blob)
return true
} catch (error) {
console.error('Error loading video:', error)
return false
} finally {
loading.value = false
}
}

const handleClickUpload = async () => {
loading.value = true
try {
    const response = await baseAPI('open_video_file', false)
    if (response.code === 200 && response.data.selected_files.length > 0) {
    resetVideo()
    videoPath.value = response.data.selected_files[0]
    const urlResponse = await baseAPI('get_video_url', videoPath.value)
    if (urlResponse.code === 200 && urlResponse.data.video_url) {
        const success = await loadVideo(urlResponse.data.video_url)
        if (success) {
        videoUrl.value = videoObjectUrl.value
        } else {
        message.error('视频加载失败')
        }
    } else {
        message.error('获取视频数据失败')
    }

    message.success('选择成功')
    }
} catch (error) {
    message.error('选择失败')
    console.error('Error selecting file:', error)
} finally {
    loading.value = false
}
}

const selectSaveFolder = async () => {
    try {
      const response = await baseAPI('open_folder_dialog', '')
      if (response.code === 200 && response.data.folder_path) {
        saveFolder.value = response.data.folder_path
      }
    } catch (error) {
      message.error('选择文件夹失败')
    }
  }

const startProcess = async () => {
if (!videoPath.value) {
    message.warning('请先选择视频')
    return
}

processing.value = true
outputDir.value = ''
try {
    const params = {
    input_path: videoPath.value,
    save_folder: saveFolder.value,
    threshold: threshold.value
    }

    const response = await smartVideoSplitAPI('smart_video_split', params)
    if (response.code === 200) {
    message.success('分割成功')
    outputDir.value = response.data.output_dir
    } else {
    throw new Error(response.msg || '分割失败')
    }
} catch (error) {
    message.error(`分割失败: ${error.message}`)
    console.error('Error splitting video:', error)
} finally {
    processing.value = false
}
}

const resetVideo = () => {
videoUrl.value = ''
videoPath.value = ''
outputDir.value = ''
}

const openResultFolder = async () => {
if (!outputDir.value) return
try {
    await baseAPI('open_directory', outputDir.value)
} catch (error) {
    message.error('打开文件夹失败')
}
}

</script>
  
<style lang="scss" scoped>
.single-video-split {
height: 100%;
.layout-container {
    height: 100%;
    display: flex;
    gap: 24px;

    .config-panel {
    flex: 0 0 400px;
    margin-right: 10px;

    :deep(.el-form) {
        .el-form-item {
        margin-bottom: 16px;

        .el-form-item__label {
            font-weight: 500;
        }
        }
    }

    .upload-content {
        display: flex;
        gap: 8px;
    }
    }

    .preview-panel {
    flex: 1;

    .preview-card {
        height: auto;

        :deep(.el-card__header) {
        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 32px;
        }
        }

        .preview-content {
        .single-preview, .result-preview {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .preview-video {
            max-width: 100%;
            max-height: 400px;
            display: flex;
            justify-content: center;
            align-items: center;

            video {
                max-width: 100%;
                max-height: 400px;
                object-fit: contain;
            }
            }

            .video-info {
            margin-top: 16px;
            padding: 16px;
            background-color: var(--el-fill-color-light);
            border-radius: 8px;

            .info-item {
                display: flex;
                margin-bottom: 8px;
                align-items: center;

                &:last-child {
                margin-bottom: 0;
                }

                .label {
                font-weight: 500;
                color: var(--el-text-color-regular);
                width: 80px;
                }

                .value {
                color: var(--el-text-color-primary);

                &.path {
                    max-width: 300px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    margin-right: 8px;
                }
                }

                .open-btn {
                padding: 4px 8px;
                font-size: 12px;

                .el-icon {
                    margin-right: 4px;
                }
                }
            }
            }
        }
        }
    }
    }
}

:deep(.el-loading-mask) {
    background-color: rgba(255, 255, 255, 0.9);
}
}

.label-with-icon {
  display: flex;
  align-items: center;
  gap: 4px;

  .help-icon {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    cursor: help;
    
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>
  