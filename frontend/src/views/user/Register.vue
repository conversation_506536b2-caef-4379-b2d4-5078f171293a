<template>
  <div class="register-container">
    <el-card class="register-card">      
      <el-form 
        ref="formRef" 
        :model="registerForm" 
        :rules="rules" 
        label-position="top" 
        @keyup.enter="handleRegister"
      >
        <el-form-item prop="email" label="邮箱">
          <el-input
            v-model="registerForm.email"
            placeholder="请输入邮箱地址"
            prefix-icon="Message"
            autocomplete="email"
          />
        </el-form-item>
        
        <el-form-item prop="password" label="密码">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            autocomplete="new-password"
          />
        </el-form-item>
        
        <el-form-item prop="confirmPassword" label="确认密码">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            prefix-icon="Lock"
            show-password
            autocomplete="new-password"
          />
        </el-form-item>
        <!-- 邀请码 -->
        <el-form-item prop="inviteCode" label="邀请码">
          <el-input
            v-model="registerForm.inviteCode"
            placeholder="请输入邀请码"
            prefix-icon="User"
          />
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="agreeTerms" @change="validateTerms">
            我已阅读并同意<el-button link @click="showTerms">用户协议</el-button>和<el-button link @click="showPrivacy">隐私政策</el-button>
          </el-checkbox>
        </el-form-item>
        
        <el-button
          type="primary"
          :loading="loading"
          class="register-button"
          :disabled="!agreeTerms"
          @click="handleRegister"
        >
          注册
        </el-button>
        
        <div class="login-link">
          <span>已有账号？</span>
          <router-link to="/user/login">立即登录</router-link>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { registerAPI } from '@/apis/system'
import message from '@/utils/message'
import { ElMessageBox } from 'element-plus'

const router = useRouter()
const formRef = ref(null)
const loading = ref(false)
const agreeTerms = ref(false)
const termsError = ref('')

const registerForm = reactive({
  email: '',
  password: '',
  confirmPassword: ''
  
})

const validateEmail = (rule, value, callback) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!value) {
    callback(new Error('请输入邮箱地址'))
  } else if (!emailRegex.test(value)) {
    callback(new Error('请输入有效的邮箱地址'))
  } else {
    callback()
  }
}

const validatePassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入密码'))
  } else if (value.length < 6) {
    callback(new Error('密码长度不能少于6个字符'))
  } else {
    // 如果确认密码已填写，则同时验证确认密码
    if (registerForm.confirmPassword) {
      formRef.value.validateField('confirmPassword')
    }
    callback()
  }
}

const validateConfirmPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const validateTerms = () => {
  if (!agreeTerms.value) {
    termsError.value = '您必须同意用户协议和隐私政策'
    return false
  }
  termsError.value = ''
  return true
}

const rules = reactive({
  email: [
    { required: true, validator: validateEmail, trigger: 'blur' }
  ],
  password: [
    { required: true, validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ]
})

const showTerms = () => {
  ElMessageBox.alert(
    `<div style="max-height: 400px; overflow-y: auto; text-align: left;">
      <h2 style="text-align: center;">用户协议</h2>
      <p><strong>欢迎使用我们的软件</strong></p>
      
      <p>本用户协议（以下简称"协议"）是您与本软件开发者（以下简称"我们"）之间关于使用本AI抠图软件及相关服务的法律协议。请您在使用本软件前，仔细阅读并充分理解本协议的全部内容。</p>
      
      <h3>1. 接受条款</h3>
      <p>通过点击"同意"或使用本软件，即表示您已阅读并同意受本协议条款的约束。如您不同意本协议中的任何条款，请勿注册或使用本软件。</p>
      
      <h3>2. 软件功能与服务</h3>
      <p>本软件是一款基于人工智能技术的图像处理工具，主要提供图像抠图、背景移除等功能。我们可能会不断更新和优化软件功能，具体服务内容以软件实际提供的为准。</p>
      
      <h3>3. 用户责任</h3>
      <p>3.1 您应当遵守中华人民共和国相关法律法规及相关国际公约。</p>
      <p>3.2 您应当对使用本软件处理的图像拥有合法权利，或已获得合法授权。</p>
      <p>3.3 您不得利用本软件制作、发布、传播违法违规内容。</p>
      <p>3.4 您应当妥善保管账号信息，因您个人原因导致的账号被盗或滥用，我们不承担责任。</p>
      
      <h3>4. 免责声明</h3>
      <p>4.1 本软件基于现有技术提供服务，我们不对软件的精确性、实用性、可靠性做任何承诺。</p>
      <p>4.2 由于人工智能技术的局限性，软件可能无法满足您的所有需求或期望，或在处理特定图像时出现不理想结果，此类情况不构成我们的违约。</p>
      <p>4.3 我们无法控制您通过本软件处理的图像内容及其用途，您应当对您处理的内容及使用行为负全部责任。</p>
      <p>4.4 因网络、设备故障或其他不可抗力因素导致的服务中断或数据丢失，我们不承担责任。</p>
      
      <h3>5. 知识产权</h3>
      <p>5.1 本软件的所有权、知识产权及其他相关权利均归我们所有。</p>
      <p>5.2 您通过本软件处理的图像，其知识产权归您或原权利人所有。</p>
      
      <h3>6. 隐私保护</h3>
      <p>我们重视您的隐私保护，详情请参阅《隐私政策》。</p>
      
      <h3>7. 协议修改</h3>
      <p>我们有权随时修改本协议，修改后的协议将在软件内公布。如您继续使用本软件，即视为您接受修改后的协议。</p>
      
      <h3>8. 法律适用</h3>
      <p>本协议的订立、执行和解释及争议的解决均适用中华人民共和国法律。</p>
      
      <h3>9. 联系方式</h3>
      <p>如您对本协议或软件使用有任何问题，请通过软件内提供的联系方式与我们联系。</p>
    </div>`,
    '用户协议',
    {
      confirmButtonText: '我已阅读',
      type: 'info',
      dangerouslyUseHTMLString: true,
    }
  )
}

const showPrivacy = () => {
  ElMessageBox.alert(
    `<div style="max-height: 400px; overflow-y: auto; text-align: left;">
      <h2 style="text-align: center;">隐私政策</h2>
      <p><strong>欢迎使用我们的软件</strong></p>
      
      <p>本隐私政策旨在向您说明我们如何收集、使用、存储和保护您的个人信息及上传的图像数据。请您在使用本软件前，仔细阅读并理解本政策的全部内容。</p>
      
      <h3>1. 信息收集</h3>
      <p>1.1 <strong>个人信息</strong>：当您注册账号时，我们会收集您的邮箱地址和密码。</p>
      <p>1.2 <strong>图像数据</strong>：不会收集你处理的图像数据</p>
    
      
      <h3>2. 信息保护</h3>
      <p>2.1 我们采取合理的安全措施保护您的信息不被未经授权访问、使用或泄露。</p>
      <p>2.2 我们不会将您的个人信息出售、出租或以其他方式共享给任何第三方机构，除非：</p>
      <p>- 获得您的明确授权</p>
      <p>- 法律法规要求</p>
      <p>- 保护我们的合法权益</p>
      
      <h3>3. 数据存储</h3>
      <p>我们可能在中华人民共和国境内的服务器上存储您的信息，同时采取一切合理措施确保数据安全。</p>
      
      <h3>4. 第三方服务</h3>
      <p>本软件可能包含第三方服务（如支付服务等），这些第三方可能有自己的隐私政策。我们建议您了解这些第三方的隐私政策。</p>
      
      <h3>5. 政策更新</h3>
      <p>我们可能会不时更新本隐私政策，更新后的政策将在软件内公布。建议您定期查阅本政策以了解最新变化。</p>
      
      <h3>6. 联系我们</h3>
      <p>如您对本隐私政策有任何疑问或建议，请通过软件内提供的联系方式与我们联系。</p>
    </div>`,
    '隐私政策',
    {
      confirmButtonText: '我已阅读',
      type: 'info',
      dangerouslyUseHTMLString: true,
    }
  )
}

const handleRegister = async () => {
  if (loading.value) return
  if (!validateTerms()) {
    message.warning(termsError.value)
    return
  }
  
  try {
    // Validate form
    await formRef.value.validate()
    
    // Set loading state
    loading.value = true
    
    // Call register API
    const response = await registerAPI(registerForm.email, registerForm.password, registerForm.inviteCode)
    
    if (response.code === 200) {
      // Show success message
      message.success('注册成功，请登录')
      
      // Redirect to login page
      router.push('/user/login')
    } else {
      message.error(response.msg || '注册失败，请稍后再试')
    }
  } catch (error) {
    console.error('Register error:', error)
    message.error(error.message || '注册时发生错误')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.register-card {
  width: 100%;
  border-radius: 8px;
}

.register-button {
  width: 100%;
  margin-top: 16px;
  padding: 12px 0;
  font-size: 16px;
}

.login-link {
  margin-top: 16px;
  text-align: center;
  font-size: 14px;
  color: #606266;
}

.login-link a {
  color: #409eff;
  margin-left: 4px;
  text-decoration: none;
}

.login-link a:hover {
  text-decoration: underline;
}
</style>
