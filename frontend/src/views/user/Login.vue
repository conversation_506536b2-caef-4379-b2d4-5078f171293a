<template>
  <div class="login-container">
    <el-card class="login-card">
      <el-form 
        ref="formRef" 
        :model="loginForm" 
        :rules="rules" 
        label-position="top" 
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="email" label="邮箱">
          <el-input
            v-model="loginForm.email"
            placeholder="请输入邮箱地址"
            prefix-icon="Message"
            autocomplete="email"
          />
        </el-form-item>
        
        <el-form-item prop="password" label="密码">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            autocomplete="current-password"
          />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="rememberPassword">记住密码</el-checkbox>
        </el-form-item>

        <el-button
          type="primary"
          :loading="loading"
          class="login-button"
          @click="handleLogin"
        >
          登录
        </el-button>
        
        <div class="register-link">
          <span>还没有账号？</span>
          <router-link to="/user/register">立即注册</router-link>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { loginAPI } from '@/apis/system'
import { setUserInfoSession } from '@/utils/auth'
import message from '@/utils/message'
import { getUserInfoAPI } from '@/apis/system'

const router = useRouter()
const formRef = ref(null)
const loading = ref(false)
const rememberPassword = ref(false)

const loginForm = reactive({
  email: '',
  password: ''
})

// Load saved credentials on mount
onMounted(() => {
  const savedEmail = localStorage.getItem('userEmail')
  const savedPassword = localStorage.getItem('userPassword')
  if (savedEmail && savedPassword) {
    loginForm.email = savedEmail
    loginForm.password = savedPassword
    rememberPassword.value = true
  }
})

const validateEmail = (rule, value, callback) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!value) {
    callback(new Error('请输入邮箱地址'))
  } else if (!emailRegex.test(value)) {
    callback(new Error('请输入有效的邮箱地址'))
  } else {
    callback()
  }
}

const rules = reactive({
  email: [
    { required: true, validator: validateEmail, trigger: ['blur', 'submit'] }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: ['blur', 'submit'] },
    { min: 6, message: '密码长度不能少于6个字符', trigger: ['blur', 'submit'] }
  ]
})


const handleLogin = async () => {
  if (loading.value) return
  
  try {
    // Validate form
    await formRef.value.validate((valid, fields) => {
      if (!valid) {
        const errors = []
        for (const field of Object.values(fields)) {
          errors.push(field[0].message)
        }
        throw new Error(errors.join('\n'))
      }
    })
    
    // Set loading state
    loading.value = true
    
    // Call login API
    const response = await loginAPI(loginForm.email, loginForm.password)
    
    if (response.code === 200) {
      // Store token
      await setUserInfoSession(getUserInfoAPI)
      
      // Save credentials if remember password is checked
      if (rememberPassword.value) {
        localStorage.setItem('userEmail', loginForm.email)
        localStorage.setItem('userPassword', loginForm.password)
      } else {
        localStorage.removeItem('userEmail')
        localStorage.removeItem('userPassword')
      }
      
      // Show success message
      message.success('登录成功')
      
      // Redirect to user info page
      router.push('/')
    } else {
      message.error(response.msg || '登录失败，请检查邮箱和密码')
    }
  } catch (error) {
    console.error('Login error:', error)
    message.error(error || '登录时发生错误')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.login-card {
  width: 100%;
  border-radius: 8px;
}

.login-button {
  width: 100%;
  margin-top: 16px;
  padding: 12px 0;
  font-size: 16px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.register-link {
  margin-top: 16px;
  text-align: center;
  font-size: 14px;
  color: #606266;
}

.register-link a {
  color: #409eff;
  margin-left: 4px;
  text-decoration: none;
}

.register-link a:hover {
  text-decoration: underline;
}
</style>
