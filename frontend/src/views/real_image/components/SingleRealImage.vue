<template>
  <div class="single-real-image-container" @dragover.prevent @drop="handleDrop">
    <!-- 左侧配置项，右侧预览项 -->
    <div class="layout-container">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <el-form label-position="left">
          <el-form-item label="选择图片">
            <div class="upload-content">
              <UploadButton text="选择文件" size="default" @upload="handleClickUpload" />
              <StartProcessButton text="开始处理" :loading="processing" :disabled="!originalImage || processing"
                @startProcess="startProcess" />

              <el-button 
                v-if="originalImage" 
                @click="resetImage"
              >
                <el-icon><Back /></el-icon>
                重新选择
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="处理模型">
            <el-radio-group v-model="modelType">
              <el-radio value="gereral">通用</el-radio>
              <el-radio value="anime">动漫</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="放大倍数">
            <el-radio-group v-model="scale">
              <el-radio value="1">1倍</el-radio>
              <el-radio value="2">2倍</el-radio>
              <el-radio value="3">3倍</el-radio>
              <el-radio value="4">4倍</el-radio>
            </el-radio-group>
          </el-form-item>

           <!-- 添加示例区域 -->
           <el-form-item v-if="!originalImage">
            <div class="demo-section">
              <div class="demo-title">
                可拖放、粘贴图片、粘贴链接，支持 jpg/png/gif/webp/bmp, 点击图片快速体验
              </div>
              <div class="demo-images">
                <div 
                  v-for="(base64, key) in imageList" 
                  :key="key"
                  class="demo-image-item"
                  @click="tryDemo(base64)"
                >
                  <img :src="base64" :alt="key">
                </div>
              </div>
            </div>
          </el-form-item>

        </el-form>

      </div>
      <!-- 右侧预览面板 -->
      <div class="preview-panel" v-if="originalImage">
        <el-card class="preview-card">
          <template #header>
            <div class="preview-header">
              <span>{{ resultUrl ? '效果对比' : '图片预览' }}</span>
              <div class="operation-buttons" v-if="resultUrl">
                <el-button-group>
                  <el-tooltip content="复制到剪贴板">
                    <el-button @click="copyToClipboard">
                      <el-icon><Document /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="导出图片">
                    <el-button @click="exportImage">
                      <el-icon><Download /></el-icon>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
              </div>
            </div>
          </template>
          <div class="preview-content">
            <div v-if="!resultUrl" class="single-preview">
              <div class="preview-image">
                <el-image :src="originalImage" fit="contain" :preview-src-list="[originalImage]" />
              </div>
            </div>

            <div v-else class="compare-preview">
              <VueCompareImage :leftImage="originalImage" :rightImage="resultUrl" :leftLabel="'原图'"
                :rightLabel="'处理结果'" :handleSize="40" class="img-container" />
            </div>
          </div>
        </el-card>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import message from '@/utils/message'
import { getImgBase64API } from '@/apis/common'
import UploadButton from '@/components/UploadButton.vue'
import baseAPI from '@/apis/base'
import { realImgAPI } from '@/apis/real_img'
import StartProcessButton from '@/components/StartProcessButton.vue'
import { VueCompareImage } from 'vue3-compare-image'
import imageList from '@/utils/demo2'
import { base64ToBlob } from '@/utils/helper'

const modelType = ref('gereral')
const scale = ref('4')
const processing = ref(false)

const originalImage = ref(null)
const loading = ref(false)
const resultUrl = ref(null)

const resetImage = () => {
  originalImage.value = null
  loading.value = false
  resultUrl.value = null
}

const startProcess = () => {
  processing.value = true
  realImgAPI("single_real_image", {
    input_path: originalImage.value,
    model_type: modelType.value,
    scale: scale.value
  }).then(res => {
    if (res.code === 200) {
      message.success('处理成功')
      resultUrl.value = res.data.result
    } else {
      message.error(res.msg || '处理失败')
    }
  }).finally(() => {
    processing.value = false
  })
}

const tryDemo = (base64) => {
  resetImage()
  originalImage.value = base64
}

// 文件上传相关方法
const handleClickUpload = async () => {
  loading.value = true
  try {
    const response = await baseAPI('open_img_file', false)
    if (response.code === 200 && response.data.selected_file) {
      originalImage.value = response.data.image_url
      message.success('上传成功')
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}

// 处理拖拽事件
const handleDragOver = (event) => {
  event.preventDefault();
};
const handleDrop = async (e) => {
  e.preventDefault();

  const file = e.dataTransfer?.files[0]
  if (!file) return

  // 检查文件类型支持jpg png webp gif bmp tiff
  const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/jpg', 'image/bmp']
  if (!validTypes.includes(file.type)) {
    message.error('只支持 JPG、PNG、WebP、GIF、BMP 格式的图片')
    return
  }

  loading.value = true
  try {
    resetImage()
    if (file.type.match('image.*')) {
      const reader = new FileReader();
      reader.onload = function (e) {
        originalImage.value = e.target.result
        message.success('上传成功')
      };
      reader.readAsDataURL(file);
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}


const exportImage = async () => {
  const response = await realImgAPI('save_image_dialog', {
    base64_data: resultUrl.value,
    origin_image: originalImage.value,

  })
  if (response.code === 200) {
    message.success('导出成功')
  } else {
    message.error('导出失败')
  }
}


// 奇怪的bug，在复制事件里面，动态获取的数据不能写入粘贴板
const copyToClipboard = async () => {
  try {
    const blob = base64ToBlob(resultUrl.value);
    const item = new ClipboardItem({ 'image/png': blob });
    await navigator.clipboard.write([item]);
    message.success('已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    message.error(`复制失败: ${error.message || '未知错误'}`);
  }
}



// 验证是否是有效的URL
const isValidUrl = (string) => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};

// 处理粘贴事件
const handlePaste = (event) => {
  event.preventDefault();
  const clipboardData = event.clipboardData || window.Clipboard;
  const items = clipboardData.items;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    if (item.kind === 'string') {
      loading.value = true
      item.getAsString((text) => {
        // 判断是不是base64
        if (text.startsWith('data:image/')) {
          console.log('粘贴的是base64:', text);
          originalImage.value = text
          loading.value = false
        } else if (isValidUrl(text)) {
          console.log('粘贴的是URL:', text);
          getImgBase64API(text).then(res => {
            if (res.code === 200) {
              originalImage.value = res.data.base64_image
            } else {
              const errorMsg = res.msg || '获取图片失败';
              message.error(`获取图片失败: ${errorMsg}`);
              console.error('获取图片失败:', res);
            }
          }).catch(error => {
            message.error(`获取图片失败: ${error.message || '未知错误'}`);
            console.error('API调用失败:', error);
          }).finally(() => {
            loading.value = false
          });
        } else {
          loading.value = false
        }
      });
    } else if (item.kind === 'file') {
      const type = item.type.split('/')[0];
      if (!['image'].includes(type)) {
        message.error('请粘贴图片文件');
        return;
      }
      loading.value = true
      const file = item.getAsFile();
      const reader = new FileReader();
      reader.onload = (e) => {
        console.log('粘贴的是图片:', e.target.result);
        originalImage.value = e.target.result
        loading.value = false
      };
      reader.readAsDataURL(file);
    }
  }
};

onMounted(() => {
  window.addEventListener('paste', handlePaste)
  window.addEventListener('dragover', handleDragOver)
  window.addEventListener('drop', handleDrop)
})

onUnmounted(() => {
  window.removeEventListener('paste', handlePaste)
  window.removeEventListener('dragover', handleDragOver)
  window.removeEventListener('drop', handleDrop)
})

</script>

<style lang="scss" scoped>
.single-real-image-container {
  height: 100%;  
  .layout-container {
    height: 100%;
    display: flex;
    gap: 24px;

    // 左侧配置面板
    .config-panel {
      flex: 0 0 400px;
      margin-right: 10px;

      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 10px;

          .el-form-item__label {
            font-weight: 500;
          }
        }
      }
      .upload-content {
        display: flex;
        gap: 8px;
      }
    }

    // 右侧预览面板
    .preview-panel {
      flex: 1;

      .preview-card {
        height: auto;

        :deep(.el-card__header) {
          .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 32px;
          }
        }

        .preview-content {          
          .single-preview {
            .preview-image {
              max-width: 100%;
              max-height: 59vh;
              display: flex;
              justify-content: center;
              align-items: center;
              
              :deep(.el-image) {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                
                img {
                  max-width: 100%;
                  max-height: 59vh;
                  object-fit: contain;
                }
              }
            }
          }
          
          .compare-preview {
            .img-container {
              max-width: 100%;
              max-height: 59vh;
              display: flex;
              justify-content: center;
              align-items: center;
              
              :deep(img) {
                max-width: 100%;
                max-height: 100%;
                object-fit: contain;
              }
            }
          }
        }
      }
    }
  }
}


.demo-section {
  width: 100%;
  margin-top: 16px;
  
  .demo-title {
    color: var(--el-text-color-secondary);
    font-size: 14px;
    margin-bottom: 20px;
    text-align: left;
  }
  
  .demo-images {
    display: grid;
    grid-template-columns: repeat(3, 100px); // 固定宽度的三列布局
    gap: 20px;
    
    .demo-image-item {
      width: 100px;
      height: 100px;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid var(--el-border-color-lighter);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.1);
        border-color: var(--el-color-primary-light-5);
      }
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
      
      &:hover img {
        transform: scale(1.05);
      }
    }
  }
}

</style>