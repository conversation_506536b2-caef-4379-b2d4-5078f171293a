<template>
  <div class="license-container">
    <el-card class="license-card" v-loading="loading">
      <template #header>
        <div class="card-header">
          <h2>软件授权</h2>
        </div>
      </template>
      
    <div v-show="!loading && licenseStatus && licenseStatus.status" class="auth-success-section">
        <el-result
          icon="success"
          title="授权成功"
          :sub-title="licenseStatus?.message || ''"
        >
          <template #extra>
            <el-button type="primary" @click="router.push('/')">返回首页</el-button>
          </template>
        </el-result>
      </div>
      
      <div v-show="!loading && (!licenseStatus || !licenseStatus.status)">
        
        <div class="contact-info-section">
          <div class="contact-item">
            <span class="contact-label">官方网站：</span>
            <a href="https://lingxiangtools.top/" target="_blank" class="contact-value">https://lingxiangtools.top/</a>
          </div>
          <div class="contact-item">
            <span class="contact-label">联系邮箱：</span>
            <span class="contact-value copyable" @click="copyText('<EMAIL>')"><EMAIL></span>
            <el-icon class="copy-icon" @click="copyText('<EMAIL>')" v-if="!copied.email"><DocumentCopy /></el-icon>
            <el-icon class="copy-icon copied" v-else><Check /></el-icon>
          </div>
          <div class="contact-item">
            <span class="contact-label">QQ反馈群：</span>
            <span class="contact-value copyable" @click="copyText('605545024')">605545024</span>
            <el-icon class="copy-icon" @click="copyText('605545024')" v-if="!copied.qq"><DocumentCopy /></el-icon>
            <el-icon class="copy-icon copied" v-else><Check /></el-icon>
          </div>
        </div>
        
        <div class="machine-code-section">
          <div class="section-header">
            <h3>机器码</h3>
            <el-tooltip
              content="请将此机器码提供给软件供应商以获取授权文件"
              placement="top"
            >
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
          <div class="code-box">
            <div class="code-content">
              <span class="code-segment" v-for="(segment, index) in machineCodeSegments" :key="index">
                {{ segment }}
              </span>
            </div>
            <el-button 
              type="primary" 
              :icon="DocumentCopy"
              @click="copyMachineCode"
              class="copy-btn"
              :disabled="copied.machineCode"
            >
              {{ copied.machineCode ? '已复制' : '复制机器码' }}
            </el-button>
          </div>
        </div>

        <div class="license-section">
          <div class="section-header">
            <h3>导入授权文件</h3>
          </div>
          <div class="license-uploader">
            <el-button 
              type="primary" 
              :icon="Upload"
              @click="selectLicenseFile"
            >
              选择授权文件
            </el-button>
            <div class="upload-tip">
              支持扩展名：.license
            </div>
          </div>
        </div>

        <div class="status-section" v-show="licenseStatus">
          <el-alert
            :title="licenseStatus?.message || ''"
            :type="licenseStatus?.status ? 'success' : 'error'"
            show-icon
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import message from '@/utils/message'
import { getMachineCodeAPI, checkLicenseStatusAPI, verifyLicenseAPI } from '@/apis/system'
import { getLocalFilePathAPI } from '@/apis/common'
import { useRouter } from 'vue-router'
import { DocumentCopy, Upload, QuestionFilled, Lock, Check } from '@element-plus/icons-vue'

const router = useRouter()
const machineCode = ref('')
const licenseStatus = ref(null)
const loading = ref(true)
const copied = ref({
  email: false,
  qq: false,
  machineCode: false
})

// 将机器码分段显示
const machineCodeSegments = computed(() => {
  if (!machineCode.value) return []
  return machineCode.value.split('-')
})

// 初始化数据
const initializeData = async () => {
  try {
    // 同时请求授权状态和机器码
    const [licenseResponse, machineCodeResponse] = await Promise.all([
      checkLicenseStatusAPI(),
      getMachineCodeAPI()
    ])
    message
    // 更新状态
    if (licenseResponse && licenseResponse.data) {
        licenseStatus.value = licenseResponse.data
        // 更新许可证缓存
        localStorage.setItem('license_status', JSON.stringify({
          status: licenseResponse.data.status,
          timestamp: Date.now()
        }))
     }
     if (machineCodeResponse && machineCodeResponse.data) {
        machineCode.value = machineCodeResponse.data.machine_code
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
    message.error('初始化数据失败' + error.message)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  initializeData()
})

const copyMachineCode = () => {
  navigator.clipboard.writeText(machineCode.value)
    .then(() => {
      copied.value.machineCode = true
      setTimeout(() => copied.value.machineCode = false, 2000)
      message.success('机器码已复制到剪贴板')
    })
    .catch(() => message.error('复制失败'))
}

const selectLicenseFile = async () => {
  try {
    const response = await getLocalFilePathAPI()
    if (response.data.selected_file) {
      loading.value = true
      const filePath = response.data.selected_file
      const res = await verifyLicenseAPI(filePath)
      if (res.data.status) {
        const licenseStatus = await checkLicenseStatus()
        // 更新许可证缓存
        localStorage.setItem('license_status', JSON.stringify({
          status: licenseStatus.status,
          timestamp: Date.now()
        }))
        message.success('导入授权文件成功')
      } else {
        message.error(res.data.message)
      }
    }
  } catch (error) {
    message.error('导入授权文件失败')
  } finally {
    loading.value = false
  }
}

const checkLicenseStatus = async () => {
  try {
    const response = await checkLicenseStatusAPI()
    // 更新许可证缓存
    localStorage.setItem('license_status', JSON.stringify({
      status: response.data.status,
      timestamp: Date.now()
    }))
    licenseStatus.value = response.data
    return response.data
  } catch (error) {
    message.error('检查授权状态失败')
    throw error
  }
}

const copyText = (text) => {
  navigator.clipboard.writeText(text)
    .then(() => {
      if (text.includes('@')) {
        copied.value.email = true
        setTimeout(() => copied.value.email = false, 2000)
      } else {
        copied.value.qq = true
        setTimeout(() => copied.value.qq = false, 2000)
      }
      message.success('复制成功')
    })
    .catch(() => message.error('复制失败'))
}
</script>

<style scoped>
.license-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 90vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.license-card {
  width: 650px;
  max-width: 90%;
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  color: #606266;
}

.contact-info-section {
  background: #f8f9fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 24px;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
}

.contact-value {
  color: #409eff;
}

.copyable {
  cursor: pointer;
}

.copyable:hover {
  text-decoration: underline;
}

.contact-value a {
  color: #409eff;
  text-decoration: none;
}

.contact-value a:hover {
  text-decoration: underline;
}

.copy-icon {
  margin-left: 8px;
  font-size: 16px;
  cursor: pointer;
  color: #909399;
}

.copy-icon.copied {
  color: #67c23a;
}

.machine-code-section,
.license-section,
.status-section {
  margin-bottom: 24px;
}

.code-box {
  background: #f8f9fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 16px;
}

.code-content {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.code-segment {
  font-family: monospace;
  font-size: 16px;
  color: #409eff;
  background: #ecf5ff;
  padding: 4px 8px;
  border-radius: 4px;
  letter-spacing: 1px;
}

.copy-btn {
  width: 100%;
}

.license-uploader {
  text-align: center;
  padding: 20px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.auth-success-section {
  padding: 20px 0;
}

:deep(.el-result__icon) {
  width: 48px;
  height: 48px;
}

:deep(.el-result__title) {
  margin-top: 16px;
}
</style>
