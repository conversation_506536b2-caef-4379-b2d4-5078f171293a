<template>
  <div class="screenshot-container">
    <PageHeader
      title="截图美化"
      description="为您的截图添加精美的边框和背景，让截图更具专业感"
    />
    <div class="iframe-container" v-loading="loading" element-loading-text="加载中...">
      <iframe 
        src="https://shoteasy.lingxiangtools.top/"
        frameborder="0"
        allowfullscreen
        @load="onIframeLoaded"
      ></iframe>
    </div>
  </div>
</template>

<script setup>
import PageHeader from '@/components/PageHeader.vue'
import { ref } from 'vue'

const loading = ref(true)

const onIframeLoaded = () => {
  loading.value = false
}
</script>

<style scoped>
.screenshot-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.iframe-container {
  flex: 1;
  overflow: hidden;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
}

iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style> 