<template>
  <div 
    class="single-ocr-container"
    @dragover.prevent
    @drop="handleDrop"
  >
    <!-- 上传区域 -->
    <div v-if="!originalImage" class="upload-section">
      <!-- 添加上传按钮 -->
      <div class="upload-button">
        <UploadButton 
          text="选择图片"
          @upload="handleClickUpload"
        />
      </div>

      <!-- 提示文字 -->
      <div class="upload-tip">
        可拖放、粘贴图片、粘贴链接，支持 jpg/png/webp/bmp, 点击图片快速体验
      </div>
    </div>

    <!-- 图片预览和结果区域 -->
    <div v-else class="preview-result-section">
      <!-- 图片预览 -->
      <div class="image-preview" v-loading="loading">
        <el-image 
          :src="originalImage" 
          fit="contain"
          :preview-src-list="[originalImage]"
          class="preview-image"
        />
        <div class="preview-actions">
          <el-button @click="resetImage" size="small">重新选择</el-button>
        </div>
      </div>

      <!-- OCR结果区域 -->
      <div class="ocr-result" v-if="result">
        <div class="result-header">
          <span class="result-title">识别结果</span>
          <el-button 
            type="primary" 
            link 
            @click="copyResult"
            size="small"
          >
            一键复制
          </el-button>
        </div>
        <div class="result-content">
          <el-input
            v-model="result"
            type="textarea"
            :rows="16"
            placeholder="识别结果将在这里显示"
            resize="none"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import message from '@/utils/message'
import { getImgBase64API } from '@/apis/common'
import UploadButton from '@/components/UploadButton.vue'
import baseAPI from '@/apis/base'
import { ppocrAPI } from '@/apis/ppocr'

const originalImage = ref(null)
const loading = ref(false)
const result = ref(null)

const resetImage = () => {
  originalImage.value = null
  loading.value = false
  result.value = null
}


// 文件上传相关方法
const handleClickUpload = async () => {
  loading.value = true
  try {
    const response = await baseAPI('open_img_file', false)
    if (response.code === 200 && response.data.selected_file) {
      originalImage.value = response.data.image_url
      message.success('上传成功')
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}

// 监听originalImage，如果originalImage发生变化，则调用handleOCR
watch(originalImage, () => {
  if (originalImage.value) {
    handleOCR()
  }
})

// 处理拖拽事件
const handleDragOver = (event) => {
  event.preventDefault();
};
const handleDrop = async (e) => {
  e.preventDefault();
  
  const file = e.dataTransfer?.files[0]
  if (!file) return
  
  // 检查文件类型支持jpg png webp gif bmp tiff
  const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/jpg', 'image/bmp']
  if (!validTypes.includes(file.type)) {
    message.error('只支持 JPG、PNG、WebP、GIF、BMP 格式的图片')
    return
  }

  loading.value = true
  try {
    resetImage()
    if (file.type.match('image.*')) {
      const reader = new FileReader();
      reader.onload = function (e) {
        originalImage.value = e.target.result
        message.success('上传成功')
      };
      reader.readAsDataURL(file);
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}


// 验证是否是有效的URL
const isValidUrl = (string) => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};

// 处理粘贴事件
const handlePaste = (event) => {
  event.preventDefault();
  const clipboardData = event.clipboardData || window.Clipboard;
  const items = clipboardData.items;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    if (item.kind === 'string') {
      loading.value = true
      item.getAsString((text) => {
        // 判断是不是base64
        if (text.startsWith('data:image/')) {
          console.log('粘贴的是base64:', text);
          originalImage.value = text
          loading.value = false
        } else if (isValidUrl(text)) {
          console.log('粘贴的是URL:', text);
          getImgBase64API(text).then(res => {
            if (res.code === 200) {
              originalImage.value = res.data.base64_image
            } else {
              const errorMsg = res.msg || '获取图片失败';
              message.error(`获取图片失败: ${errorMsg}`);
              console.error('获取图片失败:', res);
            }
          }).catch(error => {
            message.error(`获取图片失败: ${error.message || '未知错误'}`);
            console.error('API调用失败:', error);
          }).finally(() => {
            loading.value = false
          });
        } else {
          loading.value = false
        }
      });
    } else if (item.kind === 'file') {
      const type = item.type.split('/')[0];
      if (!['image'].includes(type)) {
        message.error('请粘贴图片文件');
        return;
      }
      loading.value = true
      const file = item.getAsFile();
      const reader = new FileReader();
      reader.onload = (e) => {
        console.log('粘贴的是图片:', e.target.result);
        originalImage.value = e.target.result
        loading.value = false
      };
      reader.readAsDataURL(file);
    }
  }
};

onMounted(() => {
  window.addEventListener('paste', handlePaste)
  window.addEventListener('dragover', handleDragOver)
  window.addEventListener('drop', handleDrop)
})

onUnmounted(() => {
  window.removeEventListener('paste', handlePaste)
  window.removeEventListener('dragover', handleDragOver)
  window.removeEventListener('drop', handleDrop)
})

// 添加复制功能
const copyResult = () => {
  if (result.value) {
    navigator.clipboard.writeText(result.value)
      .then(() => {
        message.success('复制成功')
      })
      .catch(() => {
        message.error('复制失败')
      })
  }
}

// 添加OCR处理函数
const handleOCR = async () => {
  if (!originalImage.value) return
  
  loading.value = true
  try {
    const response = await ppocrAPI('ppocr', {
      image_path: originalImage.value
    })
    
    if (response.code === 200) {
      result.value = response.data.result
      message.success('识别成功')
    } else {
      message.error(response.msg || '识别失败')
    }
  } catch (error) {
    console.error('OCR Error:', error)
    message.error('识别失败')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.single-ocr-container {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 20px;
  min-height: 500px;

  .upload-section {
    text-align: center;
    padding: 40px 0;

    .upload-button {
      margin-bottom: 16px;
    }

    .upload-tip {
      color: var(--el-text-color-secondary);
      font-size: 13px;
    }
  }

  .preview-result-section {
    display: flex;
    gap: 20px;
    height: 65vh;  // 减去头部和边距的高度
    
    .image-preview {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
      max-width: 62%;
      
      .preview-image {
        width: 100%;
        height: 62vh;
        border-radius: 4px;
        border: 1px solid var(--el-border-color);
        background-color: var(--el-fill-color-lighter);

        :deep(.el-image__inner) {
          max-height: 100%;
          max-width: 100%;
          object-fit: contain;
        }
      }

      .preview-actions {
        display: flex;
        gap: 12px;
        justify-content: center;
      }
    }

    .ocr-result {
      flex: 1;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      min-width: 45%;
      height: 60vh;
      overflow: hidden;  // 防止内容溢出
      
      .result-header {
        padding: 12px 16px;
        border-bottom: 1px solid var(--el-border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .result-title {
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
      }

      .result-content {
        padding: 16px;
        flex: 1;
        height: calc(100% - 52px);  // 减去header高度
        overflow: hidden;

        :deep(.el-textarea) {
          height: 100%;
          
          .el-textarea__inner {
            height: 100% !important;  // 强制高度100%
            font-family: var(--el-font-family);
            line-height: 1.6;
            color: var(--el-text-color-primary);
            resize: none;
            overflow-y: auto;
          }
        }
      }
    }
  }
}
</style>
