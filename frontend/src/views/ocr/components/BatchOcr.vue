<template>
  <div class="batch-ocr">
    <div class="layout-container">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <el-form label-position="left">
          <el-form-item label="输入目录">
            <div class="folder-select">
              <el-tooltip
                :content="inputPlaceholder"
                placement="top"
                effect="light"
                :show-after="500"
              >
                <el-input
                  v-model="inputFolder"
                  :placeholder="inputPlaceholder"
                >
                  <template #append>
                    <el-button @click="selectInputFolder">
                      <el-icon><Folder /></el-icon>
                      选择
                    </el-button>
                  </template>
                </el-input>
              </el-tooltip>
            </div>
          </el-form-item>

          <el-form-item label="输出目录">
            <div class="folder-select">
              <el-tooltip
                :content="outputPlaceholder"
                placement="bottom"
                effect="light"
                :show-after="500"
              >
                <el-input
                  v-model="outputFolder"
                  :placeholder="outputPlaceholder"
                >
                  <template #append>
                    <el-button @click="selectOutputFolder">
                      <el-icon><Folder /></el-icon>
                      选择
                    </el-button>
                  </template>
                </el-input>
              </el-tooltip>
            </div>
          </el-form-item>

          <div class="action-buttons">
            <el-button
              type="primary"
              @click="startProcessing"
              :disabled="!canStart || processing"
              class="main-button"
            >
              <el-icon><VideoPlay /></el-icon>
              开始处理
            </el-button>
            <el-button
              v-if="processing"
              @click="pauseProcessing"
              :type="isPaused ? 'success' : 'warning'"
            >
              <el-icon><VideoPause v-if="!isPaused"/><VideoPlay v-else/></el-icon>
              {{ isPaused ? '继续处理' : '暂停处理' }}
            </el-button>
            <el-button
              v-if="processing"
              type="danger"
              @click="stopProcessing"
            >
              <el-icon><CircleClose /></el-icon>
              终止处理
            </el-button>
          </div>
        </el-form>
      </div>

      <!-- 右侧进度和结果面板 -->
      <div class="result-panel" v-if="fileList.length > 0">
        <el-card class="progress-card">
          <template #header>
            <div class="progress-header">
              <div class="progress-info">
                <div class="progress-title">
                  <div class="left">
                    <span class="title">处理进度</span>
                    <el-tag size="small" :type="getProgressTagType">
                      {{ processedCount }}/{{ fileList.length }}
                    </el-tag>
                  </div>
                  <div class="right">
                    <el-tag 
                      v-if="processing && !isPaused" 
                      size="small" 
                      type="warning"
                    >
                      <el-icon><Timer /></el-icon>
                      预计剩余: {{ remainingTime }}
                    </el-tag>
                  </div>
                </div>
                <el-progress
                  :percentage="totalProgress"
                  :status="processStatus"
                  :stroke-width="8"
                  :format="progressFormat"
                  class="total-progress"
                />
              </div>
              <div class="progress-stats" v-if="errorCount > 0">
                <el-tag size="small" type="danger">
                  <el-icon><CircleCloseFilled /></el-icon>
                  失败: {{ errorCount }}
                </el-tag>
              </div>
            </div>
          </template>

          <div class="file-list">
            <el-scrollbar height="400px" class="file-list-content">
              <div
                v-for="file in fileList"
                :key="file.path"
                class="file-item"
                :class="{ 
                  'processing': file.processing,
                  'processed': file.processed,
                  'error': file.error
                }"
              >
                <div class="item-preview">
                  <el-image
                    :src="file.originalUrl"
                    fit="cover"
                    class="preview-thumbnail"
                    lazy
                  />
                </div>
                <div class="item-content">
                  <div class="item-info">
                    <span class="file-name" :title="file.name">{{ file.name }}</span>
                    <div class="status-icon">
                      <el-icon v-if="file.processed" class="success"><CircleCheckFilled /></el-icon>
                      <el-icon v-else-if="file.error" class="error"><CircleCloseFilled /></el-icon>
                      <el-icon v-else-if="file.processing" class="processing"><Loading /></el-icon>
                      <el-icon v-else class="waiting"><Clock /></el-icon>
                    </div>
                  </div>
                  <div v-if="file.result" class="ocr-result">{{ file.result }}</div>
                  <el-progress
                    v-if="file.processing || file.processed"
                    :percentage="file.progress"
                    :status="file.error ? 'exception' : file.processed ? 'success' : ''"
                  />
                </div>
              </div>
            </el-scrollbar>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElLoading } from 'element-plus'
import {
  Folder,
  VideoPlay,
  VideoPause,
  CircleClose,
  CircleCheckFilled,
  CircleCloseFilled,
  Clock,
  Loading,
  Timer
} from '@element-plus/icons-vue'
import message from '@/utils/message'
import baseAPI from '@/apis/base'
import { ppocrAPI } from '@/apis/ppocr'

// 状态变量
const inputFolder = ref('')
const outputFolder = ref('')
const fileList = ref([])
const processing = ref(false)
const isPaused = ref(false)
const processedCount = ref(0)
const startTime = ref(null)

// 计算属性
const canStart = computed(() => {
  return inputFolder.value && fileList.value.length > 0
})

const totalProgress = computed(() => {
  if (fileList.value.length === 0) return 0
  return Math.round((processedCount.value / fileList.value.length) * 100)
})

const processStatus = computed(() => {
  if (isPaused.value) return 'warning'
  if (processing.value) return ''
  return totalProgress.value === 100 ? 'success' : ''
})

const remainingTime = computed(() => {
  if (!startTime.value || processedCount.value === 0) return '计算中...'
  
  const elapsed = (Date.now() - startTime.value) / 1000
  const avgTimePerFile = elapsed / processedCount.value
  const remaining = (fileList.value.length - processedCount.value) * avgTimePerFile
  
  if (remaining < 60) return `${Math.round(remaining)}秒`
  if (remaining < 3600) return `${Math.round(remaining / 60)}分钟`
  return `${Math.round(remaining / 3600)}小时${Math.round((remaining % 3600) / 60)}分钟`
})

// 错误计数
const errorCount = computed(() => {
  return fileList.value.filter(file => file.error).length
})

// 重置状态
const resetState = () => {
  processedCount.value = 0
  startTime.value = null
  processing.value = false
  isPaused.value = false
  fileList.value = []
}

// 选择输入文件夹
const selectInputFolder = async () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    background: 'rgba(255, 255, 255, 0.9)',
    fullscreen: true
  })
  
  try {
    resetState()
    
    const response = await baseAPI('open_folder_dialog', '')
    if (response.code === 200 && response.data.folder_path) {
      inputFolder.value = response.data.folder_path
      const filesResponse = await baseAPI('get_folder_images', inputFolder.value)
      if (filesResponse.code === 200) {
        await loadFileList(filesResponse.data.image_list)
        message.success(`已加载 ${fileList.value.length} 张图片`)
      }
    }
  } catch (error) {
    console.error(error)
    message.error('获取图片列表失败')
  } finally {
    loadingInstance.close()
  }
}

// 选择输出文件夹
const selectOutputFolder = async () => {
  try {
    const response = await baseAPI('open_folder_dialog', '')
    if (response.code === 200 && response.data.folder_path) {
      outputFolder.value = response.data.folder_path
    }
  } catch (error) {
    message.error('选择文件夹失败')
  }
}

// 开始处理
const startProcessing = async () => {
  if (processing.value) return
  processing.value = true
  startTime.value = Date.now()
  processedCount.value = 0
  
  for (const file of fileList.value) {
    if (isPaused.value) {
      await new Promise(resolve => {
        const checkPause = () => {
          if (!isPaused.value) resolve()
          else setTimeout(checkPause, 100)
        }
        checkPause()
      })
    }
    
    if (!processing.value) break
    
    try {
      file.processing = true
      file.progress = 0
      
      const progressInterval = setInterval(() => {
        if (file.progress < 90) file.progress += 10
      }, 200)
      
      const response = await ppocrAPI('ppocr_from_folder', {
        input_folder: inputFolder.value,
        output_folder: outputFolder.value,
        image_path: file.path
      })
      
      clearInterval(progressInterval)
      
      if (response.code === 200) {
        file.progress = 100
        file.processed = true
        file.result = response.data.result
        processedCount.value++
      } else {
        throw new Error(response.msg || '处理失败')
      }
    } catch (error) {
      file.error = true
      file.progress = 100
      message.error(`处理文件 ${file.name} 失败: ${error.message}`)
    } finally {
      file.processing = false
    }
  }
  
  processing.value = false
  isPaused.value = false
  message.success('批量处理完成')
}

const pauseProcessing = () => {
  isPaused.value = !isPaused.value
}

const stopProcessing = () => {
  processing.value = false
  isPaused.value = false
  message.warning('已终止处理')
}

const loadFileList = async (files) => {
  fileList.value = files.map(file => ({
    name: file.image_name,
    path: file.image_path,
    processing: false,
    processed: false,
    error: false,
    progress: 0,
    originalUrl: file.image_url,
    result: ''
  }))
}

const progressFormat = (percentage) => {
  if (percentage === 100) return '已完成'
  if (isPaused.value) return '已暂停'
  if (processing.value) return `${percentage}%`
  return '等待开始'
}

const getProgressTagType = computed(() => {
  if (totalProgress.value === 100) return 'success'
  if (isPaused.value) return 'warning'
  if (processing.value) return 'primary'
  return 'info'
})

const inputPlaceholder = '选择或输入图片文件夹路径'
const outputPlaceholder = '选择或输入保存位置，不填则保存在图片同级目录'


onMounted(() => {
  resetState()
})
</script>

<style lang="scss" scoped>
// ... 复用 BatchMatting.vue 的样式 ...
.batch-ocr {
  height: 100%;
  .layout-container {
    height: 100%;
    display: flex;
    gap: 24px;
    
    .config-panel {
      flex: 0 0 400px;
      
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 10px;
          
          .el-form-item__label {
            font-weight: 500;
            margin-left: 10px;
          }
        }
      }
      
      .folder-select {
        .el-input {
          :deep(.el-input-group__append) {
            .el-button {
              padding: 8px 16px;
              .el-icon {
                margin-right: 4px;
              }
            }
          }
        }
      }
      
      .action-buttons {
        display: flex;
        gap: 12px;
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid var(--el-border-color-lighter);
        
        .main-button {
          flex: 1;
          max-width: 200px;
          height: 40px;
          font-size: 16px;
          
          .el-icon {
            margin-right: 8px;
            font-size: 18px;
          }
        }
      }
    }
    
    .result-panel {
      flex: 1;
      min-width: 0;
      
      .progress-card {
        height: 100%;
        
        .file-list {
          .file-item {
            display: flex;
            gap: 16px;
            padding: 12px;
            border-bottom: 1px solid var(--el-border-color-lighter);
            
            &.processing {
              background-color: var(--el-color-primary-light-9);
            }
            
            &.processed {
              background-color: var(--el-color-success-light-9);
            }
            
            &.error {
              background-color: var(--el-color-danger-light-9);
            }
            
            .item-preview {
              width: 100px;
              height: 100px;
              border-radius: 4px;
              overflow: hidden;
              flex-shrink: 0;
              
              .preview-thumbnail {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            
            .item-content {
              flex: 1;
              min-width: 0;
              
              .item-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
              }
              
              .ocr-result {
                margin: 8px 0;
                color: var(--el-text-color-regular);
                white-space: pre-wrap;
                font-size: 13px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
