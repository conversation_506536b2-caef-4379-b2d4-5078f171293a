<template>
  <div class="screenshot-container">
    <div class="iframe-container" v-loading="loading" element-loading-text="加载中...">
      <iframe
        v-if="!showError"
        :src="currentUrl"
        frameborder="0"
        allowfullscreen
        @load="onIframeLoaded"
        @error="onIframeError"
      ></iframe>
      <div v-else class="error-container">
        <div class="error-content">
          <i class="el-icon-warning-outline error-icon"></i>
          <p class="error-text">网络错误，请检查网络连接后重试</p>
          <el-button type="primary" @click="retry">重试</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const loading = ref(true)
const showError = ref(false)
const currentUrl = ref('https://img.ops-coffee.cn/photo/')
const backupUrl = 'https://collagemaker.tools/photo/'
const primaryUrl = 'https://img.ops-coffee.cn/photo/'

let loadTimer = null
let hasLoaded = ref(false)
let retryCount = ref(0)

const onIframeLoaded = () => {
  if (loadTimer) {
    clearTimeout(loadTimer)
    loadTimer = null
  }
  loading.value = false
  hasLoaded.value = true
}

const onIframeError = () => {
  if (loadTimer) {
    clearTimeout(loadTimer)
    loadTimer = null
  }

  if (retryCount.value === 0 && currentUrl.value === primaryUrl) {
    // 第一次失败，尝试备用URL
    retryCount.value = 1
    currentUrl.value = backupUrl
    hasLoaded.value = false
    startLoadTimer()
  } else {
    // 备用URL也失败，显示错误
    loading.value = false
    showError.value = true
  }
}

const startLoadTimer = () => {
  loadTimer = setTimeout(() => {
    if (!hasLoaded.value) {
      onIframeError()
    }
  }, 12000) // 8秒超时
}

const retry = () => {
  showError.value = false
  loading.value = true
  hasLoaded.value = false
  retryCount.value = 0
  currentUrl.value = primaryUrl
  startLoadTimer()
}

onMounted(() => {
  startLoadTimer()
})
</script>

<style scoped>
.screenshot-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.iframe-container {
  flex: 1;
  overflow: hidden;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
}

iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.error-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
}

.error-content {
  text-align: center;
  padding: 40px;
}

.error-icon {
  font-size: 64px;
  color: #f56c6c;
  margin-bottom: 16px;
}

.error-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 24px;
  line-height: 1.5;
}
</style>