<template>
  <div 
    class="beautify-page"
    @dragover.prevent
    @drop.prevent="handleDrop"
  >
    <PageHeader
      title="截图美化"
      description="为您的截图添加精美的边框和背景，让截图更具专业感"
      :tags="['支持多种系统边框', '自定义背景', '一键美化']"
    />

    <el-loading 
      v-model:visible="loading"
      :lock="true"
      text="处理中..."
    />

    <div class="content-area">
      <div class="upload-area" v-if="!currentImage">
        <div class="upload-content">
          <!-- 简化为按钮 -->
          <UploadButton 
            text="选择图片"
            @upload="handleClickUpload"
          />
          
          <!-- 提示文字 -->
          <div class="upload-tip">
            可拖放、粘贴图片、粘贴链接，支持 jpg/png/webp/bmp, 点击图片快速体验
          </div>
        </div>

        <!-- 示例图片区域 -->
        <div class="demo-images">
          <div 
            v-for="(base64, key) in imageList" 
            :key="key"
            class="demo-image-item"
            @click="tryDemo(base64)"
          >
            <img :src="base64" :alt="key">
          </div>
        </div>
      </div>

      <div v-else class="editor-area">
        <ImageFrameEditor 
          :image-url="currentImage" 
          @save="handleSave"
          ref="editorRef"
        />
        
        <div class="action-bar">
          <el-button @click="resetImage">重新上传</el-button>
          <el-button type="primary" @click="saveImage">导出图片</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import message from '@/utils/message'
import PageHeader from '@/components/PageHeader.vue'
import ImageFrameEditor from '@/components/ImageTool/ImageFrameEditor.vue'
import baseAPI from '@/apis/base'
import imageList from '@/utils/demo'
import { getImgBase64API } from '@/apis/common'
import UploadButton from '@/components/UploadButton.vue'

const currentImage = ref(null)
const editorRef = ref(null)
const loading = ref(false)

const handleFileChange = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    currentImage.value = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

const resetImage = () => {
  currentImage.value = null
}

const exportImage = async (base64_data) => {
  const response = await baseAPI('save_image_dialog', {
    base64_data: base64_data,
    extension: 'png',
  })
  if (response.code === 200) {
    message.success('图片导出成功')
  } else {
    message.error('图片导出失败')
  }
}

const saveImage = async () => {
  const result = await editorRef.value.exportImage()
  await exportImage(result)
}

const handleSave = (imageData) => {
  // 处理保存的图片数据
  console.log('Saved image data:', imageData)
}

// 新增功能：处理拖拽事件
const handleDrop = async (e) => {
  const file = e.dataTransfer?.files[0]
  if (!file) return
  
  // 检查文件类型
  const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/jpg', 'image/bmp']
  if (!validTypes.includes(file.type)) {
    message.error('只支持 JPG、PNG、WebP、GIF、BMP 格式的图片')
    return
  }

  loading.value = true
  try {
    resetImage()
    if (file.type.match('image.*')) {
      const reader = new FileReader();
      reader.onload = function (e) {
        currentImage.value = e.target.result
        message.success('上传成功')
      };
      reader.readAsDataURL(file);
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}

// 新增功能：验证URL
const isValidUrl = (string) => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};



// 新增功能：处理粘贴事件
const handlePaste = (event) => {
  event.preventDefault();
  const clipboardData = event.clipboardData || window.Clipboard;
  const items = clipboardData.items;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    if (item.kind === 'string') {
      loading.value = true
      item.getAsString((text) => {
        // 判断是不是base64
        if (text.startsWith('data:image/')) {
          console.log('粘贴的是base64:', text);
          currentImage.value = text
          loading.value = false
        } else if (isValidUrl(text)) {
          console.log('粘贴的是URL:', text);
          getImgBase64API(text).then(res => {
            if (res.code === 200) {
              currentImage.value = res.data.base64_image
            } else {
              const errorMsg = res.msg || '获取图片失败';
              message.error(`获取图片失败: ${errorMsg}`);
              console.error('获取图片失败:', res);
            }
          }).catch(error => {
            message.error(`获取图片失败: ${error.message || '未知错误'}`);
            console.error('API调用失败:', error);
          }).finally(() => {
            loading.value = false
          });
        } else {
          message.error('请粘贴有效的图片链接或图片文件');
          loading.value = false
        }
      });
    } else if (item.kind === 'file') {
      const type = item.type.split('/')[0];
      if (!['image'].includes(type)) {
        message.error('请粘贴图片文件');
        return;
      }
      loading.value = true
      const file = item.getAsFile();
      const reader = new FileReader();
      reader.onload = (e) => {
        console.log('粘贴的是图片:', e.target.result);
        currentImage.value = e.target.result
        loading.value = false
      };
      reader.readAsDataURL(file);
    }
  }
};

// 新增功能：尝试示例图片
const tryDemo = (base64) => {
  resetImage()
  currentImage.value = base64
}

// 新增方法：通过系统文件选择器上传
const handleClickUpload = async () => {
  loading.value = true
  try {
    const response = await baseAPI('open_img_file', false)
    if (response.code === 200 && response.data.selected_file) {
      resetImage()
      console.log(response.data)
      currentImage.value = response.data.image_url
      message.success('上传成功')
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}

// 注册和卸载事件监听器
onMounted(() => {
  document.addEventListener('paste', handlePaste);
  document.addEventListener('dragover', (e) => e.preventDefault());
  document.addEventListener('drop', handleDrop);
});

onUnmounted(() => {
  document.removeEventListener('paste', handlePaste);
  document.removeEventListener('dragover', (e) => e.preventDefault());
  document.removeEventListener('drop', handleDrop);
});
</script>

<style scoped>
.beautify-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background: white;
  border-radius: 8px;
  margin-top: 20px;
}

.upload-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;
}

.upload-button {
  padding: 12px 24px;
  font-size: 16px;
}

.upload-icon {
  margin-right: 8px;
}

.upload-tip {
  margin-top: 20px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  text-align: center;
}

.demo-images {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 1200px;
  width: 100%;
}

.demo-image-item {
  width: 100%;
  aspect-ratio: 1/1;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.demo-image-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.1);
  border-color: var(--el-color-primary-light-5);
}

.demo-image-item img {
  width: 100%;
  height: 200px;
  border-radius: 8px;
  object-fit: cover;
}

.demo-image-item:hover img {
  transform: scale(1.05);
}

.editor-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.action-bar {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid var(--el-border-color-light);
}
</style> 