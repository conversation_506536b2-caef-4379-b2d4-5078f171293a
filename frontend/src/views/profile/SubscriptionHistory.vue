<template>
  <div class="subscription-history">
    <el-card class="history-card">
      <template #header>
        <div class="card-header">
          <span>订阅历史</span>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else>
        <div v-if="historyList.length === 0" class="empty-state">
          <el-empty description="暂无订阅记录" />
        </div>

        <div v-else class="history-list">
          <el-timeline>
            <el-timeline-item
              v-for="item in historyList"
              :key="item.id"
              :timestamp="formatDate(item.create_time)"
              :type="getStatusType(item.status)"
            >
              <el-card class="history-item">
                <div class="history-content">
                  <div class="plan-info">
                    <h4>{{ item.plan.name }}</h4>
                    <p class="price">¥{{ item.actual_amount }}</p>
                  </div>
                  
                  <div class="details">
                    <div class="detail-item">
                      <span class="label">订单号：</span>
                      <span class="value">{{ item.order_no }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">支付方式：</span>
                      <span class="value">{{ item.pay_channel }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">订阅期限：</span>
                      <span class="value">{{ item.duration_days || item.plan.duration_days  }}天</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">状态：</span>
                      <el-tag :type="getStatusType(item.status)" size="small">
                        {{ getStatusText(item.status) }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>

        <!-- 分页 -->
        <div class="pagination-container" v-if="historyList.length > 0">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="total"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import message from '@/utils/message'
import { getOrdersAPI } from '@/apis/system'
const loading = ref(true)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const historyList = ref([])

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    paid: 'success',
    pending: 'warning',
    failed: 'danger',
    cancelled: 'info'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    paid: '支付成功',
    pending: '待支付',
    failed: '支付失败',
    cancelled: '已取消'
  }
  return texts[status] || '未知状态'
}

// 处理页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchHistoryList()
}

// 处理每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchHistoryList()
}

// 获取历史记录列表
const fetchHistoryList = async () => {
  loading.value = true
  try {
    const res = await getOrdersAPI(
      currentPage.value,
      pageSize.value
    )
    console.log(res)
    if (res.code === 200) {
      historyList.value = res.data
      total.value = res.total
    } else {
      message.error('获取订阅历史失败')
    }
  } catch (error) {
    console.error('Failed to fetch history:', error)
    message.error('获取订阅历史失败')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchHistoryList()
})
</script>

<style scoped>
.subscription-history {
  padding: 24px;
}

.history-card {
  border-radius: 8px;
}

.card-header {
  font-size: 16px;
  font-weight: 500;
}

.loading-container {
  padding: 20px 0;
}

.empty-state {
  padding: 40px 0;
}

.history-list {
  padding: 20px 0;
}

.history-item {
  margin-bottom: 4px;
}

.history-content {
  padding: 8px;
}

.plan-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.plan-info h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.price {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #f56c6c;
}

.details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  color: #909399;
  font-size: 14px;
}

.value {
  color: #606266;
  font-size: 14px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 