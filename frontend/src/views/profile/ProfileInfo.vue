<template>
  <div class="profile-info">
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>基本信息</span>
        </div>
      </template>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else class="info-content">
        <div class="info-list">
          <div class="info-item">
            <span class="info-label">邮箱账号</span>
            <span class="info-value">{{ userInfo.email || '未设置' }}</span>
          </div>
          
          <div class="info-item">
            <span class="info-label">会员状态</span>
            <span class="info-value">
              <el-tag :type="userInfo.isMember ? 'success' : 'info'">
                {{ userInfo.isMember ? 'VIP会员' : '普通用户' }}
              </el-tag>
            </span>
          </div>

          <template v-if="userInfo.isMember">
            <div class="info-item">
              <span class="info-label">开通时间</span>
              <span class="info-value">{{ formatDateTime(userInfo.memberCreatedTime) }}</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">到期时间</span>
              <span class="info-value highlight">{{ formatDateTime(userInfo.memberExpiredTime) }}</span>
            </div>

            <div class="info-item">
              <span class="info-label">剩余天数</span>
              <span class="info-value highlight">{{ calculateRemainingDays(userInfo.memberExpiredTime) }} 天</span>
            </div>
          </template>
          
          <div class="info-item">
            <span class="info-label">邀请码 
              <el-tooltip
                content="邀请人会获取7天会员"
                placement="top"
                effect="light"
              >
                <el-icon class="help-icon"><QuestionFilled /></el-icon>
              </el-tooltip>
            </span>
            
            <span class="info-value">
              <template v-if="userInfo.isPayUser">
                <template v-if="userInfo.inviteCode">
                  {{ userInfo.inviteCode }}
                  <el-button 
                    type="primary" 
                    link 
                    size="small" 
                    @click="copyInviteCode"
                  >
                    复制
                  </el-button>
                </template>
                <template v-else>未生成</template>
              </template>
              <template v-else>付费订阅用户可以获取邀请权限</template>
            </span>
          </div>

          <div class="info-item">
            <span class="info-label">注册时间</span>
            <span class="info-value">{{ formatDateTime(userInfo.createTime) }}</span>
          </div>

          <div class="info-item">
            <span class="info-label">试用状态</span>
            <span class="info-value">
              <el-tag type="info">{{ userInfo.hasUsedTrial ? '已使用' : '未使用' }}</el-tag>
            </span>
          </div>
        </div>

        <el-divider />
        
        <div class="action-buttons">
          <el-button type="danger" plain @click="handleLogout">退出登录</el-button>
          <el-button @click="goBack">返回首页</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getUserInfoAPI, logoutAPI } from '@/apis/system'
import { setUserInfo, getUserInfo, clearAuthData } from '@/utils/auth'
import message from '@/utils/message'

const router = useRouter()
const loading = ref(true)
const userInfo = ref({
  email: '',
  inviteCode: '',
  createTime: null,
  isMember: false,
  memberCreatedTime: null,
  memberExpiredTime: null,
  hasUsedTrial: false,
  isPayUser: false
})

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '未设置'
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 计算剩余天数
const calculateRemainingDays = (expireDate) => {
  if (!expireDate) return 0
  const now = new Date()
  const expire = new Date(expireDate)
  const diffTime = expire - now
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays > 0 ? diffDays : 0
}

// 复制邀请码
const copyInviteCode = () => {
  if (userInfo.value.inviteCode) {
    const copyText = `地址：https://www.lingxiangtools.top \n邀请码：${userInfo.value.inviteCode}
    `
    navigator.clipboard.writeText(copyText)
      .then(() => {
        message.success('邀请码已复制到剪贴板')
      })
      .catch(() => {
        message.error('复制失败，请手动复制')
      })
  }
}

// 获取用户信息
const fetchUserInfo = async () => {
  loading.value = true
  try {
    // 先从缓存中获取用户信息
    const cachedUserInfo = getUserInfo()
    console.log(cachedUserInfo, 'cache')
    if (cachedUserInfo) {
      userInfo.value = cachedUserInfo
      loading.value = false
      return
    }

    // 如果缓存中没有，则从API获取
    const response = await getUserInfoAPI()
    console.log(response)
    if (response.code === 200 && response.data) {
      const formattedUserInfo = {
        email: response.data.email || '',
        inviteCode: response.data.invite_code || '',
        createTime: response.data.create_time,
        isMember: !!response.data.is_member,
        memberCreatedTime: response.data.member_created_time,
        memberExpiredTime: response.data.member_expired_time,
        hasUsedTrial: response.data.has_used_trial,
        isPayUser: response.data.is_pay_user || false
      }
      userInfo.value = formattedUserInfo
      // 将用户信息存入缓存
      setUserInfo(formattedUserInfo)
    } else {
      message.error('获取用户信息失败')
      router.push('/user/login')
    }
  } catch (error) {
    console.error('Failed to fetch user info:', error)
    message.error('获取用户信息失败，请重新登录')
    router.push('/user/login')
  } finally {
    loading.value = false
  }
}

// 退出登录
const handleLogout = async () => {
  const res = await logoutAPI()
  if (res.code === 200) {
    clearAuthData()
    message.success('已退出登录')
    router.push('/user/login')
  } else {
    message.error('退出失败')
  }
}

// 返回首页
const goBack = () => {
  router.push('/home')
}

onMounted(() => {
  fetchUserInfo()
})
</script>

<style scoped>
.profile-info {
  padding: 24px;
}

.info-card {
  border-radius: 8px;
}

.card-header {
  font-size: 16px;
  font-weight: 500;
}

.loading-container {
  padding: 20px 0;
}

.info-list {
  margin: 20px 0;
}

.info-item {
  display: flex;
  margin-bottom: 16px;
  align-items: center;
}

.info-label {
  width: 80px;
  color: #606266;
  font-size: 14px;
}

.info-value {
  flex: 1;
  color: #303133;
  font-size: 14px;
}

.info-value.highlight {
  color: #e6a23c;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
}
</style> 