<template>
  <div class="subscription">
    <el-card class="subscription-card">
      <template #header>
        <div class="card-header">
          <span>会员订阅</span>
          <!-- 支付方式选择 -->
          <div class="payment-methods">
            <div class="payment-options">
              <div 
                class="payment-option" 
                :class="{ 'active': paymentMethod === 'wechat' }"
                @click="selectPayment('wechat')"
              >
                <i class="fab fa-weixin"></i>
                <span>微信支付</span>
              </div>
              <div 
                class="payment-option"
                :class="{ 'active': paymentMethod === 'alipay' }"
                @click="selectPayment('alipay')"
              >
                <i class="fab fa-alipay"></i>
                <span>支付宝</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>

      <div v-else class="subscription-content">
        <!-- 订阅套餐 -->
        <div class="plans-section">
          <h3 class="section-title">选择套餐</h3>
          <div class="plan-cards">
            <el-card 
              v-for="plan in subscriptionPlans" 
              :key="plan.id"
              class="plan-card"
              :class="{ 'active': selectedPlan === plan.id }"
              @click="selectPlan(plan.id)"
            >
              <h4 class="plan-title">{{ plan.name }}</h4>
              <div class="plan-price">
                <div class="price-header">
                  <span class="original-price">¥{{ plan.original_price }}</span>
                  <span class="discount-tag">{{ Math.round((1 - plan.discount_price/plan.original_price) * 100) }}% OFF</span>
                </div>
                <div class="main-price">
                  <span class="currency">¥</span>
                  <span class="amount">{{ plan.discount_price }}</span>
                  <span class="period">/{{ plan.duration_days === 30 ? '月' : plan.duration_days === 365 ? '年' : plan.duration_days + '天' }}</span>
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 支付按钮 -->
        <div class="action-section">
          <el-button 
            type="primary" 
            size="large" 
            :disabled="!selectedPlan"
            @click="handleSubscribe"
          >
            立即开通
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 支付二维码弹窗 -->
    <el-dialog
      v-model="qrDialogVisible"
      title="扫码支付"
      width="400px"
      :close-on-click-modal="false"
      :before-close="handleDialogClose"
    >
      <div class="qr-code-container">
        <img :src="qrCodeUrl" alt="支付二维码" class="qr-code-image" v-if="qrCodeUrl" />
        <div class="qr-code-tip">请使用{{ paymentMethod === 'wechat' ? '微信' : '支付宝' }}扫码支付</div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import message from '@/utils/message'
import { getPlansAPI, createOrderAPI, checkOrderStatusAPI, getUserInfoAPI } from '@/apis/system'
import { getOrFetchSubscriptionPlans } from '@/utils/subscription'
import { setUserInfoSession } from '@/utils/auth'
import { ElMessageBox } from 'element-plus'


const loading = ref(false)
const selectedPlan = ref(null)
const subscriptionPlans = ref([])
const paymentMethod = ref('wechat') // 默认选择微信支付

const qrDialogVisible = ref(false)
const qrCodeUrl = ref('')
const currentOrderId = ref(null)
let pollingTimer = null

const getPlans = async () => {
  try {
    loading.value = true
    const res = await getOrFetchSubscriptionPlans(() => getPlansAPI())
    subscriptionPlans.value = res.data || []
  } catch (error) {
    message.error('获取订阅套餐失败')
    console.error('Error fetching plans:', error)
  } finally {
    loading.value = false
  }
}

// 选择套餐
const selectPlan = (planId) => {
  selectedPlan.value = planId
}

// 选择支付方式
const selectPayment = (method) => {
  paymentMethod.value = method
}

// 开始轮询订单状态
const startPolling = async (orderId) => {
  if (pollingTimer) clearInterval(pollingTimer)
  
  const checkStatus = async () => {
    try {
      const response = await checkOrderStatusAPI(orderId)
      if (response.code === 200) {
        if (response.data.status === 'paid') {
          clearInterval(pollingTimer)
          qrDialogVisible.value = false
          message.success('订阅成功')
          // TODO: 这里需要调用用户提供的回调方法
          await setUserInfoSession(getUserInfoAPI)
        }
      }
    } catch (error) {
      console.error('检查订单状态失败:', error)
    }
  }

  // 立即执行一次
  await checkStatus()
  // 设置轮询间隔
  pollingTimer = setInterval(checkStatus, 1000)
}

// 处理弹窗关闭
const handleDialogClose = async (done) => {
  if (currentOrderId.value) {
    try {
      // 弹出确认对话框
      await ElMessageBox.confirm(
        '关闭窗口将取消当前订阅，您确定要关闭吗？',
        '提示',
        {
          confirmButtonText: '确认关闭',
          cancelButtonText: '继续支付',
          type: 'warning',
        }
      )
      
      // 用户确认关闭
      const response = await checkOrderStatusAPI(currentOrderId.value)
      if (response.code === 200) {
        if (response.data.status === 'paid') {
          message.success('订阅成功')
          // TODO: 这里需要调用用户提供的回调方法
        } else {
          message.info('已取消订阅')
        }
      }
      
      // 清除轮询定时器
      if (pollingTimer) {
        clearInterval(pollingTimer)
        pollingTimer = null
      }
      
      // 重置状态
      currentOrderId.value = null
      qrCodeUrl.value = ''
      done()
    } catch (error) {
      // 用户取消关闭，不执行任何操作
      if (error === 'cancel') {
        // 用户选择继续支付，不关闭弹窗
        return
      }
      console.error('检查订单状态失败:', error)
    }
  } else {
    // 没有订单ID，直接关闭
    done()
  }
}

// 处理订阅
const handleSubscribe = async () => {
  if (!selectedPlan.value) {
    message.warning('请选择订阅套餐')
    return
  }
  if (paymentMethod.value === "alipay") {
    message.info('支付宝支付功能开发中，敬请期待')
    return
  }
  try {
    const response = await createOrderAPI(selectedPlan.value, paymentMethod.value)
    if (response.code === 200) {
      // 显示支付二维码
      qrCodeUrl.value = response.data.QRcode_url
      currentOrderId.value = response.data.order_id
      qrDialogVisible.value = true
      // 开始轮询订单状态
      startPolling(response.data.order_id)
    } else {
      message.error(response.msg)
    }
  } catch (error) {
    message.error('创建订单失败')
  }
}

onMounted(() => {
  getPlans()
})

// 组件卸载前清理定时器
onBeforeUnmount(() => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
})
</script>

<style scoped>
.subscription {
  padding: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.subscription-card {
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.loading-container {
  padding: 20px 0;
}

.section-title {
  margin: 0 0 32px;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  text-align: center;
}

.plan-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.plan-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.plan-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.plan-card.active {
  border: 2px solid #409eff;
  transform: translateY(-6px);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
}

.plan-card.active::before {
  content: '推荐';
  position: absolute;
  top: 12px;
  right: -28px;
  background: #409eff;
  color: white;
  padding: 4px 30px;
  font-size: 12px;
  transform: rotate(45deg);
}

.plan-title {
  margin: 0 0 20px;
  font-size: 20px;
  color: #303133;
  text-align: center;
  font-weight: 600;
  padding-top: 8px;
}

.plan-price {
  text-align: center;
  padding: 20px 0;
  margin: 0 -20px;
}

.price-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 4px;
}

.original-price {
  font-size: 16px;
  color: #909399;
  text-decoration: line-through;
}

.discount-tag {
  background-color: #fff1f0;
  color: #ff4d4f;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.main-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.currency {
  font-size: 24px;
  color: #303133;
  font-weight: 500;
  margin-right: 2px;
}

.amount {
  font-size: 48px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.period {
  font-size: 16px;
  color: #909399;
  margin-left: 4px;
}

.plan-features {
  display: none;
}

.action-section {
  text-align: center;
  margin-top: 40px;
}

.action-section .el-button {
  min-width: 240px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 24px;
}

.payment-methods {
  margin-bottom: 0;
}

.payment-options {
  display: flex;
  gap: 12px;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.payment-option:hover {
  border-color: #409eff;
  color: #409eff;
}

.payment-option.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.payment-option i {
  font-size: 18px;
}

.payment-option span {
  font-size: 14px;
}

.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  margin-bottom: 16px;
}

.qr-code-tip {
  font-size: 14px;
  color: #606266;
  text-align: center;
}
</style> 