<template>
  <div class="video-matting" v-loading="loading" element-loading-text="处理中...">
    <div class="main-content">
      <!-- 选择文件和处理按钮 -->
      <div class="action-row">
        <div class="label">选择视频</div>
        <div class="controls">
          <UploadButton text="选择文件" size="default" @upload="handleSelectVideo" />
          <StartProcessButton 
            text="开始处理"
            :loading="isProcessing"
            :disabled="!selectedVideoPath || isProcessing"
            @startProcess="handleProcess"
          />
        </div>
      </div>

      <!-- AI模型选择 -->
      <div class="action-row">
        <div class="label">AI模型</div>
        <div class="controls">
          <el-radio-group v-model="selectedModel">
            <el-radio value="modnet">人像</el-radio>
            <el-radio value="rmbg14">快速</el-radio>
            <el-radio value="fast">标准</el-radio>
          </el-radio-group>
        </div>
      </div>

      <!-- 边缘优化 -->
      <div class="action-row">
        <div class="label">边缘优化</div>
        <div class="controls">
          <div class="edge-optimization-container">
            <el-tooltip
              content="值越大边缘越柔和，值越小边缘越锐化"
              placement="top"
            >
              <el-switch
                v-model="isEdgeOptimization"
                class="edge-switch"
              />
            </el-tooltip>
            <div v-show="isEdgeOptimization" class="edge-value-slider inline">
              <el-slider
                v-model="edgeValue"
                :min="0"
                :max="360"
                :step="1"
                :format-tooltip="(val) => `${val}°`"
                class="edge-slider"
              >
              </el-slider>
            </div>
          </div>
        </div>
      </div>

      <!-- 背景设置 -->
      <div class="action-row">
        <div class="label">背景设置</div>
        <div class="controls">
          <el-button @click="showBackgroundPicker = true">
            <div class="color-preview" :style="getColorPreviewStyle"></div>
            设置背景色
          </el-button>
          <!-- 显示已选择的背景图片预览 -->
          <div v-if="backgroundImage && !selectedColor.includes('gradient') && selectedColor !== 'transparent'" 
               class="selected-bg-preview" 
               @click="previewBackgroundImage">
            <img :src="backgroundUrl" alt="背景预览" class="bg-preview-img" />
            <div class="preview-overlay">
              <el-icon><ZoomIn /></el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 选择的文件路径 -->
      <div v-if="selectedVideoPath" class="selected-file">
        <p>已选择: {{ selectedVideoPath }}</p>
      </div>

      <!-- 处理进度 -->
      <div v-if="showProgress" class="progress-section">
        <div class="process-title">
          <h3>{{ statusInfo }}</h3>
          <p class="process-subtitle">请耐心等待，处理时间取决于视频长度...</p>
        </div>
        <el-progress
          :percentage="processPercentage"
          :status="progressStatus"
          :stroke-width="20"
        >
          <template #default="{ percentage }">
            <span class="percentage-value">{{ percentage.toFixed(2) }}%</span>
          </template>
        </el-progress>
      </div>

      <!-- 处理完成提示 -->
      <div v-if="isProcessComplete" class="complete-info">
          <el-button type="primary" @click="handleReset">处理新视频</el-button>
      </div>
    </div>

    <!-- 背景色选择器弹窗 -->
    <el-dialog
      v-model="showBackgroundPicker"
      title="设置背景色"
      width="480px"
      align-center
    >
      <div class="background-picker">
        <el-tabs>
          <el-tab-pane label="基础色">
            <div class="preset-colors">
              <div 
                v-for="color in presetColors" 
                :key="color"
                class="color-item"
                :class="{ active: selectedColor === color }"
                :style="getColorItemStyle(color)"
                @click="setBackground(color)"
              >
                <el-icon v-if="selectedColor === color"><Check /></el-icon>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="渐变色">
            <div class="gradient-colors">
              <div 
                v-for="gradient in gradientColors" 
                :key="gradient.name"
                class="gradient-item"
                :class="{ active: selectedColor === gradient.value }"
                :style="{ background: gradient.value }"
                @click="setBackground(gradient.value)"
              >
                <span class="gradient-name">{{ gradient.name }}</span>
                <el-icon v-if="selectedColor === gradient.value"><Check /></el-icon>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="图片背景">
            <div class="image-background">
              <el-button @click="handleSelectBackgroundImage">
                <el-icon><Picture /></el-icon>
                选择背景图片
              </el-button>
              <div v-if="backgroundImage" 
                   class="selected-bg-preview-large"
                   @click="previewBackgroundImage">
                <img :src="backgroundUrl" alt="背景预览" class="bg-preview-img" />
                <div class="preview-overlay">
                  <el-icon><ZoomIn /></el-icon>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div class="custom-color">
          <span class="label">自定义颜色：</span>
          <el-color-picker
            v-model="selectedColor"
            @change="setBackground"
            show-alpha
          />
        </div>
      </div>
    </el-dialog>

    <!-- 背景图片预览弹窗 -->
    <el-dialog
      v-model="showImagePreview"
      title="背景图片预览"
      width="70%"
      align-center
    >
      <div class="image-preview-container">
        <img :src="backgroundUrl" alt="背景图片" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { Picture, Check, ZoomIn } from '@element-plus/icons-vue'
import baseAPI from '@/apis/base'
import message from '@/utils/message'
import { videoDeWatermarkAPI } from '@/apis/video'
import { presetColors, gradientColors } from '@/config/colors'
import UploadButton from '@/components/UploadButton.vue'
import StartProcessButton from '@/components/StartProcessButton.vue'
// 状态变量
const loading = ref(false)
const selectedVideoPath = ref('')
const isProcessing = ref(false)
const showProgress = ref(false)
const processPercentage = ref(0)
const progressStatus = ref('')
const statusInfo = ref('')
const isProcessComplete = ref(false)
const selectedModel = ref('modnet')
const selectedColor = ref('transparent')
const backgroundImage = ref('')
const backgroundUrl = ref('')
const showBackgroundPicker = ref(false)
const showImagePreview = ref(false)
const isEdgeOptimization = ref(false)
const edgeValue = ref(90)

// 计算属性
const getColorPreviewStyle = computed(() => {
  // 如果有背景图片，显示背景图片
  if (backgroundImage.value && !selectedColor.value.includes('gradient') && selectedColor.value !== 'transparent') {
    return {
      backgroundImage: `url(${backgroundUrl.value})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center'
    }
  }
  
  // 检查是否是渐变色
  if (selectedColor.value && selectedColor.value.includes('gradient')) {
    return {
      background: selectedColor.value
    }
  }
  
  return getColorItemStyle(selectedColor.value)
})

// 获取视频信息
const getVideoInfo = async () => {
  loading.value = true
  try {
    const response = await videoDeWatermarkAPI('get_video_info', selectedVideoPath.value)
    if (response.code !== 200) {
      throw new Error(response.msg)
    }
    console.log('Video info:', response.data)
  } catch (error) {
    message.error(error.message)
  } finally {
    loading.value = false
  }
}

// 选择视频文件
const handleSelectVideo = async () => {
  loading.value = true
  try {
    const response = await baseAPI('open_video_file', false)
    const selected_files = response.data['selected_files']
    if (selected_files.length > 0) {
      selectedVideoPath.value = selected_files[0]
      await getVideoInfo()
      message.success('视频选择成功')
    } else {
      message.warning('未选择文件')
    }
  } catch (error) {
    message.error(error.message)
  } finally {
    loading.value = false
  }
}

// 选择背景图片
const handleSelectBackgroundImage = async () => {
  loading.value = true
  try {
    const response = await baseAPI('open_img_file', false)
    if (response.code === 200 && response.data.selected_file) {
      console.log(response.data)
      backgroundUrl.value = response.data.image_url
      backgroundImage.value = response.data.selected_file
      // 设置为图片背景模式
      selectedColor.value = 'image'
      message.success('背景图片选择成功')
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}

// 预览背景图片
const previewBackgroundImage = () => {
  if (backgroundUrl.value) {
    showImagePreview.value = true
  }
}

// 设置背景
const setBackground = (color) => {
  selectedColor.value = color
  // 如果选择了非图片背景，清除背景图片
  if (color !== 'image') {
    backgroundImage.value = ''
    backgroundUrl.value = ''
  }
  showBackgroundPicker.value = false
}

// 添加颜色项样式计算方法
const getColorItemStyle = (color) => {
  const style = {
    backgroundColor: color === 'transparent' ? 'transparent' : color,
  }
  
  if (color === 'transparent') {
    style.backgroundImage = `
      linear-gradient(45deg, #ccc 25%, transparent 25%),
      linear-gradient(-45deg, #ccc 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #ccc 75%),
      linear-gradient(-45deg, transparent 75%, #ccc 75%)
    `
    style.backgroundSize = '10px 10px'
    style.backgroundPosition = '0 0, 0 5px, 5px -5px, -5px 0px'
  }
  
  return style
}

// 开始处理视频
const handleProcess = async () => {
  if (!selectedVideoPath.value || !selectedModel.value) return
  loading.value = true
  
  try {
    isProcessing.value = true
    showProgress.value = true
    processPercentage.value = 0
    progressStatus.value = ''
    statusInfo.value = '正在处理中...'

    // 准备请求参数
    const params = {
      video_path: selectedVideoPath.value,
      ai_model: selectedModel.value,
      background_color: selectedColor.value === 'image' ? 'transparent' : selectedColor.value,
      is_edge_optimization: isEdgeOptimization.value,
      edge_value: edgeValue.value
    }

    // 如果是图片背景，添加背景图片路径
    if (selectedColor.value === 'image' && backgroundImage.value) {
      params['background_color_image'] = backgroundImage.value
    }

    const response = await videoDeWatermarkAPI('auto_matting_video', params)

    if (response.code !== 200) {
      throw new Error(response.msg)
    }
  } catch (error) {
    console.error('处理失败:', error)
    progressStatus.value = 'exception'  
    message.error('处理失败: ' + error.message)
    isProcessing.value = false
    showProgress.value = false
  } finally {
    loading.value = false
  }
}

// 重置状态
const handleReset = () => {
  selectedVideoPath.value = ''
  isProcessing.value = false
  showProgress.value = false
  processPercentage.value = 0
  progressStatus.value = ''
  statusInfo.value = ''
  isProcessComplete.value = false
  // 保留用户的背景设置
}

// 处理进度事件
const handleProgress = (event) => {
  const progressData = event.detail
  const progress = parseFloat(progressData.progress) || 0
  const status = progressData.message || ''
  
  processPercentage.value = progress
  statusInfo.value = status
  
  if (progress === 100) {
    progressStatus.value = 'success'
    isProcessComplete.value = true
    isProcessing.value = false
  } else if (progress === 0 && status.includes('失败')) {
    progressStatus.value = 'exception'
    message.error(status)
    isProcessing.value = false
  }
}

// 组件挂载时添加进度监听
onMounted(() => {
  window.addEventListener('ProcessProgress', handleProgress)
})

// 组件卸载时移除监听
onUnmounted(() => {
  window.removeEventListener('ProcessProgress', handleProgress)
})
</script>

<style lang="scss" scoped>
.video-matting {
  // 添加透明 loading 遮罩的样式
  :deep(.el-loading-mask) {
    background-color: rgba(255, 255, 255, 0.3);
  }

  .main-content {
    max-width: 800px;
  }

  .action-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .label {
      width: 80px;
      color: var(--el-text-color-regular);
      font-size: 15px;
    }

    .controls {
      flex: 1;
      display: flex;
      gap: 12px;
      align-items: center;

      .el-radio-group {
        display: flex;
        gap: 15px;
      }

      .edge-optimization-container {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .edge-value-slider {
          width: 180px;
          margin-left: 16px;
        }
      }

      .selected-bg-preview {
        margin-left: 12px;
        width: 60px;
        height: 40px;
        border: 1px solid var(--el-border-color);
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        cursor: pointer;
        
        &:hover .preview-overlay {
          opacity: 1;
        }
        
        .bg-preview-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .preview-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.4);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s;
          
          .el-icon {
            color: white;
            font-size: 20px;
          }
        }
      }
    }
  }

  .selected-file {
    margin: 16px 0;
    padding: 12px 16px;
    background-color: var(--el-fill-color-light);
    border-radius: 4px;
    
    p {
      margin: 0;
      color: var(--el-text-color-regular);
      font-size: 14px;
      word-break: break-all;
    }
  }

  .progress-section {
    margin-top: 32px;

    .process-title {
      text-align: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        color: var(--el-color-primary);
        font-size: 24px;
      }

      .process-subtitle {
        margin: 8px 0 0;
        color: #909399;
        font-size: 14px;
      }
    }
    .el-progress {
      margin-bottom: 12px;
    }

    .status-info {
      color: var(--el-text-color-regular);
      font-size: 14px;
      margin-top: 8px;
    }

    .percentage-value {
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
  }

  .complete-info {
    margin-top: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // 添加颜色预览样式
  .color-preview {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 4px;
    margin-right: 8px;
    vertical-align: middle;
    border: 1px solid var(--el-border-color-lighter);
  }
}

// 背景选择器样式
.background-picker {
  .preset-colors {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
    margin-bottom: 20px;
    
    .color-item {
      position: relative;
      width: 64px;
      height: 64px;
      border-radius: 8px;
      cursor: pointer;
      border: 2px solid #e4e7ed;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
      
      &:hover {
        transform: scale(1.05);
      }
      
      &.active {
        border-color: var(--el-color-primary);
        
        .el-icon {
          color: #fff;
          filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
        }
      }
    }
  }

  .gradient-colors {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
    margin-bottom: 20px;
    
    .gradient-item {
      position: relative;
      width: 64px;
      height: 64px;
      border-radius: 8px;
      cursor: pointer;
      border: 2px solid #e4e7ed;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
      
      &:hover {
        transform: scale(1.05);
      }
      
      &.active {
        border-color: var(--el-color-primary);
      }

      .gradient-name {
        color: #fff;
        text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
        font-size: 12px;
        position: absolute;
        bottom: 4px;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
      }

      .el-icon {
        color: #fff;
        filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
      }
    }
  }

  .image-background {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;

    .selected-bg-preview-large {
      width: 100%;
      height: 150px;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      overflow: hidden;
      position: relative;
      cursor: pointer;
      
      &:hover .preview-overlay {
        opacity: 1;
      }
      
      .bg-preview-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .preview-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.4);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s;
        
        .el-icon {
          color: white;
          font-size: 32px;
        }
      }
    }
  }

  .custom-color {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding-top: 16px;
    margin-top: 8px;
    border-top: 1px solid var(--el-border-color-lighter);

    .label {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
}

// 图片预览弹窗样式
.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  
  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }
}
</style> 