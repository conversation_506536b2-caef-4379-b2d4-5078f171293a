<template>
  <div class="inpainting-container">
    <PageHeader
      title="AI智能擦除"
      description="基于先进的AI技术，智能擦除图片中不需要的内容，支持单张和批量处理，让图片处理更简单。"
    />

    <div class="tab-header">
      <div class="el-tabs">
        <div class="el-tabs__header">
          <div class="el-tabs__nav-wrap">
            <div class="el-tabs__nav">
              <div
                class="el-tabs__item"
                :class="{ 'is-active': activeTab === 'single' }"
                @click="handleTabChange('single')"
              >
                单张处理
              </div>
              <div
                class="el-tabs__item"
                :class="{ 'is-active': activeTab === 'batch' }"
                @click="handleTabChange('batch')"
              >
                批量处理
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="tab-content">
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import PageHeader from '@/components/PageHeader.vue'

const router = useRouter()
const route = useRoute()

const activeTab = ref('single')

// 根据当前路由设置激活的标签
const updateActiveTab = () => {
  const path = route.path
  if (path.includes('/batch')) {
    activeTab.value = 'batch'
  } else {
    activeTab.value = 'single'
  }
}

onMounted(() => {
  updateActiveTab()
})

// 监听路由变化
watch(() => route.path, () => {
  updateActiveTab()
})

// 处理标签切换
const handleTabChange = (value) => {
  if (value === 'single') {
    router.push('/inpainting/single')
  } else {
    router.push('/inpainting/batch')
  }
}
</script>

<style lang="scss" scoped>
.inpainting-container {
  .tab-header {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    padding: 0 20px;

    .el-tabs {
      .el-tabs__header {
        margin: 0;
        border-bottom: 1px solid var(--el-border-color-light);
      }

      .el-tabs__nav {
        display: flex;
        position: relative;
      }

      .el-tabs__item {
        padding: 0 20px;
        height: 40px;
        line-height: 40px;
        cursor: pointer;
        position: relative;
        font-size: 14px;
        color: var(--el-text-color-regular);
        transition: color 0.3s;

        &:hover {
          color: var(--el-color-primary);
        }

        &.is-active {
          color: var(--el-color-primary);
          
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--el-color-primary);
          }
        }
      }
    }
  }

  .tab-content {
    min-height: 200px;
  }
}
</style>
