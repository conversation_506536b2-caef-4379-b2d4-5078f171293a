<template>
  <div 
    class="single-inpainting"
    @dragover.prevent
    @drop="handleDrop"
  >
    <!-- 示例图片区域 -->
    <div v-if="!originalImage" class="example-section">
      <!-- 添加上传按钮 -->
      <div class="upload-button">
        <UploadButton 
          text="选择图片"
          @upload="handleClickUpload"
        />
      </div>

      <!-- 提示文字 -->
      <div class="upload-tip">
        可拖放、粘贴图片、粘贴链接，支持 jpg/png/webp/bmp, 点击图片快速体验
      </div>

      <div class="example-grid">
        <div v-for="(example, index) in exampleImages" :key="index" class="example-item"
          @click="loadExampleImage(example)">
          <el-image :src="example" fit="cover" loading="lazy" class="example-image" />
        </div>
      </div>
    </div>

    <!-- 编辑区域 -->
    <div v-else class="edit-section">
      <!-- 修改模型切换按钮组 -->
      <div class="model-switch">
        <el-button-group>
          <el-button :type="modelType === 'fast' ? 'primary' : 'default'" @click="modelType = 'fast'" size="default">
            <el-icon class="el-icon--left">
              <Lightning />
            </el-icon>
            快速
          </el-button>
          <el-button :type="modelType === 'quality' ? 'primary' : 'default'" @click="modelType = 'quality'"
            size="default">
            <el-icon class="el-icon--left">
              <Star />
            </el-icon>
            高质量
          </el-button>
        </el-button-group>
      </div>

      <div class="editor-container" v-loading="loading">
        <div class="canvas-area">
          <div class="canvas-wrapper" 
            @mouseleave="handleMouseLeave" 
            @mouseenter="showBrushPreview = true"
          >
            <canvas ref="imageCanvas" class="base-canvas"></canvas>
            <canvas ref="maskCanvas" class="mask-canvas" 
              @mousedown="startDrawing" 
              @mousemove="handleMouseMove"
              @mouseup="stopDrawing" 
              @mouseleave="stopDrawing"
            ></canvas>
            <!-- Brush preview -->
            <div v-if="showBrushPreview" class="brush-preview" :style="{
              width: `${brushSize}px`,
              height: `${brushSize}px`,
              left: `${cursorX}px`,
              top: `${cursorY}px`,
            }">
            </div>
          </div>
        </div>

        <!-- Toolbar -->
        <div class="toolbar-container">
          <div class="toolbar">
            <el-button-group class="action-group">
              <el-tooltip content="撤销 (Ctrl+Z)" placement="top">
                <el-button @click="undo" :icon="RefreshLeft" class="action-button" :disabled="historyIndex <= 0" />
              </el-tooltip>
              <el-tooltip content="重做 (Ctrl+Shift+Z)" placement="top">
                <el-button @click="redo" :icon="RefreshRight" class="action-button"
                  :disabled="historyIndex >= historyMask.length - 1" />
              </el-tooltip>
            </el-button-group>

            <div class="brush-size">
              <el-tooltip content="使用 [ ] 调整画笔大小" placement="top">
                <el-slider v-model="brushSize" :min="1" :max="200" :show-tooltip="true" class="brush-slider" />
              </el-tooltip>
            </div>

            <el-button-group class="action-group">
              <el-tooltip content="清除 (Delete)" placement="top">
                <el-button @click="clearMask" :icon="Delete" class="action-button" />
              </el-tooltip>
              <el-tooltip content="处理 (Enter)" placement="top">
                <el-button @click="processImage" :icon="Check" class="action-button save-button" :loading="loading" />
              </el-tooltip>
              <!-- 重选 -->
              <el-tooltip content="重选 (Ctrl+R)" placement="top">
                <el-button @click="resetImage" :icon="Back" class="action-button" />
              </el-tooltip>
              <!-- 导出 -->
              <el-tooltip content="导出 (Ctrl+S)" placement="top">
                <el-button @click="downloadImage" :icon="Download" class="action-button" />
              </el-tooltip>
              <el-tooltip placement="top">
                <template #content>
                  <div class="shortcut-help">
                    <div>快捷键说明:</div>
                    <div>Ctrl+Z: 撤销</div>
                    <div>Ctrl+Shift+Z: 重做</div>
                    <div>Delete: 清除</div>
                    <div>Enter: 处理</div>
                    <div>[ ]: 调整画笔大小</div>
                    <div>Ctrl+R: 重选</div>
                    <div>Ctrl+S: 导出</div>
                  </div>
                </template>
                <el-button :icon="QuestionFilled" class="action-button" />
              </el-tooltip>
            </el-button-group>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { RefreshLeft, RefreshRight, Delete, Check, QuestionFilled, Upload, Picture, Back, Lightning, Star, Download } from '@element-plus/icons-vue';
import { inpaintingAPI } from '@/apis/inpainting'
import imageList from '@/utils/demo'
import message from '@/utils/message'
import baseAPI from '@/apis/base'
import { getImgBase64API } from '@/apis/common'
import UploadButton from '@/components/UploadButton.vue'


const modelType = ref('fast')
const processing = ref(false)
const exampleImages = imageList


// 基础状态
const imageCanvas = ref(null);
const maskCanvas = ref(null);
const originalImage = ref(null);

const loading = ref(false);

// 画笔相关状态
const brushSize = ref(40);
const isDrawing = ref(false);
const showBrushPreview = ref(false);
const cursorX = ref(0);
const cursorY = ref(0);

// 历史记录
const historyMask = ref([]);
const historyIndex = ref(-1);
const imageHistory = ref([]);
const historyHighQuality = ref([]);

// 添加新的状态来保存原始尺寸
const originalWidth = ref(0);
const originalHeight = ref(0);

// 监听originalImage
watch(originalImage, (newVal) => {
  if (newVal) {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    // 先设置onload，再设置src
    img.onload = () => {
      originalImage.value = img; // 保存已加载的图片对象
      initCanvas(img);
    };  
    img.src = newVal;
  }
})

// 添加防抖函数
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// 添加重置画布函数，使用防抖处理
const resizeCanvas = debounce(() => {
  if (originalImage.value) {
    initCanvas(originalImage.value);
  }
}, 200); // 200ms的防抖延迟

// 初始化画布
const initCanvas = (img) => {
  if (!img || !imageCanvas.value || !maskCanvas.value) return;

  // 获取设备像素比
  const dpr = window.devicePixelRatio || 1;
  
  // 计算画布大小 - 自适应屏幕
  const canvasWrapper = imageCanvas.value.parentElement;
  const canvasArea = document.querySelector('.canvas-area');
  
  if (!canvasArea) return;
  
  // 获取实际可用空间
  const canvasAreaRect = canvasArea.getBoundingClientRect();
  
  // 计算可用空间，考虑到padding (20px)
  const availableWidth = canvasAreaRect.width - 40;
  const availableHeight = canvasAreaRect.height - 40;
  
  // 保持图像比例，确保完整显示
  let width, height;
  const imgRatio = img.width / img.height;
  
  if (imgRatio > availableWidth / availableHeight) {
    // 图片更宽，以宽度为基准
    width = Math.min(availableWidth, img.width);
    height = width / imgRatio;
  } else {
    // 图片更高，以高度为基准
    height = Math.min(availableHeight, img.height);
    width = height * imgRatio;
  }
  
  // 设置画布尺寸，考虑设备像素比
  [imageCanvas.value, maskCanvas.value].forEach(canvas => {
    // 设置CSS尺寸
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;
    
    // 设置画布实际像素尺寸
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    
    // 设置更好的图像渲染质量
    const ctx = canvas.getContext('2d');
    ctx.setTransform(1, 0, 0, 1, 0, 0); // 重置变换
    ctx.scale(dpr, dpr);  // 应用设备像素比缩放
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
  });

  // 绘制原始图像
  renderImage(img);

  // 设置遮罩画布样式
  const maskCtx = maskCanvas.value.getContext('2d');
  maskCtx.fillStyle = 'rgba(255, 0, 0, 0.5)';

  // 保存原始尺寸
  originalWidth.value = img.width;
  originalHeight.value = img.height;

  saveState(img.src);
};

// 鼠标事件处理
const handleMouseMove = (event) => {
  updateCursor(event);
  if (isDrawing.value) {
    draw(event);
  }
};

const updateCursor = (event) => {
  const rect = event.target.getBoundingClientRect();
  cursorX.value = event.clientX - rect.left;
  cursorY.value = event.clientY - rect.top;
};

const handleMouseLeave = () => {
  showBrushPreview.value = false;
  stopDrawing();
};

// 绘制相关函数
const startDrawing = (event) => {
  isDrawing.value = true;
  draw(event);
};

const draw = (event) => {
  const ctx = maskCanvas.value.getContext('2d');
  const rect = event.target.getBoundingClientRect();
  const dpr = window.devicePixelRatio || 1;
  
  // 计算正确的坐标，考虑设备像素比
  const x = event.clientX - rect.left;
  const y = event.clientY - rect.top;
  
  // 由于我们已经在initCanvas中应用了dpr缩放，此处不需要再乘以dpr
  ctx.beginPath();
  ctx.arc(x, y, brushSize.value / 2, 0, Math.PI * 2);
  ctx.fill();
};

const stopDrawing = () => {
  if (isDrawing.value) {
    isDrawing.value = false;
    saveState();
  }
};

// 历史记录相关
const saveState = (highQuality = '') => {
  const maskCtx = maskCanvas.value.getContext('2d');
  const imageCtx = imageCanvas.value.getContext('2d');
  
  // 保存当前变换
  maskCtx.save();
  imageCtx.save();
  
  // 清除缩放以获取原始数据
  maskCtx.setTransform(1, 0, 0, 1, 0, 0);
  imageCtx.setTransform(1, 0, 0, 1, 0, 0);
  
  // 保存遮罩状态
  const maskData = maskCtx.getImageData(0, 0, maskCanvas.value.width, maskCanvas.value.height);
  // 保存图像状态
  const imageData = imageCtx.getImageData(0, 0, imageCanvas.value.width, imageCanvas.value.height);
  
  // 恢复变换
  maskCtx.restore();
  imageCtx.restore();

  // 清除当前位置之后的历史
  historyMask.value = historyMask.value.slice(0, historyIndex.value + 1);
  imageHistory.value = imageHistory.value.slice(0, historyIndex.value + 1);
  historyHighQuality.value = historyHighQuality.value.slice(0, historyIndex.value + 1);

  if (highQuality) {
    historyHighQuality.value.push(highQuality);
  } else {
    const tem = historyHighQuality.value[historyHighQuality.value.length - 1];
    historyHighQuality.value.push(tem);
  }

  // 添加新状态
  historyMask.value.push(maskData);
  imageHistory.value.push(imageData);
  historyIndex.value++;
};

const undo = () => {
  if (historyIndex.value > 0) {
    historyIndex.value--;
    const maskCtx = maskCanvas.value.getContext('2d');
    const imageCtx = imageCanvas.value.getContext('2d');
    
    // 保存当前变换
    const dpr = window.devicePixelRatio || 1;
    
    // 恢复之前的上下文状态
    maskCtx.save();
    imageCtx.save();
    
    // 清除缩放
    maskCtx.setTransform(1, 0, 0, 1, 0, 0);
    imageCtx.setTransform(1, 0, 0, 1, 0, 0);
    
    // 放入图像数据
    maskCtx.putImageData(historyMask.value[historyIndex.value], 0, 0);
    imageCtx.putImageData(imageHistory.value[historyIndex.value], 0, 0);
    
    // 恢复之前的上下文状态
    maskCtx.restore();
    imageCtx.restore();
  }
};

// 添加 redo 函数
const redo = () => {
  if (historyIndex.value < historyMask.value.length - 1) {
    historyIndex.value++;
    const maskCtx = maskCanvas.value.getContext('2d');
    const imageCtx = imageCanvas.value.getContext('2d');
    
    // 保存当前变换
    const dpr = window.devicePixelRatio || 1;
    
    // 恢复之前的上下文状态
    maskCtx.save();
    imageCtx.save();
    
    // 清除缩放
    maskCtx.setTransform(1, 0, 0, 1, 0, 0);
    imageCtx.setTransform(1, 0, 0, 1, 0, 0);
    
    // 放入图像数据
    maskCtx.putImageData(historyMask.value[historyIndex.value], 0, 0);
    imageCtx.putImageData(imageHistory.value[historyIndex.value], 0, 0);
    
    // 恢复之前的上下文状态
    maskCtx.restore();
    imageCtx.restore();
  }
};

// 图像处理相关
const renderImage = (img) => {
  const ctx = imageCanvas.value.getContext('2d');
  const dpr = window.devicePixelRatio || 1;
  
  // 使用CSS尺寸进行清除和绘制（已经应用了dpr缩放）
  ctx.clearRect(0, 0, imageCanvas.value.width / dpr, imageCanvas.value.height / dpr);
  try {
    ctx.drawImage(img, 0, 0, imageCanvas.value.width / dpr, imageCanvas.value.height / dpr);
  } catch (error) {
    console.error('Error rendering image:', error);
    message.error('图片渲染失败');
  }
};

const clearMask = () => {
  const ctx = maskCanvas.value.getContext('2d');
  const dpr = window.devicePixelRatio || 1;
  
  // 使用正确的尺寸清除遮罩
  ctx.clearRect(0, 0, maskCanvas.value.width / dpr, maskCanvas.value.height / dpr);
  saveState();
};



// API相关函数
const processImage = async () => {
  loading.value = true;
  try {
    // 创建临时画布以输出原始尺寸的遮罩
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = originalWidth.value;
    tempCanvas.height = originalHeight.value;
    const tempCtx = tempCanvas.getContext('2d');

    // 设置更好的图像渲染质量
    tempCtx.imageSmoothingEnabled = true;
    tempCtx.imageSmoothingQuality = 'high';

    // 保存当前maskCanvas的上下文状态
    const maskCtx = maskCanvas.value.getContext('2d');
    maskCtx.save();
    
    // 清除缩放，以便正确获取原始数据
    maskCtx.setTransform(1, 0, 0, 1, 0, 0);

    // 将当前遮罩缩放到原始尺寸
    tempCtx.drawImage(
      maskCanvas.value,
      0, 0, maskCanvas.value.width, maskCanvas.value.height,
      0, 0, originalWidth.value, originalHeight.value
    );
    
    // 恢复maskCanvas的上下文状态
    maskCtx.restore();

    const result = await inpaintingAPI('inpainting', {
      origin_image: historyHighQuality.value[historyHighQuality.value.length - 1],
      mask_base64: tempCanvas.toDataURL('image/png', 1.0), // 使用最高质量的PNG
      model_type: modelType.value
    });

    if (result.code === 200) {
      const img = new Image();
      img.onload = () => {
        originalImage.value = img;
        renderImage(img);
        clearMask();
        saveState(result.data.result_base64);
        message.success('处理成功');
      };
      img.src = result.data.result_base64;
    }
  } catch (error) {
    message.error('处理失败');
  } finally {
    loading.value = false;
  }
};

const downloadImage = async () => {
  try {
    
    const response = await baseAPI('save_image_dialog', {
      base64_data: historyHighQuality.value[historyIndex.value], // 使用最高质量的PNG
      origin_image: originalImage.value.src
    });

    if (response.code === 200) {
      message.success('导出成功');
    }
  } catch (error) {
    message.error('导出失败');
  }
};


// Keyboard event handler
const handleKeyboard = (event) => {
  if ((event.ctrlKey || event.metaKey) && !event.shiftKey && event.key === 'z') {
    event.preventDefault();
    undo();
  }

  if ((event.ctrlKey || event.metaKey) &&
    ((event.shiftKey && event.key === 'z') || event.key === 'y')) {
    event.preventDefault();
    redo();
  }

  // Add new shortcuts
  // Delete/Backspace to clear mask
  if (event.key === 'Delete' || event.key === 'Backspace') {
    event.preventDefault();
    clearMask();
  }

  // Enter or Ctrl/Cmd + S to save
  if (event.key === 'Enter') {
    event.preventDefault();
    processImage();
  }

  // [ and ] to adjust brush size
  if (event.key === '[') {
    event.preventDefault();
    brushSize.value = Math.max(1, brushSize.value - 5);
  }
  if (event.key === ']') {
    event.preventDefault();
    brushSize.value = Math.min(200, brushSize.value + 5);
  }
  // ctrl/cmd + r to reset
  if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
    event.preventDefault();
    resetImage();
  }
  // ctrl/cmd + s to export
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault();
    downloadImage();
  }

};


// 文件上传相关方法
const handleClickUpload = async () => {
  loading.value = true
  try {
    const response = await baseAPI('open_img_file', false)
    if (response.code === 200 && response.data.selected_file) {
      originalImage.value = response.data.image_url
      message.success('上传成功')
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}

// 加载示例图片
const loadExampleImage = (example) => {
  originalImage.value = example
}

// 重置所有状态
const resetImage = () => {
  originalImage.value = null
  modelType.value = 'fast'
  processing.value = false
  clearMask();
  imageHistory.value = [];
  historyMask.value = [];
  historyIndex.value = -1;
}


// 处理拖拽事件
const handleDrop = async (e) => {
  e.preventDefault();
  const file = e.dataTransfer?.files[0]
  if (!file) return
  
  // 检查文件类型支持jpg png webp gif bmp tiff
  const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/jpg', 'image/bmp']
  if (!validTypes.includes(file.type)) {
    message.error('只支持 JPG、PNG、WebP、GIF、BMP 格式的图片')
    return
  }

  loading.value = true
  try {
    if (file.type.match('image.*')) {
      const reader = new FileReader();
      reader.onload = function (e) {
        originalImage.value = e.target.result
        message.success('上传成功')
      };
      reader.readAsDataURL(file);
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}


// 验证是否是有效的URL
const isValidUrl = (string) => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};

// 处理粘贴事件
const handlePaste = (event) => {
  event.preventDefault();
  const clipboardData = event.clipboardData || window.Clipboard;
  const items = clipboardData.items;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    if (item.kind === 'string') {
      loading.value = true
      item.getAsString((text) => {
        // 判断是不是base64
        if (text.startsWith('data:image/')) {
          console.log('粘贴的是base64:', text);
          originalImage.value = text
          loading.value = false
        } else if (isValidUrl(text)) {
          console.log('粘贴的是URL:', text);
          getImgBase64API(text).then(res => {
            if (res.code === 200) {
              originalImage.value = res.data.base64_image
            } else {
              const errorMsg = res.msg || '获取图片失败';
              message.error(`获取图片失败: ${errorMsg}`);
              console.error('获取图片失败:', res);
            }
          }).catch(error => {
            message.error(`获取图片失败: ${error.message || '未知错误'}`);
            console.error('API调用失败:', error);
          }).finally(() => {
            loading.value = false
          });
        } else {
          loading.value = false
        }
      });
    } else if (item.kind === 'file') {
      const type = item.type.split('/')[0];
      if (!['image'].includes(type)) {
        message.error('请粘贴图片文件');
        return;
      }
      loading.value = true
      const file = item.getAsFile();
      const reader = new FileReader();
      reader.onload = (e) => {
        console.log('粘贴的是图片:', e.target.result);
        originalImage.value = e.target.result
        loading.value = false
      };
      reader.readAsDataURL(file);
    }
  }
};

onMounted(() => {
  window.addEventListener('paste', handlePaste)
  window.addEventListener('keydown', handleKeyboard);
  // 添加窗口大小改变的监听器
  window.addEventListener('resize', resizeCanvas);
})

onUnmounted(() => {
  window.removeEventListener('paste', handlePaste)
  window.removeEventListener('keydown', handleKeyboard);
  // 移除窗口大小改变的监听器
  window.removeEventListener('resize', resizeCanvas);
})
</script>

<style lang="scss" scoped>
.single-inpainting {
  background: var(--el-bg-color);
  border-radius: 8px;
  position: relative;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .edit-section {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;

    .model-switch {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;

      .el-button-group {
        .el-button {
          padding: 8px 16px;
          font-weight: 500;

          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }
  }

  .example-section {
    .upload-button {
      text-align: center;
      margin: 16px 0 12px;
    }

    .upload-tip {
      text-align: center;
      color: var(--el-text-color-secondary);
      font-size: 13px;
      margin: 12px 0;
      line-height: 1.4;
    }

    .example-grid {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin: 0 auto;
      max-width: 1200px;
      padding: 8px 0;

      .example-item {
        cursor: pointer;
        width: 250px;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .example-image {
          width: 100%;
          height: 200px;
          border-radius: 8px;
          object-fit: cover;
          margin-left: 12px;
        }
      }
    }
  }
}

.editor-container {
  height: calc(100vh - 180px); /* 减去顶部和底部空间 */
  min-height: 500px; /* 增加最小高度，更适合桌面使用 */
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.canvas-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background-color: #f5f5f5; /* 更浅的背景色，减少视觉干扰 */
}

.canvas-wrapper {
  position: relative;
  display: inline-block;
  border-radius: 6px; /* 稍微增大圆角 */
  margin: 0 auto;
  max-width: 100%;
  max-height: 100%;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); /* 增强阴影，提高视觉区分度 */
}

.base-canvas {
  display: block;
  background-color: white;
  border-radius: 6px; /* 匹配wrapper的圆角 */
}

.mask-canvas {
  position: absolute;
  top: 0;
  left: 0;
  cursor: crosshair;
  border-radius: 6px; /* 匹配wrapper的圆角 */
}

.brush-preview {
  position: absolute;
  border: 2px solid #fff;
  border-radius: 50%;
  pointer-events: none;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.3);
  background-color: rgba(255, 0, 0, 0.15); /* 稍微提高透明度 */
  z-index: 10;
}

.toolbar-container {
  margin-top: 20px;
  margin-bottom: 20px; /* 添加底部边距 */
  display: flex;
  justify-content: center;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 16px; /* 增加间距 */
  background: #555; /* 稍微变亮的背景色 */
  border-radius: 8px; /* 增大圆角 */
  padding: 10px 16px; /* 增加内边距 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2); /* 增强阴影 */
}

.brush-size {
  width: 240px; /* 增加宽度 */
  margin: 0 8px;
}

.brush-slider {
  margin: 0;
}

.brush-slider :deep(.el-slider__runway) {
  height: 3px; /* 稍微加粗滑块轨道 */
  margin: 14px 0;
  background-color: rgba(255, 255, 255, 0.25);
}

.brush-slider :deep(.el-slider__bar) {
  height: 3px; /* 匹配轨道高度 */
  background-color: #fff;
}

.brush-slider :deep(.el-slider__button-wrapper) {
  width: 14px; /* 增大滑块尺寸 */
  height: 14px;
  top: 2px;
}

.brush-slider :deep(.el-slider__button) {
  width: 14px; /* 增大滑块尺寸 */
  height: 14px;
  background-color: #fff;
  border: none;
}

.action-group {
  display: flex;
  gap: 4px; /* 添加按钮间距 */
}

.action-button {
  width: 36px !important; /* 增大按钮尺寸 */
  height: 36px !important;
  padding: 8px !important;
  border: none !important;
  background: transparent !important;
  transition: all 0.2s ease;
}

.action-button:hover {
  background: rgba(255, 255, 255, 0.15) !important; /* 稍微增强悬停效果 */
  transform: translateY(-1px); /* 添加轻微上浮效果 */
}

.action-button :deep(.el-icon) {
  color: #fff;
  font-size: 16px; /* 增大图标尺寸 */
}

.action-button.save-button {
  background: transparent !important;
}

.action-button.save-button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.action-button:disabled {
  opacity: 0.35;
}

:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5px);
}

.shortcut-help {
  font-size: 12px;
  line-height: 1.8;
  color: #fff;
  text-align: left;
}
</style>