<template>
  <div class="batch-inpainting">
    <el-loading v-model:visible="loading" :lock="true" text="处理中..." />
    <!-- 步骤条 -->
    <el-steps :active="currentStep" finish-status="success" class="steps">
      <el-step title="选择文件夹" />
      <el-step title="绘制蒙版" />
      <el-step title="选择模型" />
      <el-step title="批量处理" />
    </el-steps>

    <!-- 步骤1: 选择文件夹 -->
    <div v-if="currentStep === 0" class="step-content">
      <div class="folder-select">
        <!-- 输入文件夹选择 -->
        <el-form label-position="top">
          <el-form-item label="输入文件夹">
            <el-tooltip :content="inputFolder ? inputFolder : '选择需要处理的图片文件夹'" placement="top" effect="light">
            <el-input
              v-model="inputFolder"
              placeholder="选择需要处理的图片文件夹"
              readonly
            >
              <template #append>
                <el-button @click="selectFolder">
                  <el-icon><Folder /></el-icon>
                  选择
                </el-button>
              </template>
            </el-input>
            </el-tooltip>
          </el-form-item>

          <!-- 输出文件夹选择 -->
          <el-form-item label="输出文件夹">
            <el-tooltip :content="outputFolder ? outputFolder : '选择结果保存位置，默认在输入目录下创建result文件夹'" placement="bottom" effect="light">

            <el-input
              v-model="outputFolder"
              placeholder="选择结果保存位置，默认在输入目录下创建result文件夹"
              readonly
            >
              <template #append>
                <el-button @click="selectOutputFolder">
                  <el-icon><Folder /></el-icon>
                  选择
                </el-button>
              </template>
            </el-input>
            </el-tooltip>
          </el-form-item>
        </el-form>

        <!-- 下一步按钮 -->
        <div class="action-buttons">
          <el-button 
            type="primary" 
            @click="nextStep" 
            :disabled="!canProceed"
          >
            下一步
            <el-icon class="el-icon--right"><Right /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 步骤2: 绘制蒙版 -->
    <div v-if="currentStep === 1" class="step-content">
      <div class="mask-controls">
        <el-button @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="showMaskEditor = true">
          <el-icon>
            <Edit />
          </el-icon>
          开始标记区域
        </el-button>
        <el-button type="primary" @click="nextStep" :disabled="!maskBase64">下一步</el-button>
        
        <div v-if="maskBase64" class="mask-status">
          <el-tag type="success">已完成区域标记</el-tag>
        </div>
      </div>

      <div class="preview-image">
        <img :src="previewImage" alt="预览图片" class="base-image" />
        <img v-if="maskBase64" :src="maskBase64" alt="蒙版" class="mask-image" />
      </div>

      <el-dialog v-model="showMaskEditor" title="标记水印区域" width="80%" :close-on-click-modal="false"
        :close-on-press-escape="false" destroy-on-close>
        <InpaintIngImageEditor v-if="showMaskEditor" :imageBase64="previewImage" ref="imageEditorRef"
          @save="handleMaskSave" />
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showMaskEditor = false">取消</el-button>
            <el-button type="primary" @click="handleMaskComplete">
              完成标记
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>

    <!-- 步骤3: 选择模型 -->
    <div v-if="currentStep === 2" class="step-content">
      <div class="model-selection">
        <h3>选择处理模型</h3>
        <div class="model-options">
          <el-card v-for="model in modelOptions" :key="model.id"
            :class="['model-card', { active: modelType === model.id }]" @click="selectModel(model.id)">
            <div class="model-info">
              <div class="model-header">
                <span class="model-name">{{ model.name }}</span>
                <el-tag :type="model.tagType" size="small">{{ model.tag }}</el-tag>
              </div>
              <p class="model-description">{{ model.description }}</p>
              <ul class="model-features">
                <li v-for="feature in model.features" :key="feature">
                  <el-icon>
                    <Check />
                  </el-icon>
                  {{ feature }}
                </li>
              </ul>
            </div>
          </el-card>
        </div>
        <div class="model-controls">
          <el-button @click="prevStep">上一步</el-button>
          <el-button type="primary" @click="nextStep">下一步</el-button>
        </div>
      </div>
    </div>

    <!-- 步骤4: 批量处理 -->
    <div v-if="currentStep === 3" class="step-content">
      <div class="process-container">
        <!-- 顶部操作按钮和进度条容器 -->
        <div class="process-header">
          <!-- 操作按钮 -->
          <div class="step-actions">
            <el-button @click="prevStep" :disabled="processing">
              <el-icon><ArrowLeft /></el-icon>
              上一步
            </el-button>
            <el-button 
              type="primary" 
              @click="startProcess" 
              :loading="processing" 
              :disabled="processing"
            >
              <el-icon><VideoPlay /></el-icon>
              {{ processing ? '处理中...' : '开始处理' }}
            </el-button>
            <el-button 
              type="primary"
              @click="nextStep"
              :disabled="!canGoNext"
            >
              下一步
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>

          <!-- 进度条 -->
          <div class="process-progress">
            <el-progress 
              :percentage="processProgressed" 
              :status="processStatus"
              :format="(percentage) => `${percentage.toFixed(1)}%`"
              :stroke-width="12"
            >
              <template #default="{ percentage }">
                <span class="progress-value">{{ percentage.toFixed(1) }}%</span>
                <el-tag 
                  v-if="processing" 
                  size="small" 
                  type="warning"
                  class="time-remaining"
                >
                  <el-icon><Timer /></el-icon>
                  预计剩余: {{ remainingTime }}
                </el-tag>
              </template>
            </el-progress>
            <p class="status-text">{{ statusText }}</p>
          </div>
        </div>

        <!-- 文件列表 -->
        <div class="file-list">
          <el-scrollbar 
            height="400px" 
            class="file-list-content"
            @scroll="handleScroll"
            ref="scrollbarRef"
          >
            <div
              v-for="(file, index) in displayList"
              :key="file.path"
              class="file-item"
              :class="{ 
                'processing': file.status === 'processing',
                'processed': file.status === 'success',
                'error': file.status === 'error'
              }"
            >
              <el-image 
                :src="file.image_url" 
                class="file-image"
                lazy
                :preview-teleported="true"
                :preview-src-list="[file.image_url]"
              />
              <span class="filename" :title="file.image_name">{{ file.image_name }}</span>
              <div class="status-icon">
                <el-icon v-if="file.status === 'success'" class="success"><CircleCheckFilled /></el-icon>
                <el-icon v-else-if="file.status === 'error'" class="error"><CircleCloseFilled /></el-icon>
                <el-icon v-else-if="file.status === 'processing'" class="processing"><Loading /></el-icon>
                <el-icon v-else class="waiting"><Clock /></el-icon>
              </div>
               
            </div>
            <div v-if="loading" class="loading-more">
              <el-icon class="loading"><Loading /></el-icon>
              <span>加载中...</span>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>

    <!-- 处理完成步骤 -->
    <div v-if="currentStep === 4" class="step-content">
      <div class="process-complete">
        <el-result
          icon="success"
          title="处理完成"
          sub-title="批量处理已完成"
        >
          <template #extra>
            <div class="result-actions">
              <!-- 上一步 -->
              <el-button @click="prevStep">上一步</el-button>
              <!-- 处理新图片 -->
              <el-button type="primary" @click="handleReset">处理新图片</el-button>
              <!-- 打开输出文件夹 -->
              <el-button @click="openOutputFolder">打开输出文件夹</el-button>
            </div>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Folder, Edit, Check, Right, Warning, CircleCheckFilled, CircleCloseFilled, Clock, Loading, ArrowLeft, ArrowRight, VideoPlay, Timer } from '@element-plus/icons-vue'
import { inpaintingAPI } from '@/apis/inpainting'
import baseAPI from '@/apis/base'
import message from '@/utils/message'
import InpaintIngImageEditor from '@/components/ImageTool/InpaintIngImageEditor.vue'

const currentStep = ref(0)
const modelType = ref('fast')
const previewImage = ref('')
const maskBase64 = ref('')
const processing = ref(false)
const processProgressed = ref(0)
const processStatus = ref('')
const statusText = ref('等待开始处理...')
const fileList = ref([])
const loading = ref(false)
const showMaskEditor = ref(false)
const imageEditorRef = ref(null)
const inputFolder = ref('')
const outputFolder = ref('')
const pageSize = 20 // 每页显示数量
const currentPage = ref(1)
const scrollbarRef = ref(null)
const loadingThreshold = 100 // 距离底部多少像素时触发加载
const startTime = ref(null)

// 添加模型选项配置
const modelOptions = [
  {
    id: 'fast',
    name: '快速模式',
    tag: '推荐',
    tagType: 'success',
    description: '适用于简单区域处理，处理速度快',
    features: [
      '处理速度快',
      '适合简单场景',
      '资源占用低'
    ]
  },
  {
    id: 'quality',
    name: '高质量模式',
    tag: '专业',
    tagType: 'warning',
    description: '适用于复杂区域处理，效果更好',
    features: [
      '处理效果好',
      '适合复杂场景',
      '支持细节修复'
    ]
  }
]

// 修改 canStart 计算属性
const canStart = computed(() => {
  return inputFolder.value && fileList.value.length > 0 && outputFolder.value
})

// 添加是否可以进行下一步的计算属性
const canProceed = computed(() => {
  return inputFolder.value && fileList.value.length > 0
})

// 添加选择输出文件夹的方法
const selectOutputFolder = async () => {
  try {
    const result = await baseAPI('open_folder_dialog', '')
    if (result.code === 200 && result.data.folder_path) {
      outputFolder.value = result.data.folder_path
    }
  } catch (error) {
    message.error('选择输出文件夹失败')
  }
}

// 修改选择输入文件夹的方法
const selectFolder = async () => {
  loading.value = true
  try {
    const result = await baseAPI('open_folder_dialog', '')
    if (result.code === 200 && result.data.folder_path) {
      inputFolder.value = result.data.folder_path
      outputFolder.value = `${result.data.folder_path}/result`
      
      // 重置分页状态
      currentPage.value = 1
      
      const files = await baseAPI('get_folder_images', result.data.folder_path)
      if (files.code === 200 && files.data.image_list.length > 0) {
        fileList.value = files.data.image_list
        previewImage.value = files.data.image_list[0].image_url
      } else {
        message.warning('所选文件夹中没有找到图片文件')
      }
    }
  } catch (error) {
    message.error('选择文件夹失败')
  } finally {
    loading.value = false
  }
}

// 保存蒙版
const handleMaskSave = (data) => {
  maskBase64.value = data.maskBase64
}

// 修改完成标记的处理函数
const handleMaskComplete = () => {
  if (imageEditorRef.value) {
    imageEditorRef.value.saveMask()
    
    if (maskBase64.value) {
      message.success('区域标记完成')
      showMaskEditor.value = false
    } else {
      message.warning('请先标记处理区域')
    }
  }
}

// 开始批量处理
const startProcess = async () => {
  if (!maskBase64.value) {
    message.warning('请先绘制蒙版')
    return
  }

  try {
    processing.value = true
    startTime.value = Date.now()
    processProgressed.value = 0
    processStatus.value = 'processing'
    statusText.value = '正在处理...'

    // 更新所有文件状态为等待中
    fileList.value = fileList.value.map(file => ({
      ...file,
      status: 'waiting',
      statusText: '等待处理',
      progress: 0,
      error: null
    }))

    const result = await inpaintingAPI('batch_process', {
      input_folder: inputFolder.value,
      output_folder: outputFolder.value,
      mask_base64: maskBase64.value,
      model_type: modelType.value,
      image_list: fileList.value
    })

    if (result.code === 200) {
      processStatus.value = 'success'
      statusText.value = '处理完成'
      message.success('批量处理完成')
      
      // 自动打开输出文件夹
      await baseAPI('open_and_select_file', outputFolder.value)
    }
  } catch (error) {
    processStatus.value = 'exception'
    statusText.value = '处理失败'
    message.error(error.message || '处理失败')
  } finally {
    processing.value = false
  }
}

// 修改进度处理函数
const handleProgress = (event) => {
  const progressData = event.detail
  
  processProgressed.value = parseFloat(progressData.progress) || 0
  statusText.value = progressData.message || ''
  
  switch (progressData.status) {
    case 'start':
      processStatus.value = 'processing'
      fileList.value = fileList.value.map(file => ({
        ...file,
        status: 'waiting',
        statusText: '等待处理',
        progress: 0,
        error: null
      }))
      break
      
    case 'processing':
      const currentIndex = parseInt(progressData.current) - 1
      if (currentIndex >= 0 && currentIndex < fileList.value.length) {
        updateFileStatus(currentIndex, {
          status: 'processing',
          statusText: '处理中',
          progress: parseFloat(progressData.progress) || 0
        })
      }
      break
      
    case 'file_success':
      const successIndex = parseInt(progressData.current) - 1
      if (successIndex >= 0 && successIndex < fileList.value.length) {
        updateFileStatus(successIndex, {
          status: 'success',
          statusText: '处理完成',
          progress: 100,
          error: null
        })
      }
      break
      
    case 'file_error':
      const errorIndex = parseInt(progressData.current) - 1
      if (errorIndex >= 0 && errorIndex < fileList.value.length) {
        updateFileStatus(errorIndex, {
          status: 'error',
          statusText: '处理失败',
          progress: 100,
          error: progressData.error || '未知错误'
        })
      }
      break
      
    case 'complete':
      const hasErrors = String(progressData.has_errors).toLowerCase() === 'true'
      if (hasErrors) {
        processStatus.value = 'warning'
        const successCount = parseInt(progressData.success_count) || 0
        const errorCount = parseInt(progressData.error_count) || 0
        statusText.value = `处理完成，${successCount}个成功，${errorCount}个失败`
      } else {
        processStatus.value = 'success'
        statusText.value = '处理完成'
      }
      nextStep()
      break
      
    case 'error':
      processStatus.value = 'exception'
      statusText.value = progressData.message || '处理失败'
      message.error(progressData.error || '处理失败')
      break
  }
}

// 更新单个文件状态的辅助函数
const updateFileStatus = (index, status) => {
  if (index >= 0 && index < fileList.value.length) {
    fileList.value = fileList.value.map((file, i) => {
      if (i === index) {
        return { 
          ...file, 
          ...status,
          progress: status.progress ? parseFloat(status.progress) : 0
        }
      }
      return file
    })
  }
}

// 在 mounted 中添加事件监听
onMounted(() => {
  window.addEventListener('ProcessProgress', handleProgress)
})

// 在 unmounted 中移除事件监听
onUnmounted(() => {
  window.removeEventListener('ProcessProgress', handleProgress)
})

// 添加选择模型方法
const selectModel = (modelId) => {
  modelType.value = modelId
}

// 步骤控制
const nextStep = () => {
  if (currentStep.value < 4) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 重置所有状态
const handleReset = () => {
  currentStep.value = 0
  processProgressed.value = 0
  processStatus.value = ''
  statusText.value = '等待开始处理...'
  fileList.value = []
  maskBase64.value = ''
  inputFolder.value = ''
  outputFolder.value = ''
  processing.value = false
  modelType.value = 'fast'
}

// 打开输出文件夹
const openOutputFolder = async () => {
  if (outputFolder.value) {
    await baseAPI('open_and_select_file', outputFolder.value)
  }
}

// 添加计算属性
const displayList = computed(() => {
  const start = 0
  const end = currentPage.value * pageSize
  return fileList.value.slice(start, end)
})

// 是否还有更多数据
const hasMore = computed(() => {
  return displayList.value.length < fileList.value.length
})

// 处理滚动事件
const handleScroll = async (e) => {
  if (loading.value || !hasMore.value) return

  const scrollbar = scrollbarRef.value
  if (!scrollbar) return

  const wrap = scrollbar.wrapRef
  if (!wrap) return

  const {
    scrollTop,
    scrollHeight,
    clientHeight
  } = wrap

  // 当滚动到距离底部 loadingThreshold 像素时加载更多
  if (scrollHeight - (scrollTop + clientHeight) <= loadingThreshold) {
    await loadMoreData()
  }
}

// 加载更多数据
const loadMoreData = async () => {
  if (loading.value || !hasMore.value) return
  
  loading.value = true
  try {
    // 模拟异步加载
    await new Promise(resolve => setTimeout(resolve, 300))
    currentPage.value++
  } finally {
    loading.value = false
  }
}

// 添加剩余时间计算
const remainingTime = computed(() => {
  if (!startTime.value || processProgressed.value === 0) return '计算中...'
  
  const elapsed = (Date.now() - startTime.value) / 1000
  const avgTimePerFile = elapsed / (fileList.value.length * (processProgressed.value / 100))
  const remaining = avgTimePerFile * (fileList.value.length * (1 - processProgressed.value / 100))
  
  if (remaining < 60) return `${Math.round(remaining)}秒`
  if (remaining < 3600) return `${Math.round(remaining / 60)}分钟`
  return `${Math.round(remaining / 3600)}小时${Math.round((remaining % 3600) / 60)}分钟`
})

// 添加是否可以进入下一步的计算属性
const canGoNext = computed(() => {
  return !processing.value && processProgressed.value === 100
})

</script>

<style lang="scss" scoped>
.batch-inpainting {
  .steps {
    margin-bottom: 40px;
  }

  .step-content {
    max-width: 1000px;
    margin: 0 auto;
  }

  .folder-select {
    padding: 40px;
    
    .el-form {
      max-width: 600px;
      margin: 0 auto;
    }

    .action-buttons {
      text-align: center;
      margin-top: 32px;
      
      .el-button {
        min-width: 120px;
      }
    }
  }

  .preview-image {
    width: 500px;
    height: 350px;
    margin: 20px auto;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    overflow: hidden;
    position: relative;

    .base-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .mask-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      opacity: 0.5;
      mix-blend-mode: multiply;
    }
  }

  .model-selection {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;

    h3 {
      text-align: center;
      margin-bottom: 20px;
      color: var(--el-text-color-primary);
    }

    .model-options {
      display: flex;
      gap: 20px;
      justify-content: center;
      margin-bottom: 30px;

      .model-card {
        flex: 1;
        max-width: 340px;
        cursor: pointer;
        transition: all 0.3s;
        border: 2px solid transparent;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &.active {
          border-color: var(--el-color-primary);
        }

        .model-info {
          .model-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .model-name {
              font-size: 16px;
              font-weight: 500;
            }
          }

          .model-description {
            color: var(--el-text-color-secondary);
            font-size: 14px;
            margin-bottom: 16px;
          }

          .model-features {
            list-style: none;
            padding: 0;
            margin: 0;

            li {
              display: flex;
              align-items: center;
              gap: 8px;
              color: var(--el-text-color-regular);
              font-size: 13px;
              margin-bottom: 8px;

              .el-icon {
                color: var(--el-color-success);
              }
            }
          }
        }
      }
    }

    .model-controls {
      text-align: center;
      
      .el-button {
        margin: 0 8px;
      }
    }
  }

  .process-container {
    .process-header {
      background-color: var(--el-bg-color);
      border-radius: 4px;
      margin-bottom: 16px;

      .step-actions {
        display: flex;
        justify-content: center;
        gap: 8px;
        padding: 12px;
        border-bottom: 1px solid var(--el-border-color-lighter);
        
        .el-button {
          min-width: 90px;
          height: 32px;
          padding: 0 12px;
          
          .el-icon {
            margin-right: 4px;
          }
        }
      }

      .process-progress {
        padding: 12px 16px;
        
        :deep(.el-progress) {
          margin-bottom: 4px;
        }

        .progress-value {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-color-primary);
        }

        .time-remaining {
          margin-left: 8px;
          height: 20px;
          line-height: 20px;
          padding: 0 6px;
          
          .el-icon {
            margin-right: 2px;
            font-size: 12px;
          }
        }

        .status-text {
          margin: 4px 0 0;
          color: var(--el-text-color-secondary);
          font-size: 12px;
          line-height: 1.2;
        }
      }
    }

    .file-list {
      max-height: 40vh;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      margin-bottom: 20px;

      .file-list-content {
        .file-item {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid var(--el-border-color-lighter);
          gap: 16px;

          &:last-child {
            border-bottom: none;
          }

          &.processing {
            background-color: var(--el-color-primary-light-9);
          }

          &.processed {
            background-color: var(--el-color-success-light-9);
          }

          &.error {
            background-color: var(--el-color-danger-light-9);
          }

          .file-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 4px;
            flex-shrink: 0;
          }

          .filename {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .status-icon {
            .el-icon {
              font-size: 16px;
              
              &.success {
                color: var(--el-color-success);
              }
              
              &.error {
                color: var(--el-color-danger);
              }
              
              &.processing {
                color: var(--el-color-primary);
                animation: rotating 2s linear infinite;
              }
              
              &.waiting {
                color: var(--el-text-color-secondary);
              }
            }
          }

          .el-progress {
            width: 120px;
            margin-left: auto;
          }
        }
      }

      .loading-more {
        padding: 16px;
        text-align: center;
        color: var(--el-text-color-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        
        .loading {
          animation: rotating 2s linear infinite;
        }
      }
    }
  }

  .mask-controls {
    text-align: center;
    margin-bottom: 20px;
    
    .el-button {
      margin: 0 8px;
    }
    
    .mask-status {
      margin-top: 16px;
    }
  }

  .process-complete {
    padding: 40px 0;
    
    .result-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>