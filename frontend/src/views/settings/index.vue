<template>
  <div class="settings-container">
    <h1 class="page-title">
      系统设置
      <el-button 
        type="primary" 
        link
        @click="openLinkAPI('https://lingxiangtools.top/history')"
        class="check-update-btn"
      >
        <el-icon><RefreshRight /></el-icon>
        检查更新
      </el-button>
    </h1>

    <el-collapse v-model="activeCollapse" class="settings-collapse">
      
      <el-collapse-item name="models" ref="ref1" style="margin-bottom: 20px;">
        <template #title>
          <div class="collapse-header">
            <div class="title-wrapper">
              <el-icon class="title-icon"><Setting /></el-icon>
              <span class="title">模型管理</span>
            </div>
          </div>
        </template>

        <div class="model-section">
          <div class="section-header">
            <div class="section-title">存储位置</div>
          </div>
          
          <!-- 警告，更改请重启服务 -->
          <el-alert
            title="提示，更改模型路径后请重启程序"
            type="warning"
            show-icon
            class="warning-alert"
            style="margin-bottom: 12px;"
          />
          
          <div class="model-path">
            <div class="path-input-group">
              <el-input
                v-model="modelPath"
                placeholder="模型存储路径"
                class="path-input"
                :disabled="true"
              >
                <template #prepend>
                  <el-icon><Folder /></el-icon>
                </template>
              </el-input>
              <el-button @click="handleSelectPath">
                <el-icon><FolderOpened /></el-icon>
                选择路径
              </el-button>
              <el-button @click="openPath">
                <el-icon><Folder /></el-icon>
                打开路径
              </el-button>
            </div>
          </div>

          <div class="section-header">
            <div class="section-title">模型列表</div>
            <el-button 
              type="primary" 
              @click="handleDownloadAll" 
              :loading="downloadingAll"
              class="download-all-btn"
              ref="ref2"
            >
              <el-icon><Download /></el-icon>
              一键下载全部
            </el-button>
          </div>

          <div class="section-header">
            <div class="section-title">导入模型</div>
            <el-tooltip content="下面的模型，请自己下载导入" placement="left" effect="light">
              <el-icon class="help-icon"><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>

          <div class="import-model-section">
            <el-table 
              :data="importableModels" 
              style="width: 100%" 
              class="model-table"
            >
              <el-table-column prop="name" label="模型名称" align="center" />
              <el-table-column prop="size" label="大小" align="center" />
              <el-table-column prop="description" label="描述" align="center" />
              <el-table-column label="下载地址" align="center" width="200">
                <template #default="scope">
                  <div class="download-links-cell">
                    <el-button 
                      ref="ref3"
                      type="success" 
                      link
                      size="small"
                      @click="openLinkAPI(scope.row.acceleratedUrl)"
                      class="source-link-btn"
                    >
                      <el-icon><Link /></el-icon>
                      加速地址
                    </el-button>
                    <el-button 
                      type="primary" 
                      link
                      size="small"
                      @click="openLinkAPI(scope.row.originalUrl)"
                      class="source-link-btn"
                    >
                      <el-icon><Link /></el-icon>
                      源地址
                    </el-button>
                    
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="状态" align="center">
                <template #default="scope">
                  <el-tag
                    :type="getStatusTagType(scope.row.status)"
                    class="status-tag"
                    :effect="scope.row.status === 'valid' ? 'light' : 'plain'"
                  >
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="200">
                <template #default="scope">
                  <div class="action-buttons">
                    <el-button
                      type="primary"
                      size="small"
                      :disabled="scope.row.status === 'valid'"
                      @click="handleImportModel(scope.row)"
                      class="action-button"
                    >
                      <el-icon v-if="scope.row.status !== 'valid'">
                        <Upload />
                      </el-icon>
                      <el-icon v-else>
                        <Select />
                      </el-icon>
                      {{ scope.row.status === 'valid' ? '已导入' : '导入' }}
                    </el-button>

                    <el-button
                      v-if="scope.row.status === 'invalid'"
                      type="danger"
                      size="small"
                      @click="handleRemoveModel(scope.row)"
                      class="action-button"
                    >
                      <el-icon><Delete /></el-icon>
                      移除
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <el-table 
            :data="modelList" 
            style="width: 100%" 
            v-loading="loading"
            class="model-table"
          >
            <el-table-column prop="name" label="模型名称" align="center" />
            <el-table-column prop="size" label="大小" align="center" />
            <el-table-column prop="description" label="描述" align="center" />
            <el-table-column label="状态" align="center">
              <template #default="scope">
                <el-tag
                  :type="getStatusTagType(scope.row.status)"
                  class="status-tag"
                  :effect="scope.row.status === 'valid' ? 'light' : 'plain'"
                >
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作"  align="center" width="200">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button
                    :type="scope.row.status === 'valid' ? 'success' : 'primary'"
                    size="small"
                    :loading="scope.row.downloading"
                    :disabled="scope.row.status === 'valid'"
                    @click="handleDownload(scope.row)"
                    class="action-button"
                  >
                    <el-icon v-if="!scope.row.downloading && scope.row.status !== 'valid'">
                      <Download />
                    </el-icon>
                    <el-icon v-else-if="scope.row.status === 'valid'">
                      <Select />
                    </el-icon>
                    {{ scope.row.status === 'valid' ? '已完成' : '下载' }}
                  </el-button>

                  <el-button
                    v-if="scope.row.status === 'invalid'"
                    type="danger"
                    size="small"
                    @click="handleRemoveModel(scope.row)"
                    class="action-button"
                  >
                    <el-icon><Delete /></el-icon>
                    移除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-collapse-item>

      <el-collapse-item name="basicSettings">
        <template #title>
          <div class="collapse-header">
            <div class="title-wrapper">
              <el-icon class="title-icon"><Setting /></el-icon>
              <span class="title">基础配置</span>
            </div>
          </div>
        </template>
        <div class="settings-section" style="margin-bottom: 0; border: none; box-shadow: none; padding: 0;">
          <el-form class="basic-settings-form">
            <div class="setting-item">
              <div class="api-address-section">
                <span class="api-label">API 地址：</span>
                <a 
                  :href="`http://127.0.0.1:${settingInfo.api_server.port}/api/docs`" 
                  target="_blank"
                  class="api-link"
                >
                  http://127.0.0.1:{{ settingInfo.api_server.port }}/api/docs
                </a>
              </div>

              <div class="setting-label">
                <div class="label-content">
                  <span>API 服务端口</span>
                  <el-tooltip
                    content="设置 API 服务监听的端口号 (1024-65535)"
                    placement="top"
                    effect="light"
                  >
                    <el-icon class="help-icon"><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
                <el-input
                  v-model.number="settingInfo.api_server.port"
                  class="port-input"
                  type="number"
                  :min="1024"
                  :max="65535"
                />
                <!-- 警告，更改请重启服务 -->
                <el-alert
                  title="提示，更改端口后请重启程序"
                  type="warning"
                  show-icon
                  class="warning-alert"
                />
              </div>
            </div>
            
            <div class="form-actions">
              <el-button type="primary" @click="saveSettings" class="save-btn">
                应用设置
              </el-button>
            </div>
          </el-form>
        </div>
      </el-collapse-item>
    </el-collapse>

    <el-tour v-model="tourVisible" @finish="onTourFinish" type="primary" :mask="false" @change="onTourChange" >
      <el-tour-step
      :target="ref1?.$el"
      title="模型管理"
      description="在这里您可以管理和下载所需的AI模型"
      />
      <el-tour-step
        :target="ref2?.$el"
        title="一键下载"
        description="点击这里可以一次性下载所有必需的模型"
      />
      <el-tour-step
        :target="ref3?.$el"
        title="导入模型"
        description="这里是需要手动下载导入的模型，您可以点击加速地址下载，然后通过导入按钮导入模型"
      />
    </el-tour>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import {
  systemAPI,
  getModelStatusAPI,
  downloadModelAPI,
  downloadAllModelsAPI,
  updateModelPathAPI,
  importModelAPI,
  removeModelAPI
} from '@/apis/system'
import baseAPI from '@/apis/base'
import { openLinkAPI } from '@/apis/common'
import message from '@/utils/message'
import {
  Setting,
  Download,
  Folder,
  FolderOpened,
  Select,
  RefreshRight,
  QuestionFilled,
  Upload,
  Link,
  Delete
} from '@element-plus/icons-vue'

const loading = ref(false)
const modelPath = ref('')
const downloadingAll = ref(false)
const activeCollapse = ref([]) // 控制折叠面板状态

const ref1 = ref(null)
const ref2 = ref(null)
const ref3 = ref(null)

const settingInfo = ref({
  'api_server': {
    'is_enable': true,
    'port': 11112,
  }
})


const formData = ref({
  'api_server.is_enable': true,
  'api_server.port': 11112
})

// 模型配置信息
const MODEL_CONFIG = {
  'm_repair_tiny.onnx': { size: '28.1MB', description: '快速图像修复模型' },
  'm_repair_normal.onnx': { size: '208MB', description: '高质量图像修复模型' },

  'people_segmentation.onnx': { size: '25.9MB', description: '人像抠图模型' },
  'segmentation_normal.onnx': { size: '224MB', description: '标准抠图模型轻量版' },
  'segmentation_high.onnx': { size: '972.7MB', description: '高质量抠图模型高精度版' },
  'ppocr': { size: '16.2MB', description: 'OCR检测模型' },
  'ffmpeg': { size: '226MB', description: '视频处理' },
  'real_general.onnx': { size: '67.1MB', description: '通用图像高清放大模型' },
  'real_anime.onnx': { size: '17.9MB', description: '动漫图像高清放大模型' },
}

const modelList = ref([])
const importableModels = ref([])

// 可导入的模型配置信息
const IMPORTABLE_MODEL_CONFIG = {
  'rmbg14.onnx': { 
    size: '176MB', 
    description: '快速抠图模型',
    originalUrl: 'https://huggingface.co/briaai/RMBG-1.4/resolve/main/onnx/model.onnx?download=true',
    acceleratedUrl: 'https://hf-mirror.com/briaai/RMBG-1.4/resolve/main/onnx/model.onnx?download=true'
  },
  'rmbg2.onnx': { 
    size: '1020MB', 
    description: 'rmbg2抠图模型',
    originalUrl: 'https://huggingface.co/briaai/RMBG-2.0/resolve/main/onnx/model.onnx?download=true',
    acceleratedUrl: 'https://gitee.com/hf-models/RMBG-2.0/raw/main/onnx/model.onnx?lfs=1'
  },
}

// Tour guide state
const tourVisible = ref(false)

const onTourChange = (num) => {
  if (num === 1) {
    activeCollapse.value = ['models'] // 确保模型管理面板是展开的
  }
}

// Check if tour has been shown before
const checkTourStatus = () => {
  const tourShown = localStorage.getItem('settingsTourShown')
  if (!tourShown) {  
    tourVisible.value = true
  }
}

const onTourFinish = () => {
  // 询问是否不再提示
  ElMessageBox.alert('是否不再提示？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showCancelButton: true,
    type: 'warning'
  }).then(async (res) => {
    if (res=="confirm") {
      localStorage.setItem('settingsTourShown', 'true')
      tourVisible.value = false
    }
  })
}

// 获取模型状态
const getModelStatus = async () => {
  loading.value = true
  try {
    const res = await getModelStatusAPI()
    console.log(res)
    if (res.code === 200) {
      modelList.value = Object.entries(MODEL_CONFIG).map(([name, info]) => ({
        name,
        size: info.size,
        description: info.description,
        status: res.data[name] || 'missing',  // 新的状态字段：missing, valid, invalid
        downloading: false
      }))

      importableModels.value = Object.entries(IMPORTABLE_MODEL_CONFIG).map(([name, info]) => ({
        name,
        size: info.size,
        description: info.description,
        originalUrl: info.originalUrl,
        acceleratedUrl: info.acceleratedUrl,
        status: res.data[name] || 'missing'  // 使用状态字段而不是imported字段
      }))
    }
  } catch (error) {
    message.error('获取模型状态失败')
  }
  loading.value = false
}

// 下载模型
const handleDownload = async (model) => {
  model.downloading = true
  try {
    const res = await downloadModelAPI(model.name)
    if (res.code === 200) {
      message.success('下载成功')
      await getModelStatus()
    } else {
      message.error(res.message || '下载失败')
    }
  } catch (error) {
    message.error('下载失败')
  }
  model.downloading = false
}

// 获取初始配置
const getInitialConfig = async () => {
  try {
    const res = await systemAPI('get')
    if (res.code === 200) {
      modelPath.value = res.data.model_dir
      settingInfo.value = res.data
    }
  } catch (error) {
    message.error('获取配置失败')
  }
}
// 保存设置
const saveSettings = async () => {
  console.log(settingInfo.value)
  formData.value['api_server.port'] = settingInfo.value.api_server.port

  const res = await systemAPI('put', formData.value)
  if (res.code === 200) {
    message.success(res.msg);
    changeLanguage(settingInfo.value.language)
  } else {
    message.error(res.err_msg);
  }
}

// 选择路径
const handleSelectPath = async () => {
  loading.value = true
  try {
    const response = await baseAPI('open_folder_dialog', '')
    if (response.code === 200 && response.data.folder_path) {
      console.log(response.data.folder_path)
      const newPath = response.data.folder_path
      const updateRes = await updateModelPathAPI(newPath)
      if (updateRes.code === 200) {
        modelPath.value = newPath 
        message.success('模型路径更新成功')
        await getModelStatus() // 刷新模型状态
      }
    }
  } catch (error) {
    message.error('更新模型路径失败')
   }
  loading.value = false
  // 弹窗提示需要重启程序
  ElMessageBox.alert('模型路径更新成功，需要重启程序才能生效', '提示', {
    confirmButtonText: '确定',
    showCancelButton: false,
    type: 'warning'
  })
}

// 一键下载全部
const handleDownloadAll = async () => {
  downloadingAll.value = true
  try {
    const res = await downloadAllModelsAPI()
    if (res.code === 200) {
      message.success('所有模型下载成功')
      await getModelStatus() // 刷新模型状态
    } else {
      message.error(res.msg || '下载失败')
    }
  } catch (error) {
    message.error('下载失败')
  }
  downloadingAll.value = false
}

// 导入模型
const handleImportModel = async (model) => {
  try {
    const fileRes = await baseAPI('get_local_file_path', 'ONNX Models (*.onnx)')
    if (fileRes.code === 200 && fileRes.data.selected_file) {
      const filePath = fileRes.data.selected_file
      const res = await importModelAPI(model.name, filePath)
      if (res.code === 200) {
        message.success('模型导入成功')
        await getModelStatus()
        // await getImportableModelStatus()
      } else {
        message.error(res.msg)
      }
    }
  } catch (error) {
    message.error(error.message)
  }
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status) {
    case 'valid':
      return 'success'
    case 'invalid':
      return 'danger'
    case 'missing':
      return 'info'
    default:
      return 'warning'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'valid':
      return '已下载'
    case 'invalid':
      return '下载失败'
    case 'missing':
      return '未下载'
    default:
      return '检查中'
  }
}

// 移除模型
const handleRemoveModel = async (model) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除模型 "${model.name}" 吗？此操作不可撤销。`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const res = await removeModelAPI(model.name)
    if (res.code === 200) {
      message.success(res.msg || '模型移除成功')
      await getModelStatus() // 刷新模型状态
    } else {
      message.error(res.msg || '移除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      message.error('移除失败')
    }
  }
}

// 打开路径
// 添加打开结果文件的方法
const openPath = async () => {
  loading.value = true
  if (!modelPath.value) return
  console.log(modelPath.value)
  try {
      const res = await baseAPI('open_directory', modelPath.value)
      if (res.code === 200) {
        message.success(res.msg)
      } else {
        message.error(res.msg)
      }
  } catch (error) {
    message.error(error.message)
  }
  loading.value = false
}

onMounted( async () => {
  await getInitialConfig()
  await getModelStatus()
  checkTourStatus()
})
</script>

<style scoped>
.settings-container {
  padding: 24px;
  background-color: var(--el-bg-color-page);
  min-height: 100%;
}

.page-title {
  margin: 0 0 24px;
  font-size: 24px;
  color: var(--el-text-color-primary);
  display: flex;
  align-items: center;
  gap: 12px;
}

.settings-collapse {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.collapse-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  font-size: 18px;
  color: var(--el-text-color-secondary);
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.download-all-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

.model-section {
  padding: 0 4px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px 0 12px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
  margin: 0;
}

.model-path {
  margin-bottom: 24px;
}

.path-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.path-input {
  flex: 1;
}

.model-table {
  margin-top: 8px;
  border-radius: 4px;
}

.status-tag {
  min-width: 64px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

:deep(.el-collapse-item__header) {
  padding: 16px 20px;
  font-size: 16px;
}

:deep(.el-collapse-item__content) {
  padding: 20px;
  background-color: var(--el-bg-color);
}

:deep(.el-collapse-item__wrap) {
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-collapse) {
  border-top: 1px solid var(--el-border-color-light);
  border-bottom: 1px solid var(--el-border-color-light);
}

:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-lighter);
  --el-table-header-bg-color: var(--el-fill-color-light);
}

:deep(.el-input-group__prepend) {
  padding: 0 12px;
  background-color: var(--el-fill-color-light);
}

:deep(.el-button) {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.check-update-btn {
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.settings-section {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 24px;
}

.settings-section-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: var(--el-text-color-primary);
}

.setting-item {
  padding: 16px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 32px;
}

.label-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--el-text-color-primary);
  min-width: 120px;
}

.help-icon {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  cursor: help;
}

.port-input {
  width: 180px;
}

.port-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
}

.port-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
}

.form-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.save-btn {
  min-width: 100px;
}

.basic-settings-form {
  margin: 0;
}

.api-address-section {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.api-label {
  font-size: 14px;
}

.api-link {
  color: var(--el-color-primary);
  text-decoration: none;
  font-family: monospace;
  font-size: 14px;
  padding: 4px 8px;
  background-color: var(--el-bg-color);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-lighter);
  transition: all 0.3s ease;
}

.api-link:hover {
  color: var(--el-color-primary-dark-2);
  border-color: var(--el-color-primary);
  text-decoration: none;
}

.import-model-section {
  margin-top: 16px;
  margin-bottom: 24px;
}

.download-settings {
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
}

.download-links {
  display: flex;
  gap: 16px;
  margin-top: 12px;
}

.source-link-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

.download-links-cell {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.source-link-btn {
  font-size: 12px;
  padding: 4px 8px;
}

:deep(.tour-popper) {
  max-width: 320px;
  z-index: 3000 !important;
}

:deep(.el-tour__mask) {
  z-index: 2999 !important;
}

:deep(.el-tour__overlay) {
  z-index: 2998 !important;
}
</style> 