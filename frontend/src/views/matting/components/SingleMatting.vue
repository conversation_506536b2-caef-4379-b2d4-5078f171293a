<template>
  <div 
    class="single-matting"
    @dragover.prevent
    @drop.prevent="handleDrop"
  >
    <el-loading 
      v-model:visible="loading"
      :lock="true"
      text="处理中..."
    />
    
    <div class="layout-container">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <el-form label-position="left">
          <el-form-item label="选择图片">
            <div class="upload-content">
              <UploadButton text="选择文件" size="default" @upload="handleClickUpload" />
              <StartProcessButton 
                text="开始处理" 
                :loading="processing" 
                :disabled="!imageUrl || processing"
                @startProcess="startProcess"
              />
              <el-button 
                v-if="imageUrl" 
                @click="resetImage"
              >
                <el-icon><Back /></el-icon>
                重新选择
              </el-button>
              <el-button
                type="info"
                @click="resetToDefault"
              >
                <el-icon><RefreshRight /></el-icon>
                重置选项
              </el-button>
            </div>
          </el-form-item>

          <el-form-item>
            <template #label>
              <div class="label-with-icon">
                <span>处理模型</span>
                <el-tooltip
                  placement="top"
                  effect="light"
                >
                  <template #content>
                    <div class="shortcut-help">
                      <li>人像: 适用于人像抠图，效果好</li>
                      <li>快速: 速度快，效果好</li>
                      <li>标准: 速度相对慢，保留主体较多</li>
                      <li>高质量: 速度慢，保留主体较少</li>
                      <li>rmbg2: 速度慢，保留主体较少</li>
                    </div>
                  </template>
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            
            <el-radio-group v-model="aiModel">
              <el-radio value="modnet">人像</el-radio>
              <el-radio value="rmbg14">快速</el-radio>
              <el-radio value="fast">标准</el-radio>
              <el-radio value="quality">高质量</el-radio>
              <el-radio value="rmbg2">rmbg2</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 分辨率 72 96 150 300 600 -->
          <el-form-item label="分辨率">
            <template #label>
              <div class="label-with-icon">
                <span>分辨率</span>
                <el-tooltip
                  content="注意：psd文件不支持设置分辨率，默认使用72dpi"
                  placement="top"
                  effect="light"
                >
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-radio-group v-model="dpi">
              <el-radio value="72">72</el-radio>
              <el-radio value="96">96</el-radio>
              <el-radio value="150">150</el-radio>
              <el-radio value="300">300</el-radio>
              <el-radio value="600">600</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 边缘优化 -->
          <el-form-item>
            <template #label>
              <div class="label-with-icon">
                <span>边缘优化</span>
                <el-tooltip
                  content="黑边优化,值越大边缘越柔和，值越小边缘越锐化"
                  placement="top"
                  effect="light"
                >
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <div class="edge-optimization-container">
              <el-switch
                v-model="isEdgeOptimization"
                class="edge-switch"
              />
              <div v-show="isEdgeOptimization" class="edge-value-slider inline">
                <el-slider
                  v-model="edgeValue"
                  :min="0"
                  :max="360"
                  :step="1"
                  :format-tooltip="(val) => `${val}°`"
                  class="edge-slider"
                >
                </el-slider>
              </div>
            </div>
          </el-form-item>

          <!-- 去除小区域 -->
          <el-form-item>
            <template #label>
              <div class="label-with-icon">
                <span>去除小区域</span>
                <el-tooltip
                  content="移除小区域,值越大去除的区域越大"
                  placement="top"
                  effect="light"
                >
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <div class="small-regions-container">
              <el-switch 
                v-model="isRemoveSmallRegions" 
                :default-value="aiModel === 'rmbg14'"
                class="small-regions-switch"
              />
              <div v-show="isRemoveSmallRegions" class="small-regions-value inline">
                <el-slider
                  v-model="removeSmallRegionsValue" 
                  :min="1" 
                  :max="1000"
                  :step="1"
                  :format-tooltip="(val) => `${val}`"
                  class="small-regions-slider"
                />
              </div>
            </div>
          </el-form-item>

          <el-form-item label="背景设置">
            <el-button @click="showBackgroundPicker = true">
              <div class="color-preview" :style="getColorPreviewStyle"></div>
              设置背景色
            </el-button>
            <!-- 显示已选择的背景图片预览 -->
            <div v-if="backgroundImage && !selectedColor.includes('gradient') && selectedColor !== 'transparent'" 
                 class="selected-bg-preview" 
                 @click="previewBackgroundImage">
              <img :src="backgroundUrl" alt="背景预览" class="bg-preview-img" />
              <div class="preview-overlay">
                <el-icon><ZoomIn /></el-icon>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="处理模式">
            <el-radio-group v-model="processingMode">
              <el-radio value="normal">普通</el-radio>
              <el-tooltip content="沿主体边缘裁切" placement="top">
                <el-radio value="cut">裁切</el-radio>
              </el-tooltip>
              <el-tooltip content="沿主体边缘描边" placement="top">
                <el-radio value="stroke">描边</el-radio>
              </el-tooltip>
              <el-tooltip content="会修改图片尺寸" placement="top">
                <el-radio value="crop">裁剪</el-radio>
              </el-tooltip>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="processingMode !== 'normal' && processingMode !== 'cut'" label="去除背景">
            <el-switch v-model="removeBackground" />
          </el-form-item>

          <!-- 描边模式选项 -->
          <template v-if="processingMode === 'stroke'">
            <el-form-item label="描边模式">
              <el-radio-group v-model="strokeStyle">
                <el-radio value="solid">实线</el-radio>
                <el-radio value="dashed">虚线</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="描边颜色">
              <div class="stroke-color">
                <el-color-picker v-model="strokeColor" />
                <el-input 
                  v-model="strokeColor" 
                  placeholder="#000000"
                  class="hex-input"
                />
              </div>
            </el-form-item>

            <el-form-item label="描边大小">
              <el-input-number 
                v-model="strokeWidth" 
                :min="1" 
                :max="100" 
                :step="1"
                size="mini"
                class="stroke-width"
              />
            </el-form-item>
          </template>

          <!-- 裁剪模式选项 -->
          <template v-if="processingMode === 'crop'">
            <el-form-item label="裁剪比例">
              <el-radio-group v-model="cropRatio">
                <el-radio value="1:1">1:1</el-radio>
                <el-radio value="3:4">3:4</el-radio>
                <el-radio value="9:16">9:16</el-radio>
                <el-radio value="custom">自定义</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="cropRatio === 'custom'" label="自定义尺寸">
              <div class="custom-ratio">
                <el-input-number 
                  v-model="customRatioWidth" 
                  :min="1" 
                  :max="9999"
                  :step="1"
                  size="mini"
                  placeholder="宽度"
                />
                <span class="ratio-separator">×</span>
                <el-input-number 
                  v-model="customRatioHeight" 
                  :min="1" 
                  :max="9999"
                  :step="1"
                  size="mini"
                  placeholder="高度"
                />
                <span class="unit">px</span>
              </div>
            </el-form-item>

            <el-form-item label="边缘长度">
              <div class="edge-length">
                <el-input-number 
                  v-model="edgeLength" 
                  :min="0" 
                  :max="999"
                  :step="1"
                  size="mini"
                  placeholder="边缘长度"
                />
                <span class="unit">px</span>
              </div>
            </el-form-item>
          </template>

          <!-- 添加示例区域 -->
          <el-form-item v-if="!imageUrl">
            <div class="demo-section">
              <div class="demo-title">
                可拖放、粘贴图片、粘贴链接，支持 jpg/png/gif/webp/bmp, 点击图片快速体验
              </div>
              <div class="demo-images">
                <div 
                  v-for="(base64, key) in imageList" 
                  :key="key"
                  class="demo-image-item"
                  @click="tryDemo(base64)"
                >
                  <img :src="base64" :alt="key">
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 右侧预览面板 -->
      <div class="preview-panel" v-if="imageUrl">
        <el-card class="preview-card">
          <template #header>
            <div class="preview-header">
              <span>{{ resultUrl ? '效果对比' : '图片预览' }}</span>
              <!-- 结果操作按钮 -->
              <div class="operation-buttons" v-if="resultUrl && !isGif">
                <el-button-group>
                  <el-tooltip content="编辑结果">
                    <el-button @click="handleEdit">
                      <el-icon><Edit /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="设置背景色">
                    <el-button @click="showBackgroundPicker = true">
                      <el-icon><picture-rounded /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="复制到剪贴板">
                    <el-button @click="copyToClipboard">
                      <el-icon><Document /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="导出图片">
                    <el-button @click="showExportDialog = true">
                      <el-icon><Download /></el-icon>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
              </div>
            </div>
          </template>

          <div class="preview-content">
            <div v-if="!resultUrl" class="single-preview">
              <div class="preview-image">
                <el-image 
                  :src="imageUrl" 
                  fit="contain"
                  :preview-src-list="[imageUrl]"
                />
              </div>
            </div>
            
            <div v-else class="compare-preview">
              <VueCompareImage
                :style="containerStyle"
                :leftImage="imageUrl"
                :rightImage="resultUrl"
                :leftLabel="'原图'"
                :rightLabel="'抠图结果'"
                :handleSize="40"
                :class="{ 'img-transparent-bg': selectedColor === 'transparent' }"
                class="img-container"
              />
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 背景色选择器弹窗 -->
    <el-dialog
      v-model="showBackgroundPicker"
      title="设置背景色"
      width="480px"
      align-center
    >
      <div class="background-picker">
        <el-tabs>
          <el-tab-pane label="基础色">
            <div class="preset-colors">
              <div 
                v-for="color in presetColors" 
                :key="color"
                class="color-item"
                :class="{ active: selectedColor === color }"
                :style="getColorItemStyle(color)"
                @click="setBackground(color)"
              >
                <el-icon v-if="selectedColor === color"><Check /></el-icon>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="渐变色">
            <div class="gradient-colors">
              <div 
                v-for="gradient in gradientColors" 
                :key="gradient.name"
                class="gradient-item"
                :class="{ active: selectedColor === gradient.value }"
                :style="{ background: gradient.value }"
                @click="setBackground(gradient.value)"
              >
                <span class="gradient-name">{{ gradient.name }}</span>
                <el-icon v-if="selectedColor === gradient.value"><Check /></el-icon>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="图片背景">
            <div class="image-background">
              <el-button @click="handleSelectBackgroundImage">
                <el-icon><Picture /></el-icon>
                选择背景图片
              </el-button>
              <div v-if="backgroundImage" 
                   class="selected-bg-preview-large"
                   @click="previewBackgroundImage">
                <img :src="backgroundUrl" alt="背景预览" class="bg-preview-img" />
                <div class="preview-overlay">
                  <el-icon><ZoomIn /></el-icon>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div class="custom-color">
          <span class="label">自定义颜色：</span>
          <el-color-picker
            v-model="selectedColor"
            @change="setBackground"
            show-alpha
          />
        </div>
      </div>
    </el-dialog>

    <!-- 导出选项弹窗 -->
    <el-dialog
      v-model="showExportDialog"
      title="导出图片"
      width="400px"
    >
      <div class="export-options">
        <el-form label-position="top" v-loading="exportLoading">
          <el-form-item label="导出格式">
            <el-radio-group v-model="exportFormat">
              <el-radio value="png">PNG</el-radio>
              <el-radio value="jpg">JPG</el-radio>
              <el-radio value="psd">PSD</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showExportDialog = false">取消</el-button>
          <el-button type="primary" @click="exportImage">
            导出
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 图片编辑弹窗 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑图片"
      :fullscreen="true"
      :show-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="edit-dialog"
    >
      <template #header="{ close }">
        <div class="edit-dialog-header">
          <span>编辑图片</span>
        </div>
      </template>

      <ImageEditor
        v-if="showEditDialog"
        :initial-base64="resultUrl"
        :origin-base64="imageUrl"
        :initial-background="selectedColor"
        @export-image="handleEditComplete"
      />
    </el-dialog>

    <!-- 背景图片预览弹窗 -->
    <el-dialog
      v-model="showImagePreview"
      title="背景图片预览"
      width="70%"
     
    >
      <div class="image-preview-container">
        <img :src="backgroundUrl" alt="背景图片" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { VueCompareImage } from 'vue3-compare-image'
import baseAPI from '@/apis/base'
import imageList from '@/utils/demo'
import ImageEditor from '@/components/ImageTool/ImgaeEditor.vue'
import UploadButton from '@/components/UploadButton.vue'
import StartProcessButton from '@/components/StartProcessButton.vue'
import { ElMessageBox } from 'element-plus'

import {
  Upload,
  Back,
  VideoPlay,
  Edit,
  Document,
  Download,
  Check,
  ZoomIn,
  Picture,
  PictureRounded,
  RefreshRight,
} from '@element-plus/icons-vue'
import message from '@/utils/message'
import { getImgBase64API } from '@/apis/common'
import { imageMattingAPI } from '@/apis/image'
import { presetColors, gradientColors } from '@/config/colors'
// 路由跳转
import { useRouter } from 'vue-router'

const router = useRouter()

// 状态变量
const imageUrl = ref('')
const isGif = ref(false)
const imagePath = ref('')
const resultUrl = ref('')
const processing = ref(false)
const aiModel = ref('rmbg14')
const showBackgroundPicker = ref(false)
const showExportDialog = ref(false)
const exportFormat = ref('png')
const loading = ref(false)
const exportLoading = ref(false)
const showEditDialog = ref(false)
const showImagePreview = ref(false)
const dpi = ref('72')


const selectedColor = ref('transparent');

const containerStyle = computed(() => {
  if (selectedColor.value === 'image' && backgroundUrl.value) {
    return {
      backgroundImage: `url(${backgroundUrl.value})`,
      backgroundSize: 'contain',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }
  }
  // 检查是否是渐变色
  if (selectedColor.value && selectedColor.value.includes('gradient')) {
    return {
      background: selectedColor.value
    }
  }
  // 纯色或透明背景
  return {
    backgroundColor: selectedColor.value
  }
})

// Add new state variables
const isEdgeOptimization = ref(true)
const edgeValue = ref(90)

// 新增的状态变量
const processingMode = ref('normal')
const removeBackground = ref(true)

// 描边模式相关
const strokeStyle = ref('solid')
const strokeColor = ref('#ffffff')
const strokeWidth = ref(10)

// 裁剪模式相关
const cropRatio = ref('1:1')
const customRatioWidth = ref(1024)
const customRatioHeight = ref(1024)
const edgeLength = ref(0)

// 在 data 中添加
const isRemoveSmallRegions = ref(aiModel.value === 'rmbg14')

// 在 script setup 部分添加
const removeSmallRegionsValue = ref(100)

// 背景图片相关
const backgroundImage = ref('')
const backgroundUrl = ref('')

// 从localStorage获取值的函数
const getFromLocalStorage = (key, defaultValue) => {
  const value = localStorage.getItem(key)
  return value !== null ? JSON.parse(value) : defaultValue
}

// 更新localStorage的函数
const updateLocalStorage = (key, value) => {
  localStorage.setItem(key, JSON.stringify(value))
}

// 初始化时从localStorage加载值
onMounted(() => {
  // 加载基本设置
  aiModel.value = getFromLocalStorage('matting_ai_model', 'rmbg14')
  dpi.value = getFromLocalStorage('matting_dpi', '72')
  isEdgeOptimization.value = getFromLocalStorage('matting_edge_optimization', true)
  edgeValue.value = getFromLocalStorage('matting_edge_value', 90)
  isRemoveSmallRegions.value = getFromLocalStorage('matting_remove_small_regions', aiModel.value === 'rmbg14')
  removeSmallRegionsValue.value = getFromLocalStorage('matting_remove_small_regions_value', 100)
  processingMode.value = getFromLocalStorage('matting_processing_mode', 'normal')

  // 加载处理模式相关的设置
  strokeStyle.value = getFromLocalStorage('matting_stroke_style', 'solid')
  strokeColor.value = getFromLocalStorage('matting_stroke_color', '#ffffff')
  strokeWidth.value = getFromLocalStorage('matting_stroke_width', 10)
  cropRatio.value = getFromLocalStorage('matting_crop_ratio', '1:1')
  customRatioWidth.value = getFromLocalStorage('matting_custom_ratio_width', 1024)
  customRatioHeight.value = getFromLocalStorage('matting_custom_ratio_height', 1024)
  edgeLength.value = getFromLocalStorage('matting_edge_length', 0)

  document.addEventListener('paste', handlePaste);
  document.addEventListener('dragover', handleDragOver);
  document.addEventListener('drop', handleDrop);
});

// 监听值的变化并更新localStorage
watch(() => aiModel.value, (val) => {
  updateLocalStorage('matting_ai_model', val)
  // 处理 rmbg14 模型的特殊逻辑
  if (val === 'rmbg14') {
    isRemoveSmallRegions.value = true
  } else {
    isRemoveSmallRegions.value = false
  }
})

watch(() => dpi.value, (val) => {
  updateLocalStorage('matting_dpi', val)
})

watch(() => isEdgeOptimization.value, (val) => {
  updateLocalStorage('matting_edge_optimization', val)
})

watch(() => edgeValue.value, (val) => {
  updateLocalStorage('matting_edge_value', val)
})

watch(() => isRemoveSmallRegions.value, (val) => {
  updateLocalStorage('matting_remove_small_regions', val)
})

watch(() => removeSmallRegionsValue.value, (val) => {
  updateLocalStorage('matting_remove_small_regions_value', val)
})

watch(() => processingMode.value, (val) => {
  updateLocalStorage('matting_processing_mode', val)
})

// 监听处理模式相关的值
watch(() => strokeStyle.value, (val) => {
  updateLocalStorage('matting_stroke_style', val)
})

watch(() => strokeColor.value, (val) => {
  updateLocalStorage('matting_stroke_color', val)
})

watch(() => strokeWidth.value, (val) => {
  updateLocalStorage('matting_stroke_width', val)
})

watch(() => cropRatio.value, (val) => {
  updateLocalStorage('matting_crop_ratio', val)
})

watch(() => customRatioWidth.value, (val) => {
  updateLocalStorage('matting_custom_ratio_width', val)
})

watch(() => customRatioHeight.value, (val) => {
  updateLocalStorage('matting_custom_ratio_height', val)
})

watch(() => edgeLength.value, (val) => {
  updateLocalStorage('matting_edge_length', val)
})

// 文件上传相关方法
const handleClickUpload = async () => {
  loading.value = true
  try {
    const response = await baseAPI('open_img_file', false)
    if (response.code === 200 && response.data.selected_file) {
      resetImage()
      console.log(response.data)
      imageUrl.value = response.data.image_url
      imagePath.value = response.data.selected_file
      message.success('上传成功')
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}

// 处理拖拽事件
const handleDragOver = (event) => {
  event.preventDefault();
};

const handleDrop = async (e) => {
  const file = e.dataTransfer?.files[0]
  if (!file) return
  
  // 检查文件类型支持jpg png webp gif bmp tiff
  const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/jpg', 'image/bmp']
  if (!validTypes.includes(file.type)) {
    message.error('只支持 JPG、PNG、WebP、GIF、BMP 格式的图片')
    return
  }

  loading.value = true
  try {
    resetImage()
    if (file.type.match('image.*')) {
      const reader = new FileReader();
      reader.onload = function (e) {
        imageUrl.value = e.target.result
        message.success('上传成功')
      };
      reader.readAsDataURL(file);
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}


// 验证是否是有效的URL
const isValidUrl = (string) => {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};

// 处理粘贴事件
const handlePaste = (event) => {
  event.preventDefault();
  const clipboardData = event.clipboardData || window.Clipboard;
  const items = clipboardData.items;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];

    if (item.kind === 'string') {
      loading.value = true
      item.getAsString((text) => {
        // 判断是不是base64
        if (text.startsWith('data:image/')) {
          console.log('粘贴的是base64:', text);
          imageUrl.value = text
          loading.value = false
        } else if (isValidUrl(text)) {
          console.log('粘贴的是URL:', text);
          getImgBase64API(text).then(res => {
            if (res.code === 200) {
              imageUrl.value = res.data.base64_image
            } else {
              const errorMsg = res.msg || '获取图片失败';
              message.error(`获取图片失败: ${errorMsg}`);
              console.error('获取图片失败:', res);
            }
          }).catch(error => {
            message.error(`获取图片失败: ${error.message || '未知错误'}`);
            console.error('API调用失败:', error);
          }).finally(() => {
            loading.value = false
          });
        } else {
          message.error('请粘贴有效的图片链接或图片文件');
          loading.value = false
        }
      });
    } else if (item.kind === 'file') {
      const type = item.type.split('/')[0];
      if (!['image'].includes(type)) {
        message.error('请粘贴图片文件');
        return;
      }
      loading.value = true
      const file = item.getAsFile();
      const reader = new FileReader();
      reader.onload = (e) => {
        console.log('粘贴的是图片:', e.target.result);
        imageUrl.value = e.target.result
        loading.value = false
      };
      reader.readAsDataURL(file);
    }
  }
};

// 处理相关方法
const startProcess = async () => {
  processing.value = true
  try {
    const params = {
      image_path: imagePath.value || imageUrl.value,
      mode: processingMode.value,
      ai_model: aiModel.value,
      remove_background: removeBackground.value,
      is_edge_optimization: isEdgeOptimization.value,
      edge_value: edgeValue.value,
      background_color: selectedColor.value === 'image' ? 'transparent' : selectedColor.value,
      is_remove_small_regions: isRemoveSmallRegions.value,
      is_remove_small_regions_value: removeSmallRegionsValue.value
    }

    // 如果是图片背景，添加背景图片路径
    if (selectedColor.value === 'image' && backgroundImage.value) {
      params['background_color_image'] = backgroundImage.value
    }

    // 根据不同模式添加参数
    if (processingMode.value === 'stroke') {
      params.stroke_style = strokeStyle.value
      params.stroke_color = strokeColor.value
      params.stroke_width = strokeWidth.value
    } else if (processingMode.value === 'crop') {
      params.crop_ratio = cropRatio.value === 'custom' ? 'custom' : cropRatio.value
      params.width = customRatioWidth.value
      params.height = customRatioHeight.value
      params.subject_retention = edgeLength.value
    }

    const response = await imageMattingAPI('predict', params)
    if (response.code === 200) {
      resultUrl.value = response.data.no_bg_image
      // 更新copyBase64
      copyBase64.value = response.data.no_bg_image
      message.success('处理完成')
      if (response.data.is_gif) {
        isGif.value = true
        message.success(`处理完成，已保存到${response.data.save_path}`)
      }
    } else {
      throw new Error(response.msg)
    }
  } catch (error) {
    message.error(error.message)
    // 跳转到设置页面
    router.push('/settings')
  } finally {
    processing.value = false
  }
}

const resetImage = () => {
  imageUrl.value = ''
  imagePath.value = ''
  resultUrl.value = ''
}

// 结果操作相关方法
const handleEdit = () => {
  showEditDialog.value = true
}

const handleEditComplete = (data) => {
  resultUrl.value = data.base64;
  selectedColor.value = data.backgroundColor;  // 更新背景色
  showEditDialog.value = false;
  setCopyBase64();  // 更新带背景色的图片
  message.success('编辑已保存');
}


const setBackground = async(color) => {
  selectedColor.value = color
  // 如果选择了非图片背景，清除背景图片
  if (color !== 'image') {
    backgroundImage.value = ''
    backgroundUrl.value = ''
  }
  showBackgroundPicker.value = false
  if (!isGif.value) {
    await setCopyBase64()
  }
}

// 选择背景图片
const handleSelectBackgroundImage = async () => {
  try {
    const response = await baseAPI('open_img_file', false)
    if (response.code === 200 && response.data.selected_file) {
      backgroundUrl.value = response.data.image_url
      backgroundImage.value = response.data.selected_file
      // 设置为图片背景模式
      selectedColor.value = 'image'
      message.success('背景图片选择成功')
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  }
}

// 预览背景图片
const previewBackgroundImage = () => {
  if (backgroundUrl.value) {
    showImagePreview.value = true
  }
}

// base64转blob
function base64ToBlob(base64Data) {
  const parts = base64Data.split(';base64,');
  const contentType = parts[0].split(':')[1];
  const byteCharacters = atob(parts[1]);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
    const slice = byteCharacters.slice(offset, offset + 1024);
    const byteNumbers = new Array(slice.length);

    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  return new Blob(byteArrays, { type: contentType });
}

const copyBase64 = ref('')
const setCopyBase64 = async () => {
  console.log('setCopyBase64', selectedColor.value)
  if (selectedColor.value !== 'transparent') {
      const response = await baseAPI('get_base64_set_bg', {
        base64_data: resultUrl.value,
        hex_color: selectedColor.value
      })
      if (response.code === 200) {
        copyBase64.value = response.data.base64_data
      } else {
        message.error('合成图片失败')
        return // 添加 return 以防止继续执行
      } 
    } else {
      copyBase64.value = resultUrl.value
    }
}

// 奇怪的bug，在复制事件里面，动态获取的数据不能写入粘贴板
const copyToClipboard = async () => {
  try {
    const blob = base64ToBlob(copyBase64.value);
    const item = new ClipboardItem({ 'image/png': blob });
    await navigator.clipboard.write([item]);
    message.success('已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    message.error(`复制失败: ${error.message || '未知错误'}`);
  }
}

const exportImage = async () => {
  // TODO: 实现导出功能
  exportLoading.value = true
  const response = await baseAPI('save_image_dialog', {
    base64_data: resultUrl.value,
    extension: exportFormat.value,
    file_name: imagePath.value,
    hex_color: selectedColor.value,
    origin_image: imagePath.value || imageUrl.value,
    background_color_image: backgroundImage.value,
    dpi: dpi.value
  })
  if (response.code === 200) {
    message.success('导出成功')
    showExportDialog.value = false
  } else {
    message.error('导出失败')
  }
  exportLoading.value = false
}

// 修改计算属性
const getColorPreviewStyle = computed(() => {
  // 如果有背景图片，显示背景图片
  if (backgroundImage.value && !selectedColor.value.includes('gradient') && selectedColor.value !== 'transparent') {
    return {
      backgroundImage: `url(${backgroundUrl.value})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center'
    }
  }
  
  // 检查是否是渐变色
  if (selectedColor.value && selectedColor.value.includes('gradient')) {
    return {
      background: selectedColor.value
    }
  }
  return getColorItemStyle(selectedColor.value)
})

// 添加颜色项样式计算方法
const getColorItemStyle = (color) => {
  const style = {
    backgroundColor: color === 'transparent' ? 'transparent' : color,
  }
  
  if (color === 'transparent') {
    style.backgroundImage = `
      linear-gradient(45deg, #ccc 25%, transparent 25%),
      linear-gradient(-45deg, #ccc 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #ccc 75%),
      linear-gradient(-45deg, transparent 75%, #ccc 75%)
    `
    style.backgroundSize = '10px 10px'
    style.backgroundPosition = '0 0, 0 5px, 5px -5px, -5px 0px'
  }
  
  return style
}

const tryDemo = (base64) => {
  resetImage()
  imageUrl.value = base64
}

// 添加重置到默认值的方法
const resetToDefault = () => {
  ElMessageBox.confirm(
    '确定要重置所有选项到默认值吗？这将清除所有已保存的设置。',
    '重置确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      // 清除所有相关的 localStorage
      const keys = [
        'matting_ai_model',
        'matting_dpi',
        'matting_edge_optimization',
        'matting_edge_value',
        'matting_remove_small_regions',
        'matting_remove_small_regions_value',
        'matting_processing_mode',
        'matting_stroke_style',
        'matting_stroke_color',
        'matting_stroke_width',
        'matting_crop_ratio',
        'matting_custom_ratio_width',
        'matting_custom_ratio_height',
        'matting_edge_length'
      ]

      keys.forEach(key => localStorage.removeItem(key))

      // 重置所有值到默认值
      aiModel.value = 'rmbg14'
      dpi.value = '72'
      isEdgeOptimization.value = true
      edgeValue.value = 90
      isRemoveSmallRegions.value = true
      removeSmallRegionsValue.value = 100
      processingMode.value = 'normal'
      strokeStyle.value = 'solid'
      strokeColor.value = '#ffffff'
      strokeWidth.value = 10
      cropRatio.value = '1:1'
      customRatioWidth.value = 1024
      customRatioHeight.value = 1024
      edgeLength.value = 0
      selectedColor.value = 'transparent'
      backgroundImage.value = ''
      backgroundUrl.value = ''

      message.success('已重置所有选项到默认值')
    })
    .catch(() => {
      // 用户取消重置
    })
}

onMounted(() => {
  document.addEventListener('paste', handlePaste);
  document.addEventListener('dragover', handleDragOver);
  document.addEventListener('drop', handleDrop);
});
onUnmounted(() => {
  document.removeEventListener('paste', handlePaste);
  document.removeEventListener('dragover', handleDragOver);
  document.removeEventListener('drop', handleDrop);
}); 
</script>

<style lang="scss" scoped>
.single-matting {
  height: 100%;  
  .layout-container {
    height: 100%;
    display: flex;
    gap: 24px;
    
    // 左侧配置面板
    .config-panel {
      flex: 0 0 400px;
      margin-right: 10px;
      
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 10px;
          
          .el-form-item__label {
            font-weight: 500;
          }
        }
      }
      
      .upload-content {
        display: flex;
        gap: 8px;
      }
      
      .demo-section {
        margin-top: 16px;
        
        .demo-title {
          color: var(--el-text-color-secondary);
          font-size: 14px;
          margin-bottom: 20px;
          text-align: left;
        }
        
        .demo-images {
          display: grid;
          grid-template-columns: repeat(3, 180px); // 固定宽度的三列布局
          gap: 20px;
          
          .demo-image-item {
            width: 180px;
            height: 180px;
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--el-border-color-lighter);
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            
            &:hover {
              transform: translateY(-5px);
              box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.1);
              border-color: var(--el-color-primary-light-5);
            }
            
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.3s ease;
            }
            
            &:hover img {
              transform: scale(1.05);
            }
          }
        }
      }
    }
    
    // 右侧预览面板
    .preview-panel {
      flex: 1;
      
      .preview-card {
        height: auto;
        
        :deep(.el-card__header) {
          .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 32px;
          }
        }
        
        .preview-content {          
          .single-preview {
            .preview-image {
              max-width: 100%;
              max-height: 59vh;
              display: flex;
              justify-content: center;
              align-items: center;
              
              :deep(.el-image) {
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                
                img {
                  max-width: 100%;
                  max-height: 59vh;
                  object-fit: contain;
                }
              }
            }
          }
          
          .compare-preview {
            .img-container {
              max-width: 100%;
              max-height: 59vh;
              display: flex;
              justify-content: center;
              align-items: center;
              
              :deep(img) {
                max-width: 100%;
                max-height: 100%;
                object-fit: contain;
              }
            }
          }
        }
      }
    }
  }
}

.background-picker {
  .preset-colors {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
    margin-bottom: 20px;
    
    .color-item {
      position: relative;
      width: 64px;
      height: 64px;
      border-radius: 8px;
      cursor: pointer;
      border: 2px solid #e4e7ed;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
      
      &:hover {
        transform: scale(1.05);
      }
      
      &.active {
        border-color: var(--el-color-primary);
        
        .el-icon {
          color: #fff;
          filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
        }
      }
    }
  }

  .gradient-colors {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
    margin-bottom: 20px;
    
    .gradient-item {
      position: relative;
      width: 64px;
      height: 64px;
      border-radius: 8px;
      cursor: pointer;
      border: 2px solid #e4e7ed;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
      
      &:hover {
        transform: scale(1.05);
      }
      
      &.active {
        border-color: var(--el-color-primary);
      }

      .gradient-name {
        color: #fff;
        text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
        font-size: 12px;
        position: absolute;
        bottom: 4px;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
      }

      .el-icon {
        color: #fff;
        filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
      }
    }
  }

  .image-background {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;

    .selected-bg-preview-large {
      width: 100%;
      height: 150px;
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      overflow: hidden;
      position: relative;
      cursor: pointer;
      
      &:hover .preview-overlay {
        opacity: 1;
      }
      
      .bg-preview-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .preview-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.4);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s;
        
        .el-icon {
          color: white;
          font-size: 32px;
        }
      }
    }
  }

  .custom-color {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding-top: 16px;
    margin-top: 8px;
    border-top: 1px solid var(--el-border-color-lighter);

    .label {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
}

.export-options {
  .el-form {
    :deep(.el-form-item) {
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}


.img-transparent-bg {
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
  background-image: linear-gradient(45deg,
      #eee 25%,
      transparent 0,
      transparent 75%,
      #eee 0,
      #eee), linear-gradient(45deg, #eee 25%, #fff 0, #fff 75%, #eee 0, #eee);
}

.stacked-linear {
  background: linear-gradient(217deg,
      rgba(255, 0, 0, 0.8),
      rgba(255, 0, 0, 0) 70.71%),
    linear-gradient(127deg, rgba(0, 255, 0, 0.8), rgba(0, 255, 0, 0) 70.71%),
    linear-gradient(336deg, rgba(0, 0, 255, 0.8), rgba(0, 0, 255, 0) 70.71%);
}

// 修改对比图容器样式
.compare-preview {
  .img-container {
    border-radius: 8px;
    overflow: hidden;
    transition: background-color 0.3s;
  }
}

.demo-section {
  width: 100%;
  margin-top: 16px;
  
  .demo-title {
    color: var(--el-text-color-secondary);
    font-size: 14px;
    margin-bottom: 20px;
    text-align: left;
  }
  
  .demo-images {
    display: grid;
    grid-template-columns: repeat(3, 180px); // 固定宽度的三列布局
    gap: 20px;
    
    .demo-image-item {
      width: 180px;
      height: 180px;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid var(--el-border-color-lighter);
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.1);
        border-color: var(--el-color-primary-light-5);
      }
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
      
      &:hover img {
        transform: scale(1.05);
      }
    }
  }
}

.edge-optimization-container {
  display: flex;
  align-items: center;
  gap: 16px;  
  .edge-value-slider {
    width: 180px;
    margin-left: 16px;
    
  }
}


.stroke-color {
  display: flex;
  align-items: center;
  gap: 12px;

  .hex-input {
    width: 120px;
  }
}

.custom-ratio {
  display: flex;
  align-items: center;
  gap: 12px;

  .ratio-separator {
    color: var(--el-text-color-regular);
    font-size: 16px;
  }
  
  .unit {
    color: var(--el-text-color-secondary);
    margin-left: 4px;
  }
}

.stroke-width {
  width: 120px;
}

.edge-length {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .unit {
    color: var(--el-text-color-secondary);
  }
}

// 添加新的样式
.color-preview {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  margin-right: 8px;
  vertical-align: middle;
  border: 1px solid var(--el-border-color-lighter);
}
.small-regions-container {
  display: flex;
  align-items: center;
  gap: 16px;
  
  .small-regions-value {
    width: 180px;
    margin-left: 16px;
  }
}

.selected-bg-preview {
  margin-left: 12px;
  width: 60px;
  height: 40px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  
  &:hover .preview-overlay {
    opacity: 1;
  }
  
  .bg-preview-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
    
    .el-icon {
      color: white;
      font-size: 20px;
    }
  }
}

.image-background {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;

  .selected-bg-preview-large {
    width: 100%;
    height: 150px;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    
    &:hover .preview-overlay {
      opacity: 1;
    }
    
    .bg-preview-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .preview-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s;
      
      .el-icon {
        color: white;
        font-size: 32px;
      }
    }
  }
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  
  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }
}

.label-with-icon {
  display: flex;
  align-items: center;
  gap: 4px;

  .help-icon {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    cursor: help;
    
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style> 