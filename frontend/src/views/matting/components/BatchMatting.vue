<template>
  <div class="batch-matting">
    <div class="layout-container">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <el-form label-position="left">
          <el-form-item label="输入目录">
            <div class="folder-select">
              <el-tooltip :content="inputFolder ? inputFolder : '选择包含图片的文件夹'" placement="top" effect="light">
              <el-input
                v-model="inputFolder"
                placeholder="选择包含图片的文件夹"
              >
                <template #append>
                  <el-button @click="selectInputFolder">
                    <el-icon><Folder /></el-icon>
                    选择
                  </el-button>
                </template>
              </el-input>
              </el-tooltip>
            </div>
          </el-form-item>

          <el-form-item label="输出目录">
            <div class="folder-select">
              <el-tooltip :content="outputFolder ? outputFolder : '选择结果保存位置, 默认在输入目录下创建result文件夹'" placement="bottom" effect="light">
              <el-input
                v-model="outputFolder"
                placeholder="选择结果保存位置, 默认在输入目录下创建result文件夹"
              >
                <template #append>
                  <el-button @click="selectOutputFolder">
                    <el-icon><Folder /></el-icon>
                    选择
                  </el-button>
                </template>
              </el-input>
            </el-tooltip>
            </div>
          </el-form-item>

          <el-form-item label="文件名保留模式">
            <el-radio-group v-model="fileNameMode">
              <el-radio value="original">原文件名</el-radio>
              <el-radio value="random">原文件名_随机字符</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <template #label>
              <div class="label-with-icon">
                <span>处理模型</span>
                <el-tooltip
                  placement="top"
                  effect="light"
                >
                  <template #content>
                    <div class="shortcut-help">
                      <li>人像: 适用于人像抠图，效果好</li>
                      <li>快速: 速度快，效果好</li>
                      <li>标准: 速度相对慢，保留主体较多</li>
                      <li>高质量: 速度慢，保留主体较少</li>
                      <li>rmbg2: 速度慢，保留主体较少</li>
                    </div>
                  </template>
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-radio-group v-model="aiModel">
              <el-radio value="modnet">人像</el-radio>
              <el-radio value="rmbg14">快速</el-radio>
              <el-radio value="fast">标准</el-radio>
              <el-radio value="quality">高质量</el-radio>
              <el-radio value="rmbg2">rmbg2</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 并发数 -->
          <el-form-item>
            <template #label>
              <div class="label-with-icon">
                <span>并发量</span>
                <el-tooltip
                  placement="top"
                  effect="light"
                >
                  <template #content>
                    <div class="shortcut-help">
                    同时处理的数量，根据系统内存来设置，高质量模型不建议设置过大
                    </div>
                  </template>
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <div class="edge-optimization-container">
              <div class="edge-value-slider inline">
                <el-slider
                  v-model="core"
                  :min="1"
                  :max="8"
                  :step="1"
                  :format-tooltip="(val) => `${val}`"
                  class="edge-slider"
                >
                </el-slider>
              </div>
            </div>
          </el-form-item>

         <!-- 分辨率 72 96 150 300 600 -->
         <el-form-item label="分辨率">
            <template #label>
              <div class="label-with-icon">
                <span>分辨率</span>
                <el-tooltip
                  content="注意：psd文件不支持设置分辨率，默认使用72dpi"
                  placement="top"
                  effect="light"
                >
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-radio-group v-model="dpi" class="inline">
              <el-radio value="72">72</el-radio>
              <el-radio value="96">96</el-radio>
              <el-radio value="150">150</el-radio>
              <el-radio value="300">300</el-radio>
              <el-radio value="600">600</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="保存格式">
            <el-radio-group v-model="saveFormat">
              <el-radio value="png">PNG</el-radio>
              <el-radio value="jpg">JPG</el-radio>
              <el-radio value="psd">PSD</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="背景设置">
            <el-button @click="showBackgroundPicker = true">
              <div class="color-preview" :style="getColorPreviewStyle"></div>
              设置背景色
            </el-button>
            <!-- 显示已选择的背景图片预览 -->
            <div v-if="backgroundImage && !selectedColor.includes('gradient') && selectedColor !== 'transparent'" 
                 class="selected-bg-preview" 
                 @click="previewBackgroundImage">
              <img :src="backgroundUrl" alt="背景预览" class="bg-preview-img" />
              <div class="preview-overlay">
                <el-icon><ZoomIn /></el-icon>
              </div>
            </div>
          </el-form-item>

          <el-form-item>
            <template #label>
              <div class="label-with-icon">
                <span>边缘优化</span>
                <el-tooltip
                  placement="top"
                  effect="light"
                >
                  <template #content>
                    <div class="shortcut-help">
                      黑边优化,值越大边缘越柔和，值越小边缘越锐化
                    </div>
                  </template>
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <div class="edge-optimization-container">
              <el-switch
                v-model="isEdgeOptimization"
                class="edge-switch"
              />
              <div v-show="isEdgeOptimization" class="edge-value-slider inline">
                <el-slider
                  v-model="edgeValue"
                  :min="0"
                  :max="360"
                  :step="1"
                  :marks="{ 90: '90' }"
                  :format-tooltip="(val) => `${val}°`"
                  class="edge-slider"
                >
                </el-slider>
              </div>
            </div>
          </el-form-item>

          <el-form-item>
            <template #label>
              <div class="label-with-icon">
                <span>去除小区域</span>
                <el-tooltip
                  placement="top"
                  effect="light"
                >
                  <template #content>
                    <div class="shortcut-help">
                      移除小区域,值越大去除的区域越大
                    </div>
                  </template>
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <div class="small-regions-container">
              <el-switch 
                v-model="isRemoveSmallRegions" 
                :default-value="aiModel === 'rmbg14'"
                class="small-regions-switch"
              />
              <div v-show="isRemoveSmallRegions" class="small-regions-value inline">
                <el-slider
                  v-model="removeSmallRegionsValue" 
                  :min="1" 
                  :max="1000"
                  :step="1"
                  :format-tooltip="(val) => `${val}`"
                  class="small-regions-slider"
                />
              </div>
            </div>
          </el-form-item>

          <el-form-item label="处理模式">
            <el-radio-group v-model="processingMode">
              <el-radio value="normal">普通</el-radio>
              <el-tooltip content="沿主体边缘裁切" placement="top">
                <el-radio value="cut">裁切</el-radio>
              </el-tooltip>
              <el-tooltip content="沿主体边缘描边" placement="top">
                <el-radio value="stroke">描边</el-radio>
              </el-tooltip>
              <el-tooltip content="会修改图片尺寸" placement="top">
                <el-radio value="crop">裁剪</el-radio>
              </el-tooltip>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="processingMode !== 'normal' && processingMode !== 'cut'" label="去除背景">
            <el-switch v-model="removeBackground" />
          </el-form-item>

          <!-- 描边模式选项 -->
          <template v-if="processingMode === 'stroke'">
            <el-form-item label="描边模式">
              <el-radio-group v-model="strokeStyle">
                <el-radio value="solid">实线</el-radio>
                <el-radio value="dashed">虚线</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="描边颜色">
              <div class="stroke-color">
                <el-color-picker v-model="strokeColor" />
                <el-input 
                  v-model="strokeColor" 
                  placeholder="#000000"
                  class="hex-input"
                />
              </div>
            </el-form-item>

            <el-form-item label="描边大小">
              <el-input-number 
                v-model="strokeWidth" 
                :min="1" 
                :max="100" 
                :step="1"
                size="mini"
                class="stroke-width"
              />
            </el-form-item>
          </template>

          <!-- 裁剪模式选项 -->
          <template v-if="processingMode === 'crop'">
            <el-form-item label="裁剪比例">
              <el-radio-group v-model="cropRatio">
                <el-radio value="1:1">1:1</el-radio>
                <el-radio value="3:4">3:4</el-radio>
                <el-radio value="9:16">9:16</el-radio>
                <el-radio value="custom">自定义</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="cropRatio === 'custom'" label="自定义尺寸">
              <div class="custom-ratio">
                <el-input-number 
                  v-model="customRatioWidth" 
                  :min="1" 
                  :max="9999"
                  :step="1"
                  size="mini"
                  placeholder="宽度"
                />
                <span class="ratio-separator">×</span>
                <el-input-number 
                  v-model="customRatioHeight" 
                  :min="1" 
                  :max="9999"
                  :step="1"
                  size="mini"
                  placeholder="高度"
                />
                <span class="unit">px</span>
              </div>
            </el-form-item>

            <el-form-item label="边缘长度">
              <div class="edge-length">
                <el-input-number 
                  v-model="subjectRetention" 
                  :min="0" 
                  :max="999"
                  :step="1"
                  size="mini"
                  placeholder="边缘长度"
                />
                <span class="unit">px</span>
              </div>
            </el-form-item>
          </template>

          <div class="action-buttons">
            <StartProcessButton 
              text="开始处理" 
              :loading="processing" 
              :disabled="!canStart || processing"
              @startProcess="startProcessing"
            />
            <el-button
              v-if="processing"
              @click="pauseProcessing"
              :type="isPaused ? 'success' : 'warning'"
            >
              <el-icon><VideoPause v-if="!isPaused"/><VideoPlay v-else/></el-icon>
              {{ isPaused ? '继续处理' : '暂停处理' }}
            </el-button>
            <el-button
              v-if="processing"
              type="danger"
              @click="stopProcessing"
            >
              <el-icon><CircleClose /></el-icon>
              终止处理
            </el-button>
            <el-button
              type="info"
              @click="resetToDefault"
            >
              <el-icon><RefreshRight /></el-icon>
              重置选项
            </el-button>
          </div>
        </el-form>
      </div>

      <!-- 右侧进度和结果面板 -->
      <div class="result-panel" v-if="fileList.length > 0">
        <el-card class="progress-card">
          <template #header>
            <div class="progress-header">
              <div class="progress-info">
                <div class="progress-title">
                  <div class="left">
                    <span class="title">处理进度</span>
                    <el-tag size="small" :type="getProgressTagType">
                      {{ processedCount }}/{{ fileList.length }}
                    </el-tag>
                  </div>
                  <div class="right">
                    <el-tag 
                      v-if="processing && !isPaused" 
                      size="small" 
                      type="warning"
                    >
                      <el-icon><Timer /></el-icon>
                      预计剩余: {{ remainingTime }}
                    </el-tag>
                  </div>
                </div>
                <el-progress
                  :percentage="totalProgress"
                  :status="processStatus"
                  :stroke-width="8"
                  :format="progressFormat"
                  class="total-progress"
                />
              </div>
              <div class="progress-stats" v-if="errorCount > 0">
                <el-tag size="small" type="danger">
                  <el-icon><CircleCloseFilled /></el-icon>
                  失败: {{ errorCount }}
                </el-tag>
              </div>
            </div>
          </template>

          <div class="file-list">
            <el-scrollbar 
              height="400px" 
              class="file-list-content"
              @scroll="handleScroll"
              ref="scrollbarRef"
            >
              <div
                v-for="(file, index) in displayList"
                :key="file.path"
                class="file-item"
                :class="{ 
                  'processing': file.processing,
                  'processed': file.processed,
                  'error': file.error
                }"
              >
                <div 
                  class="item-preview" 
                >
                  <el-image
                    :src="file.resultUrl || file.originalUrl"
                    fit="cover"
                    class="preview-thumbnail"
                    lazy
                    :preview-teleported="true"
                    :preview-src-list="[file.resultUrl || file.originalUrl]"
                    :initial-index="0"
                    :hide-on-click-modal="false"
                  >
                  
                  </el-image>
                  <div class="preview-badge" v-if="file.resultUrl">
                    <el-tag 
                      size="small" 
                      type="success"
                      @click.stop="openResult(file)"
                      class="clickable"
                    >
                      处理后
                    </el-tag>
                  </div>
                  <div class="preview-badge" v-else>
                    <el-tag size="small" type="info">原图</el-tag>
                  </div>
                </div>
                <div class="item-content">
                  <div class="item-info">
                    <span class="file-name" :title="file.name">{{ file.name }}</span>
                    <div class="status-icon">
                      <el-icon v-if="file.processed" @click="openResult(file)" class="success"><CircleCheckFilled /></el-icon>
                      <el-icon v-else-if="file.error" class="error"><CircleCloseFilled /></el-icon>
                      <el-icon v-else-if="file.processing" class="processing"><Loading /></el-icon>
                      <el-icon v-else class="waiting"><Clock /></el-icon>
                    </div>
                  </div>
                  <el-progress
                    v-if="file.processing || file.processed"
                    :percentage="file.progress"
                    :status="file.error ? 'exception' : file.processed ? 'success' : ''"
                  />
                </div>
              </div>
              <div v-if="loading" class="loading-more">
                <el-icon class="loading"><Loading /></el-icon>
                <span>加载中...</span>
              </div>
            </el-scrollbar>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 添加背景色选择器弹窗 -->
    <el-dialog
      v-model="showBackgroundPicker"
      title="设置背景色"
      width="480px"
      align-center
    >
      <div class="background-picker">
        <el-tabs>
          <el-tab-pane label="基础色">
            <div class="preset-colors">
              <div 
                v-for="color in presetColors" 
                :key="color"
                class="color-item"
                :class="{ active: selectedColor === color }"
                :style="getColorItemStyle(color)"
                @click="setBackground(color)"
              >
                <el-icon v-if="selectedColor === color"><Check /></el-icon>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="渐变色">
            <div class="gradient-colors">
              <div 
                v-for="gradient in gradientColors" 
                :key="gradient.name"
                class="gradient-item"
                :class="{ active: selectedColor === gradient.value }"
                :style="{ background: gradient.value }"
                @click="setBackground(gradient.value)"
              >
                <span class="gradient-name">{{ gradient.name }}</span>
                <el-icon v-if="selectedColor === gradient.value"><Check /></el-icon>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="图片背景">
            <div class="image-background">
              <el-button @click="handleSelectBackgroundImage">
                <el-icon><Picture /></el-icon>
                选择背景图片
              </el-button>
              <div v-if="backgroundImage" 
                   class="selected-bg-preview-large"
                   @click="previewBackgroundImage">
                <img :src="backgroundUrl" alt="背景预览" class="bg-preview-img" />
                <div class="preview-overlay">
                  <el-icon><ZoomIn /></el-icon>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

        <div class="custom-color">
          <span class="label">自定义颜色：</span>
          <el-color-picker
            v-model="selectedColor"
            @change="setBackground"
            show-alpha
          />
        </div>
      </div>
    </el-dialog>

    <!-- 背景图片预览弹窗 -->
    <el-dialog
      v-model="showImagePreview"
      title="背景图片预览"
      width="70%"
      align-center
    >
      <div class="image-preview-container">
        <img :src="backgroundUrl" alt="背景图片" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElLoading, ElMessageBox } from 'element-plus'
import {
  Folder,
  VideoPlay,
  VideoPause,
  CircleClose,
  CircleCheckFilled,
  CircleCloseFilled,
  Clock,
  Loading,
  Picture,
  Timer,
  Check,
  ZoomIn,
  RefreshRight,
} from '@element-plus/icons-vue'
import message from '@/utils/message'
import baseAPI from '@/apis/base'
import { imageMattingAPI } from '@/apis/image'
import { presetColors, gradientColors } from '@/config/colors'
import StartProcessButton from '@/components/StartProcessButton.vue'


// 状态变量
const inputFolder = ref('')
const outputFolder = ref('')
const processingMode = ref('normal')
const saveFormat = ref('png')
const fileList = ref([])
const processing = ref(false)
const isPaused = ref(false)
const processedCount = ref(0)
const startTime = ref(null)
const pageSize = 20 // 每页显示数量
const currentPage = ref(1)
const aiModel = ref('rmbg14')
const isRemoveSmallRegions = ref(aiModel.value === 'rmbg14')
const removeSmallRegionsValue = ref(100)
const fileNameMode = ref('original')
const dpi = ref('72')
const core = ref(2)

// 添加滚动相关的状态
const scrollbarRef = ref(null)
const loading = ref(false)
const loadingThreshold = 100 // 距离底部多少像素时触发加载

// 添加新的状态变量
const isEdgeOptimization = ref(true)
const edgeValue = ref(90)

// 修改处理模式相关变量
const removeBackground = ref(true)

// 描边模式相关
const strokeStyle = ref('solid')
const strokeColor = ref('#ffffff')
const strokeWidth = ref(10)

// 裁剪模式相关
const cropRatio = ref('1:1')
const customRatioWidth = ref(1024)
const customRatioHeight = ref(1024)
const subjectRetention = ref(0) // 替换 subjectRetention

// 添加新的状态变量
const showBackgroundPicker = ref(false)
const selectedColor = ref('transparent')

// 添加新的状态变量
const backgroundImage = ref('')
const backgroundUrl = ref('')
const showImagePreview = ref(false)

// 添加取消标志
const cancelFlag = ref(false)

// 从localStorage获取值的函数
const getFromLocalStorage = (key, defaultValue) => {
  const value = localStorage.getItem(key)
  return value !== null ? JSON.parse(value) : defaultValue
}

// 更新localStorage的函数
const updateLocalStorage = (key, value) => {
  localStorage.setItem(key, JSON.stringify(value))
}

// 初始化时从localStorage加载值
onMounted(() => {
  // 加载基本设置
  aiModel.value = getFromLocalStorage('batch_matting_ai_model', 'rmbg14')
  dpi.value = getFromLocalStorage('batch_matting_dpi', '72')
  isEdgeOptimization.value = getFromLocalStorage('batch_matting_edge_optimization', true)
  edgeValue.value = getFromLocalStorage('batch_matting_edge_value', 90)
  isRemoveSmallRegions.value = getFromLocalStorage('batch_matting_remove_small_regions', aiModel.value === 'rmbg14')
  removeSmallRegionsValue.value = getFromLocalStorage('batch_matting_remove_small_regions_value', 100)
  processingMode.value = getFromLocalStorage('batch_matting_processing_mode', 'normal')
  saveFormat.value = getFromLocalStorage('batch_matting_save_format', 'png')
  fileNameMode.value = getFromLocalStorage('batch_matting_file_name_mode', 'original')

  // 加载处理模式相关的设置
  strokeStyle.value = getFromLocalStorage('batch_matting_stroke_style', 'solid')
  strokeColor.value = getFromLocalStorage('batch_matting_stroke_color', '#ffffff')
  strokeWidth.value = getFromLocalStorage('batch_matting_stroke_width', 10)
  cropRatio.value = getFromLocalStorage('batch_matting_crop_ratio', '1:1')
  customRatioWidth.value = getFromLocalStorage('batch_matting_custom_ratio_width', 1024)
  customRatioHeight.value = getFromLocalStorage('batch_matting_custom_ratio_height', 1024)
  subjectRetention.value = getFromLocalStorage('batch_matting_subject_retention', 0)

  core.value = getFromLocalStorage('batch_matting_core', 2)

})

// 监听值的变化并更新localStorage
watch(() => aiModel.value, (val) => {
  updateLocalStorage('batch_matting_ai_model', val)
  // 处理 rmbg14 模型的特殊逻辑
  if (val === 'rmbg14') {
    isRemoveSmallRegions.value = true
  } else {
    isRemoveSmallRegions.value = false
  }
})

watch(() => dpi.value, (val) => {
  updateLocalStorage('batch_matting_dpi', val)
})

watch(() => isEdgeOptimization.value, (val) => {
  updateLocalStorage('batch_matting_edge_optimization', val)
})

watch(() => edgeValue.value, (val) => {
  updateLocalStorage('batch_matting_edge_value', val)
})

watch(() => isRemoveSmallRegions.value, (val) => {
  updateLocalStorage('batch_matting_remove_small_regions', val)
})

watch(() => removeSmallRegionsValue.value, (val) => {
  updateLocalStorage('batch_matting_remove_small_regions_value', val)
})

watch(() => processingMode.value, (val) => {
  updateLocalStorage('batch_matting_processing_mode', val)
})

watch(() => saveFormat.value, (val) => {
  updateLocalStorage('batch_matting_save_format', val)
})

watch(() => fileNameMode.value, (val) => {
  updateLocalStorage('batch_matting_file_name_mode', val)
})

// 监听处理模式相关的值
watch(() => strokeStyle.value, (val) => {
  updateLocalStorage('batch_matting_stroke_style', val)
})

watch(() => strokeColor.value, (val) => {
  updateLocalStorage('batch_matting_stroke_color', val)
})

watch(() => strokeWidth.value, (val) => {
  updateLocalStorage('batch_matting_stroke_width', val)
})

watch(() => cropRatio.value, (val) => {
  updateLocalStorage('batch_matting_crop_ratio', val)
})

watch(() => customRatioWidth.value, (val) => {
  updateLocalStorage('batch_matting_custom_ratio_width', val)
})

watch(() => customRatioHeight.value, (val) => {
  updateLocalStorage('batch_matting_custom_ratio_height', val)
})

watch(() => subjectRetention.value, (val) => {
  updateLocalStorage('batch_matting_subject_retention', val)
})

watch(() => core.value, (val) => {
  updateLocalStorage('batch_matting_core', val)
})

// 计算属性
const canStart = computed(() => {
  return inputFolder.value  && fileList.value.length > 0
})

const totalProgress = computed(() => {
  if (fileList.value.length === 0) return 0
  return Math.round((processedCount.value / fileList.value.length) * 100)
})

const processStatus = computed(() => {
  if (isPaused.value) return 'warning'
  if (processing.value) return ''
  return totalProgress.value === 100 ? 'success' : ''
})

const remainingTime = computed(() => {
  if (!startTime.value || processedCount.value === 0) return '计算中...'
  
  const elapsed = (Date.now() - startTime.value) / 1000
  const avgTimePerFile = elapsed / processedCount.value
  const remaining = (fileList.value.length - processedCount.value) * avgTimePerFile
  
  if (remaining < 60) return `${Math.round(remaining)}秒`
  if (remaining < 3600) return `${Math.round(remaining / 60)}分钟`
  return `${Math.round(remaining / 3600)}小时${Math.round((remaining % 3600) / 60)}分钟`
})

// 计算当前显示的列表
const displayList = computed(() => {
  const start = 0
  const end = currentPage.value * pageSize
  return fileList.value.slice(start, end)
})

// 是否还有更多数据
const hasMore = computed(() => {
  return displayList.value.length < fileList.value.length
})

// 修改处理滚动事件的方法
const handleScroll = async (e) => {
  if (loading.value || !hasMore.value) return

  // 获取 scrollbar 实例
  const scrollbar = scrollbarRef.value
  if (!scrollbar) return

  // 获取滚动容器
  const wrap = scrollbar.wrapRef
  if (!wrap) return

  const {
    scrollTop,
    scrollHeight,
    clientHeight
  } = wrap

  // 当滚动到距离底部loadingThreshold像素时加载更多
  if (scrollHeight - (scrollTop + clientHeight) <= loadingThreshold) {
    await loadMoreData()
  }
}

// 加载更多数据
const loadMoreData = async () => {
  if (loading.value || !hasMore.value) return
  
  loading.value = true
  try {
    // 模拟异步加载
    await new Promise(resolve => setTimeout(resolve, 300))
    currentPage.value++
  } finally {
    loading.value = false
  }
}

// 添加重置状态的方法
const resetState = () => {
  // 重置进度相关状态
  processedCount.value = 0
  startTime.value = null
  processing.value = false
  isPaused.value = false
  currentPage.value = 1
  loading.value = false
  cancelFlag.value = false
  // 重置文件列表
  fileList.value = []
}

// 修改选择输入文件夹的方法
const selectInputFolder = async () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    background: 'rgba(255, 255, 255, 0.9)',
    fullscreen: true
  })
  
  try {
    // 先重置所有状态
    resetState()
    
    const response = await baseAPI('open_folder_dialog', '')
    if (response.code === 200 && response.data.folder_path) {
      inputFolder.value = response.data.folder_path
      const filesResponse = await baseAPI('get_folder_images', inputFolder.value)
      if (filesResponse.code === 200) {
        await loadFileList(filesResponse.data.image_list)
        message.success(`已加载 ${fileList.value.length} 张图片`)
      }
    }
  } catch (error) {
    console.error(error)
    message.error('获取图片列表失败')
  } finally {
    loadingInstance.close()
  }
}

const selectOutputFolder = async () => {
  try {
    const response = await baseAPI('open_folder_dialog', '')
    if (response.code === 200 && response.data.folder_path) {
      outputFolder.value = response.data.folder_path
    }
  } catch (error) {
    message.error('选择文件夹失败')
  }
}

// 添加并发控制函数
const concurrentProcess = async (tasks, limit = 3) => {
  const results = [];
  const executing = new Set();

  for (const task of tasks) {
    if (cancelFlag.value) {
      break;
    }

    const promise = Promise.resolve().then(() => task());
    results.push(promise);

    executing.add(promise);
    const clean = () => executing.delete(promise);
    promise.then(clean).catch(clean);

    if (executing.size >= limit) {
      await Promise.race(executing);
    }
  }
  return Promise.all(results);
};


const startProcessing = async () => {
  if (processing.value) return
  processing.value = true
  startTime.value = Date.now()
  processedCount.value = 0
  cancelFlag.value = false
  
  try {
    // 创建任务数组
    const tasks = fileList.value.map((file, index) => async () => {
      // 检查是否暂停
      while (isPaused.value) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      
      // 检查是否取消
      if (cancelFlag.value) {
        throw new Error('处理已取消')
      }
      
      // 在函数开始就声明 progressInterval 变量
      let progressInterval = null
      
      try {
        file.processing = true
        file.progress = 0
        
        // 模拟进度更新
        progressInterval = setInterval(() => {
          if (file.progress < 90 && !cancelFlag.value) file.progress += 10
        }, 200)
        
        // 调用抠图API
        const params = {
          image_path: file.path,
          mode: processingMode.value,
          ai_model: aiModel.value,
          remove_background: removeBackground.value,
          is_edge_optimization: isEdgeOptimization.value,
          edge_value: edgeValue.value,
          save_format: saveFormat.value,
          output_folder: outputFolder.value,
          input_folder: inputFolder.value,
          background_color: selectedColor.value === 'image' ? 'transparent' : selectedColor.value,
          is_remove_small_regions: isRemoveSmallRegions.value,
          is_remove_small_regions_value: removeSmallRegionsValue.value,
          file_name_mode: fileNameMode.value,
          dpi: dpi.value
        }

        // 如果是图片背景，添加背景图片路径
        if (selectedColor.value === 'image' && backgroundImage.value) {
          params['background_color_image'] = backgroundImage.value
        }

        // 根据不同模式添加参数
        if (processingMode.value === 'stroke') {
          params.stroke_style = strokeStyle.value
          params.stroke_color = strokeColor.value
          params.stroke_width = strokeWidth.value
        } else if (processingMode.value === 'crop') {
          params.crop_ratio = cropRatio.value
          params.width = customRatioWidth.value
          params.height = customRatioHeight.value
          params.subject_retention = subjectRetention.value
        }

        const response = await imageMattingAPI('batch_predict', params)
        
        // 清除定时器
        if (progressInterval) {
          clearInterval(progressInterval)
          progressInterval = null
        }
        
        if (response.code === 200) {
          file.progress = 100
          file.processed = true
          file.resultUrl = response.data.result_url
          file.resultPath = response.data.result_path // 保存结果文件路径
          processedCount.value++
        } else {
          throw new Error(response.msg || '处理失败')
        }
      } catch (error) {
        // 安全地清除定时器
        if (progressInterval) {
          clearInterval(progressInterval)
          progressInterval = null
        }
        file.error = true
        file.progress = 100
        message.error(`处理文件 ${file.name} 失败: ${error.message}`)
      } finally {
        file.processing = false
      }
    })
    
    // 使用并发控制处理任务，默认并发数为3
    await concurrentProcess(tasks, core.value)
    
    if (!cancelFlag.value) {
      message.success('批量处理完成')
    }
  } catch (error) {
    console.error('批量处理出错:', error)
  } finally {
    processing.value = false
    isPaused.value = false
  }
}

const pauseProcessing = () => {
  isPaused.value = !isPaused.value
}

const stopProcessing = () => {
  cancelFlag.value = true
  processing.value = false
  isPaused.value = false
  message.warning('已终止处理')
}

const loadFileList = async (files) => {
  // 不需要在这里重置 currentPage，因为已经在 resetState 中重置了
  fileList.value = files.map(file => ({
    name: file.image_name,
    path: file.image_path,
    processing: false,
    processed: false,
    error: false,
    progress: 0,
    originalUrl: file.image_url,
    resultUrl: '',
    resultPath: ''
  }))
}

// 添加错误计数
const errorCount = computed(() => {
  return fileList.value.filter(file => file.error).length
})

// 添加进度格式化函数
const progressFormat = (percentage) => {
  if (percentage === 100) return '已完成'
  if (isPaused.value) return '已暂停'
  if (processing.value) return `${percentage}%`
  return '等待开始'
}

// 添加进度标签类型计算
const getProgressTagType = computed(() => {
  if (totalProgress.value === 100) return 'success'
  if (isPaused.value) return 'warning'
  if (processing.value) return 'primary'
  return 'info'
})

// 添加打开结果文件的方法
const openResult = async (file) => {
  if (!file.resultPath) return
  
  try {
    const response = await baseAPI('open_and_select_file', file.resultPath)

  } catch (error) {
    message.error(error.message)
  }
}

// 修改计算属性
const getColorPreviewStyle = computed(() => {
  // 如果有背景图片，显示背景图片
  if (backgroundImage.value && !selectedColor.value.includes('gradient') && selectedColor.value !== 'transparent') {
    return {
      backgroundImage: `url(${backgroundUrl.value})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center'
    }
  }
  
  // 检查是否是渐变色
  if (selectedColor.value && selectedColor.value.includes('gradient')) {
    return {
      background: selectedColor.value
    }
  }
  
  return getColorItemStyle(selectedColor.value)
})

// 修改方法
const getColorItemStyle = (color) => {
  // 检查是否是渐变色
  if (color && color.includes('gradient')) {
    return {
      background: color
    }
  }
  
  const style = {
    backgroundColor: color === 'transparent' ? 'transparent' : color,
  }
  
  if (color === 'transparent') {
    style.backgroundImage = `
      linear-gradient(45deg, #ccc 25%, transparent 25%),
      linear-gradient(-45deg, #ccc 25%, transparent 25%),
      linear-gradient(45deg, transparent 75%, #ccc 75%),
      linear-gradient(-45deg, transparent 75%, #ccc 75%)
    `
    style.backgroundSize = '10px 10px'
    style.backgroundPosition = '0 0, 0 5px, 5px -5px, -5px 0px'
  }
  
  return style
}

const setBackground = (color) => {
  selectedColor.value = color
  // 如果选择了非图片背景，清除背景图片
  if (color !== 'image') {
    backgroundImage.value = ''
    backgroundUrl.value = ''
  }
  showBackgroundPicker.value = false
}

// 选择背景图片
const handleSelectBackgroundImage = async () => {
  try {
    const response = await baseAPI('open_img_file', false)
    if (response.code === 200 && response.data.selected_file) {
      backgroundUrl.value = response.data.image_url
      backgroundImage.value = response.data.selected_file
      // 设置为图片背景模式
      selectedColor.value = 'image'
      message.success('背景图片选择成功')
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  }
}

// 预览背景图片
const previewBackgroundImage = () => {
  if (backgroundUrl.value) {
    showImagePreview.value = true
  }
}

// 添加重置到默认值的方法
const resetToDefault = () => {
  ElMessageBox.confirm(
    '确定要重置所有选项到默认值吗？这将清除所有已保存的设置。',
    '重置确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      // 清除所有相关的 localStorage
      const keys = [
        'batch_matting_ai_model',
        'batch_matting_dpi',
        'batch_matting_edge_optimization',
        'batch_matting_edge_value',
        'batch_matting_remove_small_regions',
        'batch_matting_remove_small_regions_value',
        'batch_matting_processing_mode',
        'batch_matting_save_format',
        'batch_matting_file_name_mode',
        'batch_matting_stroke_style',
        'batch_matting_stroke_color',
        'batch_matting_stroke_width',
        'batch_matting_crop_ratio',
        'batch_matting_custom_ratio_width',
        'batch_matting_custom_ratio_height',
        'batch_matting_subject_retention',
        'batch_matting_core'
      ]

      keys.forEach(key => localStorage.removeItem(key))

      // 重置所有值到默认值
      aiModel.value = 'rmbg14'
      dpi.value = '72'
      isEdgeOptimization.value = true
      edgeValue.value = 90
      isRemoveSmallRegions.value = true
      removeSmallRegionsValue.value = 100
      processingMode.value = 'normal'
      saveFormat.value = 'png'
      fileNameMode.value = 'original'
      strokeStyle.value = 'solid'
      strokeColor.value = '#ffffff'
      strokeWidth.value = 10
      cropRatio.value = '1:1'
      customRatioWidth.value = 1024
      customRatioHeight.value = 1024
      subjectRetention.value = 0
      selectedColor.value = 'transparent'
      backgroundImage.value = ''
      backgroundUrl.value = ''

      message.success('已重置所有选项到默认值')
    })
    .catch(() => {
      // 用户取消重置
    })
}

</script>

<style lang="scss" scoped>
.batch-matting {
  height: 100%;
  .layout-container {
    height: 100%;
    display: flex;
    gap: 24px;
    
    // 左侧配置面板
    .config-panel {
      flex: 0 0 400px;
            
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 10px;
          
          .el-form-item__label {
            font-weight: 500;
            margin-left: 10px;
          }
        }
      }
      
      .folder-select {
        .el-input {
          :deep(.el-input-group__append) {
            .el-button {
              padding: 8px 16px;
              .el-icon {
                margin-right: 4px;
              }
            }
          }
        }
      }
      
      .color-picker {
        margin-left: 24px;
      }
      
      .action-buttons {
        display: flex;
        gap: 12px;
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid var(--el-border-color-lighter);
        
        .main-button {
          flex: 1;
          max-width: 200px;
          height: 40px;
          font-size: 16px;
          
          .el-icon {
            margin-right: 8px;
            font-size: 18px;
          }
        }
        
        .el-button {
          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }
    
    // 右侧结果面板
    .result-panel {
      flex: 1;
      min-width: 0; // 防止内容溢出
      display: flex;
      flex-direction: column;
      
      .progress-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        
        :deep(.el-card__body) {
          flex: 1;
          overflow: auto;
          padding: 0;
        }
      }
      
      .progress-header {
        padding: 12px 16px;
        
        .progress-info {
          margin-bottom: 8px;
          
          .progress-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            
            .left {
              display: flex;
              align-items: center;
              gap: 8px;
              
              .title {
                font-size: 14px;
                font-weight: 500;
                color: var(--el-text-color-primary);
              }
            }
          }
          
          .total-progress {
            margin: 0;
            
            :deep(.el-progress-bar__outer) {
              background-color: var(--el-fill-color-light);
            }
            
            :deep(.el-progress-bar__inner) {
              transition: all 0.3s ease;
            }
          }
        }
        
        .progress-stats {
          display: flex;
          gap: 8px;
          margin-top: 8px;
          
          .el-tag {
            display: flex;
            align-items: center;
            gap: 4px;
            
            .el-icon {
              font-size: 14px;
            }
          }
        }
      }
      
      .file-list {
        padding: 16px;
        
        .file-list-content {
          border: 1px solid var(--el-border-color-lighter);
          border-radius: 4px;
          
          .loading-more {
            padding: 16px;
            text-align: center;
            color: var(--el-text-color-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            
            .loading {
              animation: rotating 2s linear infinite;
            }
          }
          
          .file-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px 16px;
            border-bottom: 1px solid var(--el-border-color-lighter);
            
            &:last-child {
              border-bottom: none;
            }
            
            &.processing {
              background-color: var(--el-color-primary-light-9);
            }
            
            &.processed {
              background-color: var(--el-color-success-light-9);
            }
            
            &.error {
              background-color: var(--el-color-danger-light-9);
            }
            
            .item-preview {
              position: relative;
              cursor: zoom-in;
              width: 100px;
              height: 100px;
              overflow: hidden;
              border-radius: 4px;
              flex-shrink: 0;
              transition: transform 0.2s;
              
              &:hover {
                transform: scale(1.05);
              }
              
              .preview-thumbnail {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            
            .item-content {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 8px;
              
              .item-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                
                .file-name {
                  font-weight: 500;
                  color: var(--el-text-color-primary);
                }
                
                .status-icon {
                  .el-icon {
                    margin-left: 8px;
                  }
                }
              }
              
              .el-progress {
                margin-top: 8px;
              }
            }
            
            .preview-badge {
              position: absolute;
              top: 8px;
              right: 8px;
              z-index: 1;
              
              .el-tag {
                border-radius: 4px;
                padding: 0 6px;
                height: 20px;
                line-height: 20px;
                font-size: 12px;
                
                &.clickable {
                  cursor: pointer;
                  transition: all 0.2s;
                  
                  &:hover {
                    transform: scale(1.1);
                    opacity: 0.9;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.edge-optimization-container {
  display: flex;
  align-items: center;
  gap: 16px;
  
  .edge-value-slider {
    width: 180px;
    margin-left: 16px;
  }
}

.stroke-color {
  display: flex;
  align-items: center;
  gap: 12px;

  .hex-input {
    width: 120px;
  }
}

.custom-ratio {
  display: flex;
  align-items: center;
  gap: 12px;

  .ratio-separator {
    color: var(--el-text-color-regular);
    font-size: 16px;
  }
  
  .unit {
    color: var(--el-text-color-secondary);
    margin-left: 4px;
  }
}

.stroke-width {
  width: 120px;
}

.edge-length {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .unit {
    color: var(--el-text-color-secondary);
  }
}

// 添加新的样式
.color-preview {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  margin-right: 8px;
  vertical-align: middle;
  border: 1px solid var(--el-border-color-lighter);
}

.background-picker {
  .preset-colors {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
    margin-bottom: 20px;
    
    .color-item {
      position: relative;
      width: 64px;
      height: 64px;
      border-radius: 8px;
      cursor: pointer;
      border: 2px solid #e4e7ed;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
      
      &:hover {
        transform: scale(1.05);
      }
      
      &.active {
        border-color: var(--el-color-primary);
        
        .el-icon {
          color: #fff;
          filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
        }
      }
    }
  }

  .gradient-colors {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
    margin-bottom: 20px;
    
    .gradient-item {
      position: relative;
      width: 64px;
      height: 64px;
      border-radius: 8px;
      cursor: pointer;
      border: 2px solid #e4e7ed;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
      
      &:hover {
        transform: scale(1.05);
      }
      
      &.active {
        border-color: var(--el-color-primary);
      }

      .gradient-name {
        color: #fff;
        text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
        font-size: 12px;
        position: absolute;
        bottom: 4px;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
      }

      .el-icon {
        color: #fff;
        filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));
      }
    }
  }

  .custom-color {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding-top: 16px;
    margin-top: 8px;
    border-top: 1px solid var(--el-border-color-lighter);

    .label {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
}

.small-regions-container {
  display: flex;
  align-items: center;
  gap: 16px;
  
  .small-regions-value {
    width: 180px;
    margin-left: 16px;
  }
}

/* 添加新的样式 */
.selected-bg-preview {
  margin-left: 12px;
  width: 60px;
  height: 40px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  
  &:hover .preview-overlay {
    opacity: 1;
  }
  
  .bg-preview-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
    
    .el-icon {
      color: white;
      font-size: 20px;
    }
  }
}

.image-background {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;

  .selected-bg-preview-large {
    width: 100%;
    height: 150px;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    
    &:hover .preview-overlay {
      opacity: 1;
    }
    
    .bg-preview-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .preview-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s;
      
      .el-icon {
        color: white;
        font-size: 32px;
      }
    }
  }
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  
  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }
}

.label-with-icon {
  display: flex;
  align-items: center;
  gap: 4px;

  .help-icon {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    cursor: help;
    
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style> 