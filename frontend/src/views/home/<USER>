<template>
  <div class="home-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <h1>欢迎使用灵象工具箱</h1>
      <p class="subtitle">本地化AI工具集合，让创作更简单，更高效</p>
    </div>

    <!-- 功能卡片区域 -->
    <div class="features-grid">
      <!-- 基础信息介绍卡片 -->
      <div class="feature-card info-card">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="feature-icon"><InfoFilled /></el-icon>
            <h3>关于我们</h3>
          </div>
        </div>
        <p class="description">灵象工具箱是一款集成多种AI功能的本地化工具集合，为创作者提供高效便捷的解决方案</p>
        <div class="info-list">
          <div class="info-item">
            <span class="info-label">官方网站：</span>
            <a href="https://lingxiangtools.top/" target="_blank" class="info-link">https://lingxiangtools.top/</a>
          </div>
          <div class="info-item">
            <span class="info-label">文档地址：</span>
            <a href="https://docs.lingxiangtools.top/" target="_blank" class="info-link">https://docs.lingxiangtools.top/</a>
          </div>
          <div class="info-item">
            <span class="info-label">联系邮箱：</span>
            <span class="copyable" @click="copyText('<EMAIL>')"><EMAIL></span>
            <el-icon class="copy-icon" @click="copyText('<EMAIL>')" v-if="!copied.email"><DocumentCopy /></el-icon>
            <el-icon class="copy-icon copied" v-else><Check /></el-icon>
          </div>
          <div class="info-item">
            <span class="info-label">QQ反馈群：</span>
            <span class="copyable" @click="copyText('605545024')">605545024</span>
            <el-icon class="copy-icon" @click="copyText('605545024')" v-if="!copied.qq"><DocumentCopy /></el-icon>
            <el-icon class="copy-icon copied" v-else><Check /></el-icon>
          </div>
        </div>
      </div>

       <!-- 视频抠图卡片 -->
       <div class="feature-card video-matting-card" @click="router.push('/matting_video')">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="feature-icon"><VideoCameraFilled /></el-icon>
            <h3>视频抠图</h3>
          </div>
          <el-button class="use-now-btn" type="primary" size="small">
            立即使用 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <p class="description">智能识别视频主体，支持人像和通用场景，可自定义背景颜色和图片</p>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>人像/通用模式</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>自定义背景</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>边缘优化</span>
          </div>
        </div>
        <div class="feature-tags">
          <el-tag size="small" effect="plain" type="success">智能抠图</el-tag>
          <el-tag size="small" effect="plain" type="warning">高清输出</el-tag>
          <el-tag size="small" effect="plain" type="info">自定义背景</el-tag>
        </div>
      </div>
      

      <!-- 视频去水印卡片 -->
      <div class="feature-card video-card" @click="router.push('/video')">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="feature-icon"><VideoCameraFilled /></el-icon>
            <h3>视频去水印</h3>
          </div>
          <el-button class="use-now-btn" type="primary" size="small">
            立即使用 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <p class="description">专业的视频水印去除工具，支持去除固定位置水印、字幕等内容</p>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>操作简单</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>无损画质导出</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>自媒体必备</span>
          </div>
        </div>
        <div class="feature-tags">
          <el-tag size="small" effect="plain" type="success">自媒体必备</el-tag>
          <el-tag size="small" effect="plain" type="warning">高清输出</el-tag>
          <el-tag size="small" effect="plain" type="danger">快速处理</el-tag>
        </div>
      </div>

      <!-- AI抠图卡片 -->
      <div class="feature-card matting-card" @click="router.push('/matting')">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="feature-icon"><PictureFilled /></el-icon>
            <h3>AI 智能抠图</h3>
          </div>
          <el-button class="use-now-btn" type="primary" size="small">
            立即使用 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <p class="description">智能识别并抠出图片主体，支持单张和批量处理，让抠图更简单高效,支持透明和多彩背景、自定义图片背景导出</p>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>一键智能识别主体</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>精细边缘处理</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>支持透明和多彩背景导出</span>
          </div>
        </div>
        <div class="feature-tags">
          <el-tag size="small" effect="plain" type="success">批量处理</el-tag>
          <el-tag size="small" effect="plain" type="info">自定义背景颜色、背景图片</el-tag>
          <el-tag size="small" effect="plain" type="success">支持描边</el-tag>
          <el-tag size="small" effect="plain" type="danger">支持裁剪</el-tag>
        </div>
      </div>

      <!-- AI智能擦除卡片 -->
      <div class="feature-card inpainting-card" @click="router.push('/inpainting')">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="feature-icon"><EditPen /></el-icon>
            <h3>AI 智能擦除</h3>
          </div>
          <el-button class="use-now-btn" type="primary" size="small">
            立即使用 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <p class="description">智能去除图片中不需要的内容，完美修复背景，让图片处理更简单</p>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>智能背景修复</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>多场景适用</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>一键快速处理</span>
          </div>
        </div>
        <div class="feature-tags">
          <el-tag size="small" effect="plain" type="danger">AI 修复</el-tag>
          <el-tag size="small" effect="plain" type="info">便捷操作</el-tag>
          <el-tag size="small" effect="plain" type="success">高效率</el-tag>
        </div>
      </div>

      <!-- 智能OCR卡片 -->
      <div class="feature-card ocr-card" @click="router.push('/ocr')">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="feature-icon"><Document /></el-icon>
            <h3>智能OCR识别</h3>
          </div>
          <el-button class="use-now-btn" type="primary" size="small">
            立即使用 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <p class="description">基于先进的AI技术，智能识别并提取图片中的文字，支持单张和批量处理</p>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>高精度识别</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>批量处理</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>多语言支持</span>
          </div>
        </div>
        <div class="feature-tags">
          <el-tag size="small" effect="plain" type="info">AI 识别</el-tag>
          <el-tag size="small" effect="plain" type="success">多语言</el-tag>
          <el-tag size="small" effect="plain" type="warning">高效率</el-tag>
        </div>
      </div>

       <!-- 图片格式转换卡片 -->
       <div class="feature-card convert-img-card" @click="router.push('/convert_img/single')">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="feature-icon"><PictureFilled /></el-icon>
            <h3>图片格式转换</h3>
          </div>
          <el-button class="use-now-btn" type="primary" size="small">
            立即使用 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <p class="description">支持任意格式相互转换，包括JPG、PNG、WEBP、BMP、GIF、PDF、TIFF、ICO、ICNS等格式</p>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>多种格式支持</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>单张/批量处理</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>高质量转换</span>
          </div>
        </div>
        <div class="feature-tags">
          <el-tag size="small" effect="plain" type="success">批量处理</el-tag>
          <el-tag size="small" effect="plain" type="warning">多格式支持</el-tag>
          <el-tag size="small" effect="plain" type="info">高效转换</el-tag>
        </div>
      </div>

      <!-- 截图美化卡片 -->
      <div class="feature-card beautify-card" @click="router.push('/screenshot')">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="feature-icon"><PictureFilled /></el-icon>
            <h3>截图美化</h3>
          </div>
          <el-button class="use-now-btn" type="primary" size="small">
            立即使用 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <p class="description">为您的截图添加精美的边框和背景，让截图更具专业感</p>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>多种边框样式</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>自定义背景</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>一键美化</span>
          </div>
        </div>
        <div class="feature-tags">
          <el-tag size="small" effect="plain" type="success">专业美化</el-tag>
          <el-tag size="small" effect="plain" type="info">自定义</el-tag>
          <el-tag size="small" effect="plain" type="warning">快速导出</el-tag>
        </div>
      </div>

      <!-- 图片高清放大卡片 -->
      <div class="feature-card real-image-card" @click="router.push('/real_image')">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="feature-icon"><PictureFilled /></el-icon>
            <h3>图片高清放大</h3>
          </div>
          <el-button class="use-now-btn" type="primary" size="small">
            立即使用 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <p class="description">支持单张和批量处理，支持无损放大，支持1-4倍无损放大</p>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>高清修复</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>无损放大</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>批量处理</span>
          </div>
        </div>
        <div class="feature-tags">
          <el-tag size="small" effect="plain" type="success">高清放大</el-tag>
          <el-tag size="small" effect="plain" type="info">无损放大</el-tag>
          <el-tag size="small" effect="plain" type="warning">批量处理</el-tag>
        </div>
      </div>

      <!-- 图片压缩卡片 -->
      <div class="feature-card compress-card" @click="router.push('/compress')">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="feature-icon"><PictureFilled /></el-icon>
            <h3>图片压缩</h3>
          </div>
          <el-button class="use-now-btn" type="primary" size="small">
            立即使用 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <p class="description">智能压缩图片文件大小，保持画质的同时大幅减少存储空间，支持JPG、PNG、WEBP等格式</p>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>智能压缩算法</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>保持画质</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>批量处理</span>
          </div>
        </div>
        <div class="feature-tags">
          <el-tag size="small" effect="plain" type="success">智能压缩</el-tag>
          <el-tag size="small" effect="plain" type="warning">保持画质</el-tag>
          <el-tag size="small" effect="plain" type="info">批量处理</el-tag>
        </div>
      </div>

      <!-- 智能镜头分割卡片 -->
      <div class="feature-card video-split-card" @click="router.push('/smart_video_split')">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="feature-icon"><VideoCameraFilled /></el-icon>
            <h3>智能镜头分割</h3>
          </div>
          <el-button class="use-now-btn" type="primary" size="small">
            立即使用 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <p class="description">基于AI技术的智能视频镜头分割，自动识别场景变化，分割效果媲美剪映Pro版本</p>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>AI智能识别</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>精准分割</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>批量处理</span>
          </div>
        </div>
        <div class="feature-tags">
          <el-tag size="small" effect="plain" type="success">AI识别</el-tag>
          <el-tag size="small" effect="plain" type="danger">精准分割</el-tag>
          <el-tag size="small" effect="plain" type="info">专业级效果</el-tag>
        </div>
      </div>

      <!-- 免费拼图卡片 -->
      <div class="feature-card pintu-card" @click="router.push('/pintu')">
        <div class="card-header">
          <div class="header-left">
            <el-icon class="feature-icon"><Grid /></el-icon>
            <h3>免费拼图</h3>
          </div>
          <el-button class="use-now-btn" type="primary" size="small">
            立即使用 <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        <p class="description">免费在线拼图工具，支持多种拼图模板和布局，让您的照片更有创意</p>
        <div class="feature-list">
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>多种模板</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>免费使用</span>
          </div>
          <div class="feature-item">
            <el-icon><Check /></el-icon>
            <span>在线编辑</span>
          </div>
        </div>
        <div class="feature-tags">
          <el-tag size="small" effect="plain" type="success">免费使用</el-tag>
          <el-tag size="small" effect="plain" type="warning">多种模板</el-tag>
          <el-tag size="small" effect="plain" type="info">在线编辑</el-tag>
        </div>
      </div>

      
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import message from '@/utils/message'
import { 
  VideoCameraFilled, 
  PictureFilled, 
  EditPen, 
  ArrowRight, 
  Check, 
  Document, 
  InfoFilled,
  DocumentCopy,
  Grid
} from '@element-plus/icons-vue'

const router = useRouter()
const copied = ref({
  email: false,
  qq: false
})

const copyText = (text) => {
  navigator.clipboard.writeText(text).then(() => {
    if (text.includes('@')) {
      copied.value.email = true
      setTimeout(() => copied.value.email = false, 2000)
    } else {
      copied.value.qq = true
      setTimeout(() => copied.value.qq = false, 2000)
    }
    message.success('复制成功')
  }).catch(() => {
    message.error('复制失败')
  })
}
</script>

<style scoped lang="scss">
.home-container {
  max-width: 1200px;
  margin: 0 auto;

  .welcome-section {
    text-align: center;
    margin-bottom: 24px;

    h1 {
      font-size: 36px;
      background: linear-gradient(45deg, #4fcaf2, #03eb32);
      -webkit-background-clip: text;
      color: transparent;
      margin-bottom: 16px;
      font-weight: 600;
    }

    .subtitle {
      font-size: 18px;
      color: var(--el-text-color-secondary);
    }
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    
    .feature-card {
      background: var(--el-bg-color);
      border-radius: 16px;
      padding: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
      }
      
      &.video-card::before {
        background: linear-gradient(90deg, #ff9a9e, #fad0c4);
      }
      
      &.matting-card::before {
        background: linear-gradient(90deg, #a1c4fd, #c2e9fb);
      }
      
      &.inpainting-card::before {
        background: linear-gradient(90deg, #d4fc79, #96e6a1);
      }
      
      &.ocr-card::before {
        background: linear-gradient(90deg, #84fab0, #8fd3f4);
      }
      
      &.beautify-card::before {
        background: linear-gradient(90deg, #fa709a, #fee140);
      }
      
      &.info-card::before {
        background: linear-gradient(90deg, #4facfe, #00f2fe);
      }
      
      &.video-matting-card::before {
        background: linear-gradient(90deg, #4facfe, #00f2fe);
      }
      
      &.convert-img-card::before {
        background: linear-gradient(90deg, #fbc2eb, #a6c1ee);
      }

      &.real-image-card::before {
        background: linear-gradient(90deg, #ea8007, #d3eab4);
      }

      &.compress-card::before {
        background: linear-gradient(90deg, #4facfe, #00f2fe);
      }

      &.video-split-card::before {
        background: linear-gradient(90deg, #d4fc79, #96e6a1);
      }

      &.pintu-card::before {
        background: linear-gradient(90deg, #fa709a, #fee140);
      }
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .feature-icon {
          font-size: 32px;
          padding: 4px;
          margin-right: 8px;
          border-radius: 8px;
          background: var(--el-color-primary-light-9);
          color: var(--el-color-primary);
        }

        h3 {
          font-size: 18px;
          margin: 0;
          color: var(--el-text-color-primary);
        }

        .use-now-btn {
          padding: 6px 12px;
          border-radius: 4px;
          
          .el-icon {
            margin-left: 4px;
          }
        }
      }

      .description {
        margin-bottom: 16px;
        font-size: 14px;
      }

      .feature-list {
        margin-bottom: 16px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        
        .feature-item {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-top: 8px;
          
          .el-icon {
            font-size: 16px;
            color: #fff;
            background: var(--el-color-success);
            padding: 2px;
            border-radius: 50%;
            margin-right: 8px;
          }
          
          span {
            font-size: 14px;
            color: var(--el-text-color-primary);
            line-height: 1;
          }
        }
      }

      .feature-tags {
        margin-bottom: 0;
        
        .el-tag {
          font-size: 12px;
          padding: 0 8px;
          margin-right: 8px;
          margin-bottom: 8px;
        }
      }

      .info-list {
        margin-bottom: 16px;
        display: flex;
        flex-direction: column;
        gap: 12px;
        
        .info-item {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          margin-top: 8px;
          
          .info-label {
            font-weight: 500;
            color: var(--el-text-color-primary);
            margin-right: 8px;
          }
          
          .info-link {
            color: var(--el-color-primary);
            text-decoration: none;
            
            &:hover {
              text-decoration: underline;
            }
          }
          
          .copyable {
            color: var(--el-color-primary);
            cursor: pointer;
            
            &:hover {
              text-decoration: underline;
            }
          }
          
          .copy-icon {
            margin-left: 8px;
            font-size: 16px;
            cursor: pointer;
            color: var(--el-text-color-secondary);
            
            &.copied {
              color: var(--el-color-success);
            }
          }
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 768px) {
  .home-container {
    padding: 20px;

    .welcome-section {
      h1 {
        font-size: 28px;
      }

      .subtitle {
        font-size: 16px;
      }
    }

    .features-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
