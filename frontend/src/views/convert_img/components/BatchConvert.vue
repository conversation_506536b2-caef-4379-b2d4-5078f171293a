<template>
  <div class="batch-convert">
    <div class="layout-container">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <el-form label-position="left">
          <el-form-item label="输入目录">
            <div class="folder-select">
              <el-tooltip :content="inputFolder ? inputFolder : '选择包含图片的文件夹'" placement="top" effect="light">

              <el-input
                v-model="inputFolder"
                placeholder="选择包含图片的文件夹"
              >
                <template #append>
                  <el-button @click="selectInputFolder">
                    <el-icon><Folder /></el-icon>
                    选择
                  </el-button>
                </template>
              </el-input>
              </el-tooltip>
            </div>
          </el-form-item>

          <el-form-item label="输出目录">
            <div class="folder-select">
              <el-tooltip :content="outputFolder ? outputFolder : '选择结果保存位置, 默认在输入目录下创建result文件夹'" placement="bottom" effect="light">
              <el-input
                v-model="outputFolder"
                placeholder="选择结果保存位置, 默认在输入目录下创建result文件夹"
              >
                <template #append>
                  <el-button @click="selectOutputFolder">
                    <el-icon><Folder /></el-icon>
                    选择
                  </el-button>
                </template>
              </el-input>
              </el-tooltip>
            </div>
          </el-form-item>

          <el-form-item label="输出格式">
            <el-select v-model="outputFormat" placeholder="选择输出格式">
                <el-option v-for="item in outputFormatList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>

          <el-form-item label="图片质量" v-if="outputFormat === 'JPEG' || outputFormat === 'WEBP'">
            <el-slider 
              v-model="quality" 
              :min="1" 
              :max="100" 
              :step="1"
              :format-tooltip="(val) => `${val}%`"
            />
          </el-form-item>

          <div class="action-buttons">
            <StartProcessButton 
              text="开始转换" 
              :loading="processing" 
              :disabled="!canStart || processing"
              @startProcess="startProcessing"
            />
            <el-button
              v-if="processing"
              @click="pauseProcessing"
              :type="isPaused ? 'success' : 'warning'"
            >
              <el-icon><VideoPause v-if="!isPaused"/><VideoPlay v-else/></el-icon>
              {{ isPaused ? '继续处理' : '暂停处理' }}
            </el-button>
            <el-button
              v-if="processing"
              type="danger"
              @click="stopProcessing"
            >
              <el-icon><CircleClose /></el-icon>
              终止处理
            </el-button>
          </div>
        </el-form>
      </div>

      <!-- 右侧进度和结果面板 -->
      <div class="result-panel" v-if="fileList.length > 0">
        <el-card class="progress-card">
          <template #header>
            <div class="progress-header">
              <div class="progress-info">
                <div class="progress-title">
                  <div class="left">
                    <span class="title">处理进度</span>
                    <el-tag size="small" :type="getProgressTagType">
                      {{ processedCount }}/{{ fileList.length }}
                    </el-tag>
                  </div>
                  <div class="right">
                    <el-tag 
                      v-if="processing && !isPaused" 
                      size="small" 
                      type="warning"
                    >
                      <el-icon><Timer /></el-icon>
                      预计剩余: {{ remainingTime }}
                    </el-tag>
                  </div>
                </div>
                <el-progress
                  :percentage="totalProgress"
                  :status="processStatus"
                  :stroke-width="8"
                  :format="progressFormat"
                  class="total-progress"
                />
              </div>
              <div class="progress-stats" v-if="errorCount > 0">
                <el-tag size="small" type="danger">
                  <el-icon><CircleCloseFilled /></el-icon>
                  失败: {{ errorCount }}
                </el-tag>
              </div>
            </div>
          </template>

          <div class="file-list">
            <el-scrollbar 
              height="400px" 
              class="file-list-content"
              @scroll="handleScroll"
              ref="scrollbarRef"
            >
              <div
                v-for="(file, index) in displayList"
                :key="file.path"
                class="file-item"
                :class="{ 
                  'processing': file.processing,
                  'processed': file.processed,
                  'error': file.error
                }"
              >
                <div class="item-preview">
                  <el-image
                    :src="file.resultUrl || file.originalUrl"
                    fit="cover"
                    class="preview-thumbnail"
                    lazy
                    :preview-teleported="true"
                    :preview-src-list="[file.resultUrl || file.originalUrl]"
                    :initial-index="0"
                    :hide-on-click-modal="false"
                  >
                  </el-image>
                  <div class="preview-badge" v-if="file.resultUrl">
                    <el-tag 
                      size="small" 
                      type="success"
                      @click.stop="openResult(file)"
                      class="clickable"
                    >
                      处理后
                    </el-tag>
                  </div>
                  <div class="preview-badge" v-else>
                    <el-tag size="small" type="info">原图</el-tag>
                  </div>
                </div>
                <div class="item-content">
                  <div class="item-info">
                    <span class="file-name" :title="file.name">{{ file.name }}</span>
                    <div class="status-icon">
                      <el-icon v-if="file.processed" @click="openResult(file)" class="success"><CircleCheckFilled /></el-icon>
                      <el-icon v-else-if="file.error" class="error"><CircleCloseFilled /></el-icon>
                      <el-icon v-else-if="file.processing" class="processing"><Loading /></el-icon>
                      <el-icon v-else class="waiting"><Clock /></el-icon>
                    </div>
                  </div>
                  <el-progress
                    v-if="file.processing || file.processed"
                    :percentage="file.progress"
                    :status="file.error ? 'exception' : file.processed ? 'success' : ''"
                  />
                </div>
              </div>
              <div v-if="loading" class="loading-more">
                <el-icon class="loading"><Loading /></el-icon>
                <span>加载中...</span>
              </div>
            </el-scrollbar>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElLoading } from 'element-plus'
import {
  Folder,
  VideoPlay,
  VideoPause,
  CircleClose,
  CircleCheckFilled,
  CircleCloseFilled,
  Clock,
  Loading,
  Timer
} from '@element-plus/icons-vue'
import message from '@/utils/message'
import baseAPI from '@/apis/base'
import { convertImageAPI } from '@/apis/convert_img'
import StartProcessButton from '@/components/StartProcessButton.vue'

// 状态变量
const inputFolder = ref('')
const outputFolder = ref('')
const fileList = ref([])
const processing = ref(false)
const isPaused = ref(false)
const processedCount = ref(0)
const startTime = ref(null)
const pageSize = 20 // 每页显示数量
const currentPage = ref(1)
const outputFormat = ref('PNG')
const quality = ref(90)

const outputFormatList = ["PNG", "JPEG", "GIF", "BMP", "WEBP", "ICO", "ICNS", "TIFF", "PDF"]


// 添加滚动相关的状态
const scrollbarRef = ref(null)
const loading = ref(false)
const loadingThreshold = 100 // 距离底部多少像素时触发加载

// 计算属性
const canStart = computed(() => {
  return inputFolder.value && fileList.value.length > 0
})

const totalProgress = computed(() => {
  if (fileList.value.length === 0) return 0
  return Math.round((processedCount.value / fileList.value.length) * 100)
})

const processStatus = computed(() => {
  if (isPaused.value) return 'warning'
  if (processing.value) return ''
  return totalProgress.value === 100 ? 'success' : ''
})

const remainingTime = computed(() => {
  if (!startTime.value || processedCount.value === 0) return '计算中...'
  
  const elapsed = (Date.now() - startTime.value) / 1000
  const avgTimePerFile = elapsed / processedCount.value
  const remaining = (fileList.value.length - processedCount.value) * avgTimePerFile
  
  if (remaining < 60) return `${Math.round(remaining)}秒`
  if (remaining < 3600) return `${Math.round(remaining / 60)}分钟`
  return `${Math.round(remaining / 3600)}小时${Math.round((remaining % 3600) / 60)}分钟`
})

// 计算当前显示的列表
const displayList = computed(() => {
  const start = 0
  const end = currentPage.value * pageSize
  return fileList.value.slice(start, end)
})

// 是否还有更多数据
const hasMore = computed(() => {
  return displayList.value.length < fileList.value.length
})

// 修改处理滚动事件的方法
const handleScroll = async (e) => {
  if (loading.value || !hasMore.value) return

  // 获取 scrollbar 实例
  const scrollbar = scrollbarRef.value
  if (!scrollbar) return

  // 获取滚动容器
  const wrap = scrollbar.wrapRef
  if (!wrap) return

  const {
    scrollTop,
    scrollHeight,
    clientHeight
  } = wrap

  // 当滚动到距离底部loadingThreshold像素时加载更多
  if (scrollHeight - (scrollTop + clientHeight) <= loadingThreshold) {
    await loadMoreData()
  }
}

// 加载更多数据
const loadMoreData = async () => {
  if (loading.value || !hasMore.value) return
  
  loading.value = true
  try {
    // 模拟异步加载
    await new Promise(resolve => setTimeout(resolve, 300))
    currentPage.value++
  } finally {
    loading.value = false
  }
}

// 添加重置状态的方法
const resetState = () => {
  // 重置进度相关状态
  processedCount.value = 0
  startTime.value = null
  processing.value = false
  isPaused.value = false
  currentPage.value = 1
  loading.value = false
  
  // 重置文件列表
  fileList.value = []
}

// 修改选择输入文件夹的方法
const selectInputFolder = async () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    background: 'rgba(255, 255, 255, 0.9)',
    fullscreen: true
  })
  
  try {
    // 先重置所有状态
    resetState()
    
    const response = await baseAPI('open_folder_dialog', '')
    if (response.code === 200 && response.data.folder_path) {
      inputFolder.value = response.data.folder_path
      const filesResponse = await convertImageAPI('get_folder_convert_images', response.data.folder_path)
      if (filesResponse.code === 200) {
        await loadFileList(filesResponse.data.convert_images)
        message.success(`已加载 ${fileList.value.length} 张图片`)
      }
    }
  } catch (error) {
    console.error(error)
    message.error('获取图片列表失败')
  } finally {
    loadingInstance.close()
  }
}

const selectOutputFolder = async () => {
  try {
    const response = await baseAPI('open_folder_dialog', '')
    if (response.code === 200 && response.data.folder_path) {
      outputFolder.value = response.data.folder_path
    }
  } catch (error) {
    message.error('选择文件夹失败')
  }
}

const startProcessing = async () => {
  if (processing.value) return
  processing.value = true
  startTime.value = Date.now()
  processedCount.value = 0
  
  for (const file of fileList.value) {
    if (isPaused.value) {
      await new Promise(resolve => {
        const checkPause = () => {
          if (!isPaused.value) resolve()
          else setTimeout(checkPause, 100)
        }
        checkPause()
      })
    }
    
    if (!processing.value) break // 如果已停止则退出循环
    
    try {
      file.processing = true
      file.progress = 0
      
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        if (file.progress < 90) file.progress += 10
      }, 200)
      
      // 调用转换API
      const params = {
        image_path: file.path,
        folder_path: inputFolder.value,
        output_format: outputFormat.value,
        quality: outputFormat.value === 'JPEG' || outputFormat.value === 'WEBP' ? quality.value : null,
        save_folder: outputFolder.value
      }

      const response = await convertImageAPI('convert_image_from_folder', params)
      
      clearInterval(progressInterval)
      
      if (response.code === 200) {
        file.progress = 100
        file.processed = true
        file.resultPath = response.data.convert_result
        
        // 获取结果图片URL
        const resultResponse = await baseAPI('get_image_url', file.resultPath)
        if (resultResponse.code === 200) {
          file.resultUrl = resultResponse.data.image_url
        }
        
        processedCount.value++
      } else {
        throw new Error(response.msg || '处理失败')
      }
    } catch (error) {
      file.error = true
      file.progress = 100
      message.error(`处理文件 ${file.name} 失败: ${error.message}`)
    } finally {
      file.processing = false
    }
  }
  
  processing.value = false
  isPaused.value = false
  message.success('批量处理完成')
}

const pauseProcessing = () => {
  isPaused.value = !isPaused.value
}

const stopProcessing = () => {
  processing.value = false
  isPaused.value = false
  message.warning('已终止处理')
}

const loadFileList = async (files) => {
  // 不需要在这里重置 currentPage，因为已经在 resetState 中重置了
  fileList.value = files.map(file => ({
    name: file.image_name,
    path: file.image_path,
    processing: false,
    processed: false,
    error: false,
    progress: 0,
    originalUrl: file.image_url || '',
    resultUrl: '',
    resultPath: ''
  }))
}

// 添加错误计数
const errorCount = computed(() => {
  return fileList.value.filter(file => file.error).length
})

// 添加进度格式化函数
const progressFormat = (percentage) => {
  if (percentage === 100) return '已完成'
  if (isPaused.value) return '已暂停'
  if (processing.value) return `${percentage}%`
  return '等待开始'
}

// 添加进度标签类型计算
const getProgressTagType = computed(() => {
  if (totalProgress.value === 100) return 'success'
  if (isPaused.value) return 'warning'
  if (processing.value) return 'primary'
  return 'info'
})

// 添加打开结果文件的方法
const openResult = async (file) => {
  if (!file.resultPath) return
  
  try {
    const response = await baseAPI('open_and_select_file', file.resultPath)
  } catch (error) {
    message.error(error.message)
  }
}

onMounted(() => {
  resetState()
})
</script>

<style lang="scss" scoped>
.batch-convert {
  height: 100%;
  .layout-container {
    height: 100%;
    display: flex;
    gap: 24px;
    
    // 左侧配置面板
    .config-panel {
      flex: 0 0 400px;
            
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 16px;
          
          .el-form-item__label {
            font-weight: 500;
            margin-left: 10px;
          }
        }
      }
      
      .folder-select {
        .el-input {
          :deep(.el-input-group__append) {
            .el-button {
              padding: 8px 16px;
              .el-icon {
                margin-right: 4px;
              }
            }
          }
        }
      }
      
      .action-buttons {
        display: flex;
        gap: 12px;
        margin-top: 32px;
        padding-top: 24px;
        border-top: 1px solid var(--el-border-color-lighter);
        
        .main-button {
          flex: 1;
          max-width: 200px;
          height: 40px;
          font-size: 16px;
          
          .el-icon {
            margin-right: 8px;
            font-size: 18px;
          }
        }
        
        .el-button {
          .el-icon {
            margin-right: 4px;
          }
        }
      }
    }
    
    // 右侧结果面板
    .result-panel {
      flex: 1;
      min-width: 0; // 防止内容溢出
      display: flex;
      flex-direction: column;
      
      .progress-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        
        :deep(.el-card__body) {
          flex: 1;
          overflow: auto;
          padding: 0;
        }
      }
      
      .progress-header {
        padding: 12px 16px;
        
        .progress-info {
          margin-bottom: 8px;
          
          .progress-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            
            .left {
              display: flex;
              align-items: center;
              gap: 8px;
              
              .title {
                font-size: 14px;
                font-weight: 500;
                color: var(--el-text-color-primary);
              }
            }
          }
          
          .total-progress {
            margin: 0;
            
            :deep(.el-progress-bar__outer) {
              background-color: var(--el-fill-color-light);
            }
            
            :deep(.el-progress-bar__inner) {
              transition: all 0.3s ease;
            }
          }
        }
        
        .progress-stats {
          display: flex;
          gap: 8px;
          margin-top: 8px;
          
          .el-tag {
            display: flex;
            align-items: center;
            gap: 4px;
            
            .el-icon {
              font-size: 14px;
            }
          }
        }
      }
      
      .file-list {
        padding: 16px;
        
        .file-list-content {
          border: 1px solid var(--el-border-color-lighter);
          border-radius: 4px;
          
          .loading-more {
            padding: 16px;
            text-align: center;
            color: var(--el-text-color-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            
            .loading {
              animation: rotating 2s linear infinite;
            }
          }
          
          .file-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 12px 16px;
            border-bottom: 1px solid var(--el-border-color-lighter);
            
            &:last-child {
              border-bottom: none;
            }
            
            &.processing {
              background-color: var(--el-color-primary-light-9);
            }
            
            &.processed {
              background-color: var(--el-color-success-light-9);
            }
            
            &.error {
              background-color: var(--el-color-danger-light-9);
            }
            
            .item-preview {
              position: relative;
              cursor: zoom-in;
              width: 100px;
              height: 100px;
              overflow: hidden;
              border-radius: 4px;
              flex-shrink: 0;
              transition: transform 0.2s;
              
              &:hover {
                transform: scale(1.05);
              }
              
              .preview-thumbnail {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }
            }
            
            .item-content {
              flex: 1;
              display: flex;
              flex-direction: column;
              gap: 8px;
              
              .item-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                
                .file-name {
                  font-weight: 500;
                  color: var(--el-text-color-primary);
                }
                
                .status-icon {
                  .el-icon {
                    margin-left: 8px;
                  }
                }
              }
              
              .el-progress {
                margin-top: 8px;
              }
            }
            
            .preview-badge {
              position: absolute;
              top: 8px;
              right: 8px;
              z-index: 1;
              
              .el-tag {
                border-radius: 4px;
                padding: 0 6px;
                height: 20px;
                line-height: 20px;
                font-size: 12px;
                
                &.clickable {
                  cursor: pointer;
                  transition: all 0.2s;
                  
                  &:hover {
                    transform: scale(1.1);
                    opacity: 0.9;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 