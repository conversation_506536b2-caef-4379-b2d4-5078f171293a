<template>
  <div class="single-convert" v-loading="loading" element-loading-text="处理中..." element-loading-lock>
    <div class="layout-container">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <el-form label-position="left">
          <el-form-item label="选择图片">
            <div class="upload-content">
              <UploadButton text="选择文件" size="default" @upload="handleClickUpload" />
              <StartProcessButton 
                text="开始转换" 
                :loading="processing" 
                :disabled="!imageUrl || processing"
                @startProcess="startProcess"
              />
              <el-button 
                v-if="imageUrl" 
                @click="resetImage"
              >
                <el-icon><Back /></el-icon>
                重新选择
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="输出格式">
            <el-select v-model="outputFormat" placeholder="选择输出格式">
                <el-option v-for="item in outputFormatList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>

          <el-form-item label="图片质量" v-if="outputFormat === 'JPEG' || outputFormat === 'WEBP'">
            <el-slider 
              v-model="quality" 
              :min="1" 
              :max="100" 
              :step="1"
              :format-tooltip="(val) => `${val}%`"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 右侧预览面板 -->
      <div class="preview-panel" v-if="imageUrl">
        <el-card class="preview-card">
          <template #header>
            <div class="preview-header">
              <span>{{ resultUrl ? '转换结果' : '图片预览' }}</span>
              <!-- 结果操作按钮 -->
              <div class="operation-buttons" v-if="resultUrl">
                <el-button-group>
                  <el-tooltip content="复制到剪贴板">
                    <el-button @click="copyToClipboard">
                      <el-icon><Document /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="保存图片">
                    <el-button @click="saveImage">
                      <el-icon><Download /></el-icon>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
              </div>
            </div>
          </template>

          <div class="preview-content">
            <div v-if="!resultUrl" class="single-preview">
              <div class="preview-image">
                <el-image 
                  :src="imageUrl" 
                  fit="contain"
                  :preview-src-list="[imageUrl]"
                />
              </div>
              <div class="image-info" v-if="imageInfo">
                <div class="info-item">
                  <span class="label">格式:</span>
                  <span class="value">{{ imageInfo.format }}</span>
                </div>
                <div class="info-item">
                  <span class="label">尺寸:</span>
                  <span class="value">{{ imageInfo.width }} × {{ imageInfo.height }}</span>
                </div>
                <div class="info-item">
                  <span class="label">大小:</span>
                  <span class="value">{{ imageInfo.size }}</span>
                </div>
              </div>
            </div>
            
            <div v-else class="result-preview">
              <div class="preview-image">
                <el-image 
                  :src="resultUrl" 
                  fit="contain"
                  :preview-src-list="[resultUrl]"
                />
              </div>
              <div class="image-info" v-if="resultInfo">
                <div class="info-item">
                  <span class="label">格式:</span>
                  <span class="value">{{ resultInfo.format }}</span>
                </div>
                <div class="info-item">
                  <span class="label">尺寸:</span>
                  <span class="value">{{ resultInfo.width }} × {{ resultInfo.height }}</span>
                </div>
                <div class="info-item">
                  <span class="label">大小:</span>
                  <span class="value">{{ resultInfo.size }}</span>
                </div>
                <div class="info-item">
                  <span class="label">保存路径:</span>
                  <span class="value path">{{ resultInfo.path }}</span>
                  <el-button size="small" @click="openResultFolder" class="open-btn">
                    <el-icon><FolderOpened /></el-icon>
                    打开
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { 
  Back, 
  Document, 
  Download,
  FolderOpened
} from '@element-plus/icons-vue'
import message from '@/utils/message'
import baseAPI from '@/apis/base'
import { convertImageAPI } from '@/apis/convert_img'
import UploadButton from '@/components/UploadButton.vue'
import StartProcessButton from '@/components/StartProcessButton.vue'

// 状态变量
const imageUrl = ref('')
const imagePath = ref('')
const resultUrl = ref('')
const resultPath = ref('')
const processing = ref(false)
const loading = ref(false)
const outputFormat = ref('PNG')
const quality = ref(90)
const imageInfo = ref(null)
const resultInfo = ref(null)

const outputFormatList = ["PNG", "JPEG", "GIF", "BMP", "WEBP", "ICO", "ICNS", "TIFF", "PDF"]

// 文件上传相关方法
const handleClickUpload = async () => {
  loading.value = true
  try {
    const response = await baseAPI('open_convert_img_file', false)
    if (response.code === 200 && response.data.selected_file) {
      resetImage()
      imageUrl.value = response.data.image_url
      imagePath.value = response.data.selected_file  
      message.success('上传成功')
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}

// 处理相关方法
const startProcess = async () => {
  if (!imageUrl.value) {
    message.warning('请先选择图片')
    return
  }
  
  processing.value = true
  try {
    const params = {
      input_path: imagePath.value || imageUrl.value,
      output_format: outputFormat.value,
      quality: outputFormat.value === 'JPEG' || outputFormat.value === 'WEBP' ? quality.value : null
    }

    const response = await convertImageAPI('single_convert_image', params)
    if (response.code === 200) {
      message.success('转换成功')
        //   打开转换后的文件
      await baseAPI('open_and_select_file', response.data.output_path)
    } else {
      throw new Error(response.msg || '转换失败')
    }
  } catch (error) {
    message.error(`转换失败: ${error.message}`)
    console.error('Error converting image:', error)
  } finally {
    processing.value = false
  }
}

const resetImage = () => {
  imageUrl.value = ''
  imagePath.value = ''
  resultUrl.value = ''
  resultPath.value = ''
  imageInfo.value = null
  resultInfo.value = null
}

// 复制到剪贴板
const copyToClipboard = async () => {
  try {
    // 从base64创建blob
    const base64Response = await fetch(resultUrl.value);
    const blob = await base64Response.blob();
    
    const item = new ClipboardItem({ [blob.type]: blob });
    await navigator.clipboard.write([item]);
    message.success('已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    message.error(`复制失败: ${error.message || '未知错误'}`);
  }
}

// 保存图片
const saveImage = async () => {
  if (!resultPath.value) {
    message.warning('没有可保存的图片')
    return
  }
  
  try {
    const response = await baseAPI('save_image_as', {
      source_path: resultPath.value,
      format: outputFormat.value
    })
    
    if (response.code === 200) {
      message.success('保存成功')
    } else {
      throw new Error(response.msg || '保存失败')
    }
  } catch (error) {
    message.error(`保存失败: ${error.message}`)
  }
}

// 打开结果文件夹
const openResultFolder = async () => {
  if (!resultPath.value) return
  
  try {
    await baseAPI('open_folder_location', resultPath.value)
  } catch (error) {
    message.error('打开文件夹失败')
  }
}

onMounted(() => {
  // Remove event listeners
});

onUnmounted(() => {
  // Remove event listeners
});
</script>

<style lang="scss" scoped>
.single-convert {
  height: 100%;  
  .layout-container {
    height: 100%;
    display: flex;
    gap: 24px;
    
    // 左侧配置面板
    .config-panel {
      flex: 0 0 400px;
      margin-right: 10px;
      
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 16px;
          
          .el-form-item__label {
            font-weight: 500;
          }
        }
      }
      
      .upload-content {
        display: flex;
        gap: 8px;
      }
    }
    
    // 右侧预览面板
    .preview-panel {
      flex: 1;
      
      .preview-card {
        height: auto;
        
        :deep(.el-card__header) {
          .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 32px;
          }
        }
        
        .preview-content {
          .single-preview, .result-preview {
            display: flex;
            flex-direction: column;
            gap: 16px;
            
            .preview-image {
              max-width: 100%;
              max-height: 400px;
              display: flex;
              justify-content: center;
              align-items: center;
              
              :deep(.el-image) {
                max-width: 100%;
                max-height: 400px;
                
                img {
                  max-width: 100%;
                  max-height: 400px;
                  object-fit: contain;
                }
              }
            }
            
            .image-info {
              margin-top: 16px;
              padding: 16px;
              background-color: var(--el-fill-color-light);
              border-radius: 8px;
              
              .info-item {
                display: flex;
                margin-bottom: 8px;
                align-items: center;
                
                &:last-child {
                  margin-bottom: 0;
                }
                
                .label {
                  font-weight: 500;
                  color: var(--el-text-color-regular);
                  width: 80px;
                }
                
                .value {
                  color: var(--el-text-color-primary);
                  
                  &.path {
                    max-width: 300px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    margin-right: 8px;
                  }
                }
                
                .open-btn {
                  padding: 4px 8px;
                  font-size: 12px;
                  
                  .el-icon {
                    margin-right: 4px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // Add loading mask style
  :deep(.el-loading-mask) {
    background-color: rgba(255, 255, 255, 0.9);
  }
}
</style> 