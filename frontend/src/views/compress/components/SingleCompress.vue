<template>
  <div class="single-compress" v-loading="loading" element-loading-text="处理中..." element-loading-lock>
    <div class="layout-container">
      <!-- 顶部操作面板 -->
      <div class="operation-panel">
        <el-form label-position="left" inline>
          <el-form-item label="选择图片">
            <div class="upload-content">
              <UploadButton text="选择文件" size="default" @upload="handleClickUpload" />
              <StartProcessButton 
                text="开始压缩" 
                :loading="processing" 
                :disabled="!imageUrl || processing"
                @startProcess="startProcess"
              />
              <el-button 
                v-if="imageUrl" 
                @click="resetImage"
              >
                <el-icon><Back /></el-icon>
                重新选择
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="压缩质量">
            <template #label>
              <div class="label-with-icon">
                <span>边缘优化</span>
                <el-tooltip
                  content="质量越低，文件越小，但图片质量也会降低"
                  placement="top"
                  effect="light"
                >
                  <el-icon class="help-icon"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>
            <el-slider 
              v-model="quality" 
              :min="1" 
              :max="100" 
              :step="1"
              :format-tooltip="(val) => `${val}%`"
              style="width: 200px;"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 图片对比展示区域 -->
      <div class="comparison-panel" v-if="imageUrl">
        <div class="comparison-container">
          <!-- 左侧：原始图片 -->
          <div class="image-card original-image">
            <el-card class="preview-card">
              <template #header>
                <div class="preview-header">
                  <span class="title">原始图片</span>
                  <el-tag type="info" size="small">未压缩</el-tag>
                </div>
              </template>

              <div class="preview-content">
                <div class="preview-image">
                  <el-image 
                    :src="imageUrl" 
                    fit="contain"
                    :preview-src-list="[imageUrl]"
                  />
                </div>
                <div class="image-info" v-if="imageInfo">
                  <div class="info-item">
                    <span class="label">格式:</span>
                    <span class="value">{{ imageInfo.format }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">尺寸:</span>
                    <span class="value">{{ imageInfo.width }} × {{ imageInfo.height }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">文件大小:</span>
                    <span class="value size">{{ imageInfo.size_str }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">文件路径:</span>
                    <span class="value path">{{ imagePath }}</span>
                    <el-button size="small" @click="openOriginalFolder" class="open-btn">
                      <el-icon><FolderOpened /></el-icon>
                      打开
                    </el-button>
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 中间：对比箭头 -->
          <div class="comparison-arrow" v-if="resultUrl">
            <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            <div class="compression-ratio">
              <span class="ratio-text">{{ compressionRatio }}</span>
              <span class="ratio-label">压缩比例</span>
            </div>
          </div>

          <!-- 右侧：压缩结果 -->
          <div class="image-card compressed-image">
            <el-card class="preview-card">
              <template #header>
                <div class="preview-header">
                  <span class="title">压缩结果</span>
                  <el-tag 
                    :type="getCompressionRatioTagType" 
                    size="small"
                  >
                    {{ getCompressionRatioTagText }}
                  </el-tag>
                </div>
              </template>

              <div class="preview-content">
                <div v-if="!resultUrl" class="placeholder">
                  <el-icon class="placeholder-icon"><Picture /></el-icon>
                  <span class="placeholder-text">点击"开始压缩"查看结果</span>
                </div>
                
                <div v-else>
                  <div class="preview-image">
                    <el-image 
                      :src="resultUrl" 
                      fit="contain"
                      :preview-src-list="[resultUrl]"
                    />
                  </div>
                  <div class="image-info" v-if="resultInfo">
                    <div class="info-item">
                      <span class="label">格式:</span>
                      <span class="value">{{ resultInfo.format }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">尺寸:</span>
                      <span class="value">{{ resultInfo.width }} × {{ resultInfo.height }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">文件大小:</span>
                      <span class="value size" :class="getCompressionRatioClass">
                        {{ resultInfo.size_str }}
                      </span>
                    </div>
                    <div class="info-item">
                      <span class="label">文件路径:</span>
                      <span class="value path">{{ resultInfo.path }}</span>
                      <el-button size="small" @click="openResultFolder" class="open-btn">
                        <el-icon><FolderOpened /></el-icon>
                        打开
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { 
  Back, 
  FolderOpened,
  ArrowRight,
  Picture
} from '@element-plus/icons-vue'
import message from '@/utils/message'
import baseAPI from '@/apis/base'
import { compressImageAPI } from '@/apis/compress'
import UploadButton from '@/components/UploadButton.vue'
import StartProcessButton from '@/components/StartProcessButton.vue'

// 状态变量
const imageUrl = ref('')
const imagePath = ref('')
const resultUrl = ref('')
const resultPath = ref('')
const processing = ref(false)
const loading = ref(false)
const quality = ref(80)
const imageInfo = ref(null)
const resultInfo = ref(null)

// 计算属性
const compressionRatio = computed(() => {
  if (!imageInfo.value || !resultInfo.value) return ''
  
  const originalSize = parseFloat(imageInfo.value.size)
  const compressedSize = parseFloat(resultInfo.value.size)
  
  if (originalSize === 0) return '0%'
  
  const ratio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1)
  return `${ratio}%`
})

const getCompressionRatioClass = computed(() => {
  if (!compressionRatio.value) return ''
  
  const ratio = parseFloat(compressionRatio.value.replace('%', ''))
  if (ratio >= 50) return 'excellent'
  if (ratio >= 30) return 'good'
  if (ratio >= 10) return 'moderate'
  return 'poor'
})

const getCompressionRatioTagType = computed(() => {
  if (!compressionRatio.value) return 'info'
  
  const ratio = parseFloat(compressionRatio.value.replace('%', ''))
  if (ratio >= 50) return 'success'
  if (ratio >= 30) return 'warning'
  if (ratio >= 10) return 'info'
  return 'danger'
})

const getCompressionRatioTagText = computed(() => {
  if (!compressionRatio.value) return '未压缩'
  
  const ratio = parseFloat(compressionRatio.value.replace('%', ''))
  if (ratio >= 50) return '优秀'
  if (ratio >= 30) return '良好'
  if (ratio >= 10) return '中等'
  return '较差'
})

// 文件上传相关方法
const handleClickUpload = async () => {
  loading.value = true
  try {
    const response = await baseAPI('open_convert_img_file', false)
    if (response.code === 200 && response.data.selected_file) {
      resetImage()
      imageUrl.value = response.data.image_url
      imagePath.value = response.data.selected_file
      
      // 获取图片信息
      await getImageInfo(response.data.selected_file)
      
      message.success('上传成功')
    }
  } catch (error) {
    message.error('上传失败')
    console.error('Error uploading file:', error)
  } finally {
    loading.value = false
  }
}

// 获取图片信息
const getImageInfo = async (filePath) => {
  try {
    const response = await baseAPI('get_image_info', filePath)
    if (response.code === 200) {
      imageInfo.value = response.data
    }
    else{
      message.error(response.msg || "获取图片信息失败")
    }
  } catch (error) {
    console.error('Error getting image info:', error)
  }
}

// 处理相关方法
const startProcess = async () => {
  if (!imageUrl.value) {
    message.warning('请先选择图片')
    return
  }
  
  processing.value = true
  try {
    const params = {
      input_path: imagePath.value,
      quality: quality.value
    }

    const response = await compressImageAPI('single_compress_image', params)
    if (response.code === 200) {
      resultPath.value = response.data.output_path
      
      // 获取压缩后的图片信息
      await getCompressedImageInfo(response.data.output_path)
      
      // 获取压缩后的图片URL
      const resultResponse = await baseAPI('get_image_url', response.data.output_path)
      if (resultResponse.code === 200) {
        resultUrl.value = resultResponse.data.image_url
      }
      
      message.success('压缩成功')
      
      // 打开压缩后的文件
      await baseAPI('open_and_select_file', response.data.output_path)
    } else {
      throw new Error(response.msg || '压缩失败')
    }
  } catch (error) {
    message.error(`压缩失败: ${error.message}`)
    console.error('Error compressing image:', error)
  } finally {
    processing.value = false
  }
}

// 获取压缩后的图片信息
const getCompressedImageInfo = async (filePath) => {
  try {
    const response = await baseAPI('get_image_info', filePath)
    if (response.code === 200) {
      resultInfo.value = response.data
    }
  } catch (error) {
    console.error('Error getting compressed image info:', error)
  }
}

const resetImage = () => {
  imageUrl.value = ''
  imagePath.value = ''
  resultUrl.value = ''
  resultPath.value = ''
  imageInfo.value = null
  resultInfo.value = null
}

// 打开结果文件夹
const openResultFolder = async () => {
  if (!resultPath.value) return
  
  try {
    const res = await baseAPI('open_and_select_file', resultPath.value)
    if (res.code != 200){
      message.error(res.msg || "打开失败")
    }
  } catch (error) {
    message.error('打开文件夹失败')
  }
}

// 打开原始文件夹
const openOriginalFolder = async () => {
  if (!imagePath.value) return
  
  try {
    const response = await baseAPI('open_and_select_file', imagePath.value)
    if (response.code === 200) {
      message.success('打开文件夹成功')
    } else {
      message.error(response.msg)
    }
  } catch (error) {
    message.error('打开文件夹失败')
  }
}

onMounted(() => {
  // Remove event listeners
});

onUnmounted(() => {
  // Remove event listeners
});
</script>

<style lang="scss" scoped>
.single-compress {
  height: 100%;  
  .layout-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    
    // 顶部操作面板
    .operation-panel {
      flex: 0 0 auto;
      padding: 16px;
      background-color: var(--el-fill-color-light);
      border-radius: 8px;
      
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 12px;
          
          .el-form-item__label {
            font-weight: 500;
          }
        }
      }
      
      .upload-content {
        display: flex;
        gap: 6px;
      }
    }
    
    // 图片对比展示区域
    .comparison-panel {
      flex: 1;
      display: flex;
      gap: 16px;
      padding: 16px;
      background-color: var(--el-fill-color-light);
      border-radius: 8px;
      
      .comparison-container {
        display: flex;
        align-items: center;
        gap: 16px;
        width: 100%;
        
        .image-card {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 12px;
          max-width: 400px;
          
          .preview-card {
            height: auto;
            
            :deep(.el-card__header) {
              .preview-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                min-height: 28px;
                
                .title {
                  font-weight: 500;
                }
              }
            }
            
            .preview-content {
              .placeholder {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 280px;
                color: var(--el-text-color-secondary);
                
                .placeholder-icon {
                  font-size: 44px;
                  margin-bottom: 8px;
                }
                
                .placeholder-text {
                  font-size: 14px;
                }
              }
              
              .preview-image {
                max-width: 100%;
                max-height: 280px;
                display: flex;
                justify-content: center;
                align-items: center;
                
                :deep(.el-image) {
                  max-width: 100%;
                  max-height: 280px;
                  
                  img {
                    max-width: 100%;
                    max-height: 280px;
                    object-fit: contain;
                  }
                }
              }
              
              .image-info {
                margin-top: 10px;
                padding: 10px;
                background-color: var(--el-fill-color-light);
                border-radius: 6px;
                
                .info-item {
                  display: flex;
                  margin-bottom: 5px;
                  align-items: center;
                  
                  &:last-child {
                    margin-bottom: 0;
                  }
                  
                  .label {
                    font-weight: 500;
                    color: var(--el-text-color-regular);
                    width: 70px;
                    font-size: 13px;
                  }
                  
                  .value {
                    color: var(--el-text-color-primary);
                    font-size: 13px;
                    
                    &.path {
                      max-width: 180px;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                      margin-right: 6px;
                    }
                    
                    &.excellent {
                      color: var(--el-color-success);
                      font-weight: 600;
                    }
                    
                    &.good {
                      color: var(--el-color-warning);
                      font-weight: 600;
                    }
                    
                    &.moderate {
                      color: var(--el-color-info);
                      font-weight: 600;
                    }
                    
                    &.poor {
                      color: var(--el-color-danger);
                      font-weight: 600;
                    }
                  }
                  
                  .open-btn {
                    padding: 2px 5px;
                    font-size: 11px;
                    
                    .el-icon {
                      margin-right: 2px;
                    }
                  }
                }
              }
            }
          }
        }
        
        .comparison-arrow {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 6px;
          min-width: 70px;
          
          .arrow-icon {
            font-size: 28px;
            color: var(--el-text-color-secondary);
          }
          
          .compression-ratio {
            text-align: center;
            
            .ratio-text {
              font-size: 18px;
              font-weight: bold;
              color: var(--el-text-color-primary);
            }
            
            .ratio-label {
              font-size: 12px;
              color: var(--el-text-color-secondary);
            }
          }
        }
      }
    }
  }

  // Add loading mask style
  :deep(.el-loading-mask) {
    background-color: rgba(255, 255, 255, 0.9);
  }
}

.label-with-icon {
  display: flex;
  align-items: center;
  gap: 4px;

  .help-icon {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    cursor: help;
    
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>