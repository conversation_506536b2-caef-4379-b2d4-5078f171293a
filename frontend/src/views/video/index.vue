<template>
  <div class="video-container">
    <PageHeader
      title="视频去水印工具"
      description="专业的视频水印去除工具，支持去除固定位置水印、字幕等内容"
    />

    <div class="tab-header">
      <div class="el-tabs">
        <div class="el-tabs__header">
          <div class="el-tabs__nav-wrap">
            <div class="el-tabs__nav">
              <div
                class="el-tabs__item"
                :class="{ 'is-active': activeTab === 'manual' }"
                @click="handleTabChange('manual')"
              >
                手动处理
              </div>
              <div
                class="el-tabs__item"
                :class="{ 'is-active': activeTab === 'auto' }"
                @click="handleTabChange('auto')"
              >
                自动处理
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="tab-content">
      <router-view></router-view>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import PageHeader from '@/components/PageHeader.vue'

const router = useRouter()
const route = useRoute()

const activeTab = ref('manual')

// 根据当前路由设置激活的标签
const updateActiveTab = () => {
  const path = route.path
  if (path.includes('/auto')) {
    activeTab.value = 'auto'
  } else {
    activeTab.value = 'manual'
  }
}

onMounted(() => {
  updateActiveTab()
})

// 监听路由变化
watch(() => route.path, () => {
  updateActiveTab()
})

// 处理标签切换
const handleTabChange = (value) => {
  if (value === 'manual') {
    router.push('/video/manual')
  } else {
    router.push('/video/auto')
  }
}
</script>

<style lang="scss" scoped>
.video-container {
  max-width: 1200px;
  margin: 0 auto;

  .tab-header {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    padding: 0 20px;

    .el-tabs {
      .el-tabs__header {
        margin: 0;
        border-bottom: 1px solid var(--el-border-color-light);
      }

      .el-tabs__nav {
        display: flex;
        position: relative;
      }

      .el-tabs__item {
        padding: 0 20px;
        height: 40px;
        line-height: 40px;
        cursor: pointer;
        position: relative;
        font-size: 14px;
        color: var(--el-text-color-regular);
        transition: color 0.3s;

        &:hover {
          color: var(--el-color-primary);
        }

        &.is-active {
          color: var(--el-color-primary);
          
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--el-color-primary);
          }
        }
      }
    }
  }

  .tab-content {
    min-height: 200px;
  }
}
</style> 