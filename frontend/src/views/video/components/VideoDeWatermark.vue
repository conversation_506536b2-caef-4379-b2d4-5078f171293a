<template>
  <div class="video-dewatermark" v-loading="loading" element-loading-text="处理中...">
    <div v-if="selectedVideoPath" class="selected-video-path">
      <p>选择的视频文件路径: {{ selectedVideoPath }}</p>
    </div>
     
    <!-- 步骤条 -->
    <el-steps :active="currentStep" finish-status="success" class="mb-4">
      <el-step title="选择视频" description="选择需要处理的视频文件" />
      <el-step title="选择帧" description="选择要处理的视频帧" />
      <el-step title="标注水印" description="标注需要去除的水印区域" />
      <el-step title="选择模型" description="选择处理模型" />
      <el-step title="AI处理" description="正在进行水印去除" />
      <el-step title="处理结束" description="处理完成" />
    </el-steps>
    <!-- 步骤1：选择视频 -->
    <div v-if="currentStep === 0" class="step-content">
      <UploadButton text="选择视频文件" size="default" @upload="handleSelectVideo" />
    </div>

    <!-- 步骤2：选择帧 -->
    <div v-if="currentStep === 1" class="step-content">
      <el-dialog
        v-model="showFrameSelector"
        title="选择视频帧"
        width="80%"
        :close-on-click-modal="false"
      >
        <div class="frame-selector">
          <video
            ref="videoRef"
            :src="videoUrl"
            controls
            @loadedmetadata="onVideoLoaded"
            @error="onVideoError"
            @loadstart="isLoading = true"
            @canplay="isLoading = false"
          ></video>
          <div v-if="isLoading" class="video-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            加载中...
          </div>
          <div class="frame-controls">
            <el-slider
              v-model="currentTime"
              :min="0"
              :max="duration"
              :step="0.1"
              @input="onTimeChange"
            />
            <el-button type="primary" @click="selectCurrentFrame">
              选择当前帧
            </el-button>
          </div>
        </div>
      </el-dialog>
      
      <el-button type="primary" @click="showFrameSelector = true">
        选择视频帧
      </el-button>

      <div class="step-actions">
        <el-button @click="currentStep--">上一步</el-button>
        <el-button type="primary" @click="currentStep++" :disabled="!selectedFrameBase64">
          下一步
        </el-button>
      </div>
      
      <div v-if="selectedFrameBase64" class="selected-frame">
        <img :src="selectedFrameBase64" style="width: 400px; height: 300px;" />
      </div>
      
      
    </div>

    <!-- 步骤3：标注水印 -->
    <div v-if="currentStep === 2" class="step-content">
      <el-button type="primary" @click="showMaskEditor = true">
        <el-icon><Edit /></el-icon>
        开始标记水印区域
      </el-button>
      <el-dialog
        v-model="showMaskEditor"
        title="标记水印区域"
        width="80%"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        destroy-on-close
      >
        <ImageEditor
          v-if="showMaskEditor && selectedVideoPath"
          :imageBase64="originalBase64"
          ref="imageEditorRef"
          @save="handleMaskSave"
        />
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showMaskEditor = false">取消</el-button>
            <el-button type="primary" @click="handleMaskComplete">
              完成标记
            </el-button>
          </span>
        </template>
      </el-dialog>
      <div v-if="maskData" class="mask-status">
        <el-tag type="success">已完成水印区域标记</el-tag>
      </div>
    </div>

    <!-- 新增步骤：选择模型 -->
    <div v-if="currentStep === 3" class="step-content">
      <div class="model-selection">
        <h3>选择处理模型</h3>
        <div class="model-options">
          <el-card
            v-for="model in modelOptions"
            :key="model.id"
            :class="['model-card', { active: selectedModel === model.id }]"
            @click="selectModel(model.id)"
          >
            <div class="model-info">
              <div class="model-header">
                <span class="model-name">{{ model.name }}</span>
                <el-tag :type="model.tagType" size="small">{{ model.tag }}</el-tag>
              </div>
              <p class="model-description">{{ model.description }}</p>
              <ul class="model-features">
                <li v-for="feature in model.features" :key="feature">
                  <el-icon><Check /></el-icon>
                  {{ feature }}
                </li>
              </ul>
            </div>
          </el-card>
        </div>
        <div class="step-actions">
          <el-button @click="currentStep--">上一步</el-button>
          <el-button 
            type="primary" 
            @click="currentStep++"
            :disabled="!selectedModel"
          >
            下一步
          </el-button>
        </div>
      </div>
    </div>

    <!-- 步骤4：确认处理 -->
    <div v-if="currentStep === 4" class="step-content">
      <div class="confirm-process">
        <h3>确认处理</h3>
        <p class="confirm-tips">已完成水印区域标注，是否开始处理视频？</p>
        <p class="process-warning">注意：视频处理时间取决于视频长度，请耐心等待</p>
        <div class="step-actions">
          <el-button @click="currentStep--">上一步</el-button>
          <el-button 
            type="primary" 
            @click="handleProcess"
          >
            开始处理视频
          </el-button>
        </div>
      </div>
    </div>

    <!-- 步骤5：处理中 -->
    <div v-if="currentStep === 5" class="step-content">
      <div class="processing-status">
        <div class="process-title">
          <h3>{{ statusInfo }}</h3>
          <p class="process-subtitle">请耐心等待，处理时间取决于视频长度...</p>
        </div>
        <el-progress
          :percentage="processPercentage"
          :status="progressStatus"
          :stroke-width="26"
        >
          <template #default="{ percentage }">
            <span class="percentage-value">{{ percentage.toFixed(2) }}%</span>
          </template>
        </el-progress>
      </div>
    </div>

    <!-- 步骤5 处理完成 -->
    <div v-if="currentStep === 6" class="step-content">
      <div v-if="isProcessComplete" class="process-complete">
        <el-result
          icon="success"
          title="处理完成"
          sub-title="视频水印去除已完成"
        >
          <template #extra>
            <el-button type="primary" @click="handleReset">处理新视频</el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Edit, Loading, Check } from '@element-plus/icons-vue'
import ImageEditor from './ImageEditor.vue'
import baseAPI from '@/apis/base';
import message from '@/utils/message'  // 导入消息工具
import { videoDeWatermarkAPI } from '@/apis/video'
import UploadButton from '@/components/UploadButton.vue'



const currentStep = ref(0)
const showProgress = ref(false)
const processPercentage = ref(0)
const progressStatus = ref('')
const selectedVideoPath = ref('')
const maskData = ref(null)
const originalBase64 = ref('')
const showMaskEditor = ref(false)
const isProcessing = ref(false)
const isProcessComplete = ref(false)
const imageEditorRef = ref(null)
const videoRef = ref(null)
const showFrameSelector = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const selectedFrameBase64 = ref('')
const videoUrl = ref('')
const isLoading = ref(false)
const videoObjectUrl = ref('')
const statusInfo = ref('')
const loading = ref(false)
// 默认为fast模型
const selectedModel = ref('fast')
const modelOptions = [
  {
    id: 'fast',
    name: '快速模型',
    tag: '推荐',
    tagType: 'success',
    description: '适用于简单水印去除，处理速度快',
    features: [
      '处理速度快',
      '适合简单水印',
      '资源占用低'
    ]
  },
  {
    id: 'quality',
    name: '高质量模型',
    tag: '专业',
    tagType: 'warning',
    description: '适用于复杂水印去除，效果更好',
    features: [
      '去除效果好',
      '适合复杂水印',
      '支持细节修复'
    ]
  }
]

const getVideoInfo = async () => {
  loading.value = true
  const response = await videoDeWatermarkAPI('get_video_info', selectedVideoPath.value)
  if (response.code === 200){
    console.log(response.data)
  }else{
    message.error(response.msg)
  }
  loading.value = false
}

// 修改视频加载方法
const loadVideo = async (url) => {
  try {
    isLoading.value = true
    const response = await fetch(url)
    const blob = await response.blob()
    if (videoObjectUrl.value) {
      URL.revokeObjectURL(videoObjectUrl.value)
    }
    videoObjectUrl.value = URL.createObjectURL(blob)
    return true
  } catch (error) {
    console.error('Error loading video:', error)
    return false
  } finally {
    isLoading.value = false
  }
}

// 修改选择视频文件的处理函数
const handleSelectVideo = async () => {
  loading.value = true
  try {
    const response = await baseAPI('open_video_file', false)
    const selected_files = response.data['selected_files']
    if (selected_files.length > 0) {
      selectedVideoPath.value = selected_files[0]
      await getVideoInfo()
      
      // 获取视频的 URL
      const urlResponse = await baseAPI('get_video_url', selectedVideoPath.value)
      if (urlResponse.code === 200) {
        const success = await loadVideo(urlResponse.data.video_url)
        if (success) {
          videoUrl.value = videoObjectUrl.value
          currentStep.value++
        } else {
          message.error('视频加载失败')
        }
      } else {
        message.error('获取视频数据失败')
      }
    } else {
      message.warning('未选择文件')
    }
  } catch (error) {
    message.error(error)
  } finally {
    loading.value = false
  }
}

// 保存 mask 数据
const handleMaskSave = (data) => {
  console.log('保存的标记数据:', data)
  console.log('原始图片 Base64:', data.originalBase64)
  console.log('遮罩图片 Base64:', data.maskBase64)
  maskData.value = data
}

// 处理视频
const handleProcess = async () => {
  if (!maskData.value || !selectedModel.value) return
  
  try {
    currentStep.value += 1
    isProcessing.value = true
    showProgress.value = true
    processPercentage.value = 0
    progressStatus.value = ''

    const response = await videoDeWatermarkAPI('process_video_with_mask', {
      video_path: selectedVideoPath.value,
      mask_data: maskData.value,
      model_type: selectedModel.value
    })

    if (response.code !== 200) {
      throw new Error(response.msg)
    }
  } catch (error) {
    console.error('处理失败:', error)
    progressStatus.value = 'exception'
    message.error('处理失败: ' + error.message)
    currentStep.value -= 1
  }
}

// 处理完成标记
const handleMaskComplete = () => {
  if (imageEditorRef.value) {
    imageEditorRef.value.saveMask()
    
    if (maskData.value) {
      console.log('完成标记，最终数据:', maskData.value)
      message.success('水印区域标记完成')
      showMaskEditor.value = false
      currentStep.value++ // 进入确认步骤
    } else {
      message.warning('请先标记水印区域')
    }
  }
}

// 重置状态，处理新视频
const handleReset = () => {
  currentStep.value = 0
  showProgress.value = false
  processPercentage.value = 0
  progressStatus.value = ''
  selectedVideoPath.value = ''
  selectedFrameBase64.value = ''
  videoUrl.value = ''
  maskData.value = null
  originalBase64.value = ''
  showMaskEditor.value = false
  isProcessing.value = false
  isProcessComplete.value = false
}

const onVideoLoaded = () => {
  if (videoRef.value) {
    duration.value = videoRef.value.duration
    console.log('Video duration:', duration.value)
  }
}

const onTimeChange = (value) => {
  if (videoRef.value) {
    videoRef.value.currentTime = value
    console.log('Current time:', value)
  }
}

const selectCurrentFrame = async () => {
  try {
    const response = await videoDeWatermarkAPI('get_frame_image', {
      video_path: selectedVideoPath.value,
      time_position: currentTime.value
    })
    
    if (response.code === 200) {
      selectedFrameBase64.value = response.data.frame_base64
      originalBase64.value = response.data.frame_base64  // 用于后续标注
      showFrameSelector.value = false
      message.success('已选择视频帧')
    } else {
      message.error(response.msg)
    }
  } catch (error) {
    console.error('Error selecting frame:', error)
    message.error('获取视频帧失败')
  }
}

const onVideoError = (error) => {
  console.error('Video error:', error)
  message.error('视频加载失败')
  isLoading.value = false
}


const handleProgress = (event) => {
  const progressData = event.detail
  const progress = parseFloat(progressData.progress) || 0
  const status = progressData.message || ''
  processPercentage.value = progress
  statusInfo.value = status
  if (progress === 100) {
    progressStatus.value = 'success'
    isProcessComplete.value = true
    currentStep.value = 6
  } else if (progress === 0 && status.includes('失败')) {
    progressStatus.value = 'exception'
    message.error(status)
  }
}
  
// 添加进度监听
onMounted(() => {
  window.addEventListener('ProcessProgress', handleProgress)
})

// 组件卸载时清理
onUnmounted(() => {
  if (videoObjectUrl.value) {
    URL.revokeObjectURL(videoObjectUrl.value)
  }
  window.removeEventListener('ProcessProgress', handleProgress)

})

const selectModel = (modelId) => {
  selectedModel.value = modelId
}
</script>

<style lang="scss" scoped>
.video-dewatermark {
  .selected-video-path {
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 16px;
    border: 1px solid #e4e7ed;

    p {
      margin: 0;
      color: #606266;
      word-break: break-all;
    }
  }

  .mb-4 {
    margin-bottom: 16px;
    margin-top: 40px;
  }

  .step-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .frame-selector {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 16px;

    video {
      width: 100%;
      max-height: 60vh;
      background-color: #000;
    }

    .video-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--el-color-primary);
    }

    .frame-controls {
      display: flex;
      gap: 16px;
      align-items: center;
    }
  }

  .selected-frame {
    margin: 16px auto;
    max-width: 800px;
    width: 100%;
    text-align: center;

    img {
      width: 400px;
      height: 300px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      object-fit: contain;
    }
  }

  .processing-status {
    width: 100%;
    max-width: 600px;
    margin: 20px auto;

    .process-title {
      text-align: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        color: var(--el-color-primary);
        font-size: 24px;
      }

      .process-subtitle {
        margin: 8px 0 0;
        color: #909399;
        font-size: 14px;
      }
    }
  }

  .confirm-process {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;

    h3 {
      // color: var(--el-color-primary);
      font-size: 24px;
      margin-bottom: 16px;
    }

    .confirm-tips {
      font-size: 16px;
      color: #606266;
      margin-bottom: 8px;
    }

    .process-warning {
      color: #e6a23c;
      font-size: 14px;
      margin-bottom: 20px;
    }
  }

  .step-actions {
    margin-top: 16px;
    display: flex;
    gap: 16px;
  }

  .percentage-value {
    display: block;
    font-size: 20px;
    font-weight: bold;
  }

  .model-selection {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;

    h3 {
      text-align: center;
      margin-bottom: 20px;
      color: var(--el-text-color-primary);
    }

    .model-options {
      display: flex;
      gap: 20px;
      justify-content: center;
      margin-bottom: 30px;

      .model-card {
        flex: 1;
        max-width: 340px;
        cursor: pointer;
        transition: all 0.3s;
        border: 2px solid transparent;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &.active {
          border-color: var(--el-color-primary);
        }

        .model-info {
          .model-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .model-name {
              font-size: 16px;
              font-weight: 500;
            }
          }

          .model-description {
            color: var(--el-text-color-secondary);
            font-size: 14px;
            margin-bottom: 16px;
          }

          .model-features {
            list-style: none;
            padding: 0;
            margin: 0;

            li {
              display: flex;
              align-items: center;
              gap: 8px;
              color: var(--el-text-color-regular);
              font-size: 13px;
              margin-bottom: 8px;

              .el-icon {
                color: var(--el-color-success);
              }
            }
          }
        }
      }
    }

    .step-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
    }
  }
}
</style>
