<template>
  <div class="video-auto" v-loading="loading" element-loading-text="处理中...">
    <div class="main-content">
      <!-- 选择文件和处理按钮 -->
      <div class="action-row">
        <div class="label">选择视频</div>
        <div class="controls">
          <UploadButton text="选择视频" size="default" @upload="handleSelectVideo" />
          <StartProcessButton 
            text="开始处理" 
            :loading="isProcessing" 
            :disabled="!selectedVideoPath || isProcessing"
            @startProcess="handleProcess"
          />
        </div>
      </div>

      <!-- 模型选择 -->
      <div class="action-row">
        <div class="label">处理模型</div>
        <div class="controls">
          <el-radio-group v-model="selectedModel">
            <el-radio value="fast">快速</el-radio>
            <el-radio value="quality">高质量</el-radio>
          </el-radio-group>
        </div>
      </div>

      <!-- 选择的文件路径 -->
      <div v-if="selectedVideoPath" class="selected-file">
        <p>已选择: {{ selectedVideoPath }}</p>
      </div>

      <!-- 处理进度 -->
      <div v-if="showProgress" class="progress-section">
        <div class="process-title">
          <h3>{{ statusInfo }}</h3>
          <p class="process-subtitle">请耐心等待，处理时间取决于视频长度...</p>
        </div>
        <el-progress
          :percentage="processPercentage"
          :status="progressStatus"
          :stroke-width="20"
        >
          <template #default="{ percentage }">
            <span class="percentage-value">{{ percentage.toFixed(2) }}%</span>
          </template>
        </el-progress>
      </div>

      <!-- 处理完成提示 -->
      <div v-if="isProcessComplete" class="complete-info">
          <el-button type="primary" @click="handleReset">处理新视频</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import baseAPI from '@/apis/base'
import message from '@/utils/message'
import { videoDeWatermarkAPI } from '@/apis/video'
import UploadButton from '@/components/UploadButton.vue'
import StartProcessButton from '@/components/StartProcessButton.vue'
// 状态变量
const loading = ref(false)
const selectedVideoPath = ref('')
const isProcessing = ref(false)
const showProgress = ref(false)
const processPercentage = ref(0)
const progressStatus = ref('')
const statusInfo = ref('')
const isProcessComplete = ref(false)
const selectedModel = ref('fast')

// 获取视频信息
const getVideoInfo = async () => {
  loading.value = true
  try {
    const response = await videoDeWatermarkAPI('get_video_info', selectedVideoPath.value)
    if (response.code !== 200) {
      throw new Error(response.msg)
    }
    console.log('Video info:', response.data)
  } catch (error) {
    message.error(error.message)
  } finally {
    loading.value = false
  }
}

// 选择视频文件
const handleSelectVideo = async () => {
  loading.value = true
  try {
    const response = await baseAPI('open_video_file', false)
    const selected_files = response.data['selected_files']
    if (selected_files.length > 0) {
      selectedVideoPath.value = selected_files[0]
      await getVideoInfo()
      message.success('视频选择成功')
    } else {
      message.warning('未选择文件')
    }
  } catch (error) {
    message.error(error.message)
  } finally {
    loading.value = false
  }
}

// 开始处理视频
const handleProcess = async () => {
  if (!selectedVideoPath.value || !selectedModel.value) return
  loading.value = true
  
  try {
    isProcessing.value = true
    showProgress.value = true
    processPercentage.value = 0
    progressStatus.value = ''
    statusInfo.value = '正在处理中...'

    const response = await videoDeWatermarkAPI('auto_remove_watermark', {
      video_path: selectedVideoPath.value,
      model_type: selectedModel.value
    })

    if (response.code !== 200) {
      throw new Error(response.msg)
    }
  } catch (error) {
    console.error('处理失败:', error)
    progressStatus.value = 'exception'  
    message.error('处理失败: ' + error.message)
    isProcessing.value = false
    showProgress.value = false
  } finally {
    loading.value = false
  }
}

// 重置状态
const handleReset = () => {
  selectedVideoPath.value = ''
  isProcessing.value = false
  showProgress.value = false
  processPercentage.value = 0
  progressStatus.value = ''
  statusInfo.value = ''
  isProcessComplete.value = false
}

// 处理进度事件
const handleProgress = (event) => {
  const progressData = event.detail
  const progress = parseFloat(progressData.progress) || 0
  const status = progressData.message || ''
  
  processPercentage.value = progress
  statusInfo.value = status
  
  if (progress === 100) {
    progressStatus.value = 'success'
    isProcessComplete.value = true
    isProcessing.value = false
  } else if (progress === 0 && status.includes('失败')) {
    progressStatus.value = 'exception'
    message.error(status)
    isProcessing.value = false
  }
}

// 组件挂载时添加进度监听
onMounted(() => {
  window.addEventListener('ProcessProgress', handleProgress)
})

// 组件卸载时移除监听
onUnmounted(() => {
  window.removeEventListener('ProcessProgress', handleProgress)
})
</script>

<style lang="scss" scoped>
.video-auto {
  // 添加透明 loading 遮罩的样式
  :deep(.el-loading-mask) {
    background-color: rgba(255, 255, 255, 0.3);
  }

  .main-content {
    max-width: 800px;
  }

  .action-row {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .label {
      width: 80px;
      color: var(--el-text-color-regular);
      font-size: 15px;
    }

    .controls {
      flex: 1;
      display: flex;
      gap: 12px;
      align-items: center;

      .el-radio-group {
        display: flex;
        gap: 24px;
      }
    }
  }

  .selected-file {
    margin: 16px 0;
    padding: 12px 16px;
    background-color: var(--el-fill-color-light);
    border-radius: 4px;
    
    p {
      margin: 0;
      color: var(--el-text-color-regular);
      font-size: 14px;
      word-break: break-all;
    }
  }

  .progress-section {
    margin-top: 32px;

    .process-title {
      text-align: center;
      margin-bottom: 20px;

      h3 {
        margin: 0;
        color: var(--el-color-primary);
        font-size: 24px;
      }

      .process-subtitle {
        margin: 8px 0 0;
        color: #909399;
        font-size: 14px;
      }
    }
    .el-progress {
      margin-bottom: 12px;
    }

    .status-info {
      color: var(--el-text-color-regular);
      font-size: 14px;
      margin-top: 8px;
    }

    .percentage-value {
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
  }

  .complete-info {
    margin-top: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

}
</style>
