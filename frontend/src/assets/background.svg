<svg xmlns="http://www.w3.org/2000/svg" width="1440" height="900" viewBox="0 0 1440 900">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f0f2f5" stop-opacity="0.8" />
      <stop offset="100%" stop-color="#e6f7ff" stop-opacity="0.8" />
    </linearGradient>
    <filter id="blur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="30" />
    </filter>
  </defs>
  
  <rect width="1440" height="900" fill="url(#gradient)" />
  
  <!-- Abstract shapes -->
  <circle cx="200" cy="150" r="80" fill="#1890ff" opacity="0.1" filter="url(#blur)" />
  <circle cx="1200" cy="600" r="100" fill="#1890ff" opacity="0.05" filter="url(#blur)" />
  <circle cx="400" cy="700" r="60" fill="#1890ff" opacity="0.05" filter="url(#blur)" />
  <circle cx="900" cy="200" r="120" fill="#1890ff" opacity="0.05" filter="url(#blur)" />
  
  <!-- Pattern elements -->
  <g fill="#1890ff" opacity="0.03">
    <circle cx="100" cy="100" r="4" />
    <circle cx="100" cy="200" r="4" />
    <circle cx="100" cy="300" r="4" />
    <circle cx="100" cy="400" r="4" />
    <circle cx="100" cy="500" r="4" />
    <circle cx="100" cy="600" r="4" />
    <circle cx="100" cy="700" r="4" />
    <circle cx="100" cy="800" r="4" />
    
    <circle cx="200" cy="100" r="4" />
    <circle cx="200" cy="200" r="4" />
    <circle cx="200" cy="300" r="4" />
    <circle cx="200" cy="400" r="4" />
    <circle cx="200" cy="500" r="4" />
    <circle cx="200" cy="600" r="4" />
    <circle cx="200" cy="700" r="4" />
    <circle cx="200" cy="800" r="4" />
    
    <circle cx="300" cy="100" r="4" />
    <circle cx="300" cy="200" r="4" />
    <circle cx="300" cy="300" r="4" />
    <circle cx="300" cy="400" r="4" />
    <circle cx="300" cy="500" r="4" />
    <circle cx="300" cy="600" r="4" />
    <circle cx="300" cy="700" r="4" />
    <circle cx="300" cy="800" r="4" />
    
    <circle cx="400" cy="100" r="4" />
    <circle cx="400" cy="200" r="4" />
    <circle cx="400" cy="300" r="4" />
    <circle cx="400" cy="400" r="4" />
    <circle cx="400" cy="500" r="4" />
    <circle cx="400" cy="600" r="4" />
    <circle cx="400" cy="700" r="4" />
    <circle cx="400" cy="800" r="4" />
    
    <circle cx="500" cy="100" r="4" />
    <circle cx="500" cy="200" r="4" />
    <circle cx="500" cy="300" r="4" />
    <circle cx="500" cy="400" r="4" />
    <circle cx="500" cy="500" r="4" />
    <circle cx="500" cy="600" r="4" />
    <circle cx="500" cy="700" r="4" />
    <circle cx="500" cy="800" r="4" />
    
    <circle cx="600" cy="100" r="4" />
    <circle cx="600" cy="200" r="4" />
    <circle cx="600" cy="300" r="4" />
    <circle cx="600" cy="400" r="4" />
    <circle cx="600" cy="500" r="4" />
    <circle cx="600" cy="600" r="4" />
    <circle cx="600" cy="700" r="4" />
    <circle cx="600" cy="800" r="4" />
    
    <circle cx="700" cy="100" r="4" />
    <circle cx="700" cy="200" r="4" />
    <circle cx="700" cy="300" r="4" />
    <circle cx="700" cy="400" r="4" />
    <circle cx="700" cy="500" r="4" />
    <circle cx="700" cy="600" r="4" />
    <circle cx="700" cy="700" r="4" />
    <circle cx="700" cy="800" r="4" />
    
    <circle cx="800" cy="100" r="4" />
    <circle cx="800" cy="200" r="4" />
    <circle cx="800" cy="300" r="4" />
    <circle cx="800" cy="400" r="4" />
    <circle cx="800" cy="500" r="4" />
    <circle cx="800" cy="600" r="4" />
    <circle cx="800" cy="700" r="4" />
    <circle cx="800" cy="800" r="4" />
    
    <circle cx="900" cy="100" r="4" />
    <circle cx="900" cy="200" r="4" />
    <circle cx="900" cy="300" r="4" />
    <circle cx="900" cy="400" r="4" />
    <circle cx="900" cy="500" r="4" />
    <circle cx="900" cy="600" r="4" />
    <circle cx="900" cy="700" r="4" />
    <circle cx="900" cy="800" r="4" />
    
    <circle cx="1000" cy="100" r="4" />
    <circle cx="1000" cy="200" r="4" />
    <circle cx="1000" cy="300" r="4" />
    <circle cx="1000" cy="400" r="4" />
    <circle cx="1000" cy="500" r="4" />
    <circle cx="1000" cy="600" r="4" />
    <circle cx="1000" cy="700" r="4" />
    <circle cx="1000" cy="800" r="4" />
    
    <circle cx="1100" cy="100" r="4" />
    <circle cx="1100" cy="200" r="4" />
    <circle cx="1100" cy="300" r="4" />
    <circle cx="1100" cy="400" r="4" />
    <circle cx="1100" cy="500" r="4" />
    <circle cx="1100" cy="600" r="4" />
    <circle cx="1100" cy="700" r="4" />
    <circle cx="1100" cy="800" r="4" />
    
    <circle cx="1200" cy="100" r="4" />
    <circle cx="1200" cy="200" r="4" />
    <circle cx="1200" cy="300" r="4" />
    <circle cx="1200" cy="400" r="4" />
    <circle cx="1200" cy="500" r="4" />
    <circle cx="1200" cy="600" r="4" />
    <circle cx="1200" cy="700" r="4" />
    <circle cx="1200" cy="800" r="4" />
    
    <circle cx="1300" cy="100" r="4" />
    <circle cx="1300" cy="200" r="4" />
    <circle cx="1300" cy="300" r="4" />
    <circle cx="1300" cy="400" r="4" />
    <circle cx="1300" cy="500" r="4" />
    <circle cx="1300" cy="600" r="4" />
    <circle cx="1300" cy="700" r="4" />
    <circle cx="1300" cy="800" r="4" />
  </g>
</svg> 