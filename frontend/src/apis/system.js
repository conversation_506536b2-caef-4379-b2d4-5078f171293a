import baseAPI from './base'

export async function systemAPI(action, parameters) {
    return await baseAPI(`system__${action}`, parameters)
}

export async function checkLicenseStatusAPI() {
    return await systemAPI('check_license_status')
}

export async function getMachineCodeAPI() {
    return await systemAPI('get_machine_code')
}

export async function verifyLicenseAPI(licensePath) {
    return await systemAPI('verify_license', licensePath)
}

export async function getModelStatusAPI() {
    return await systemAPI('get_model_status')
}

export async function downloadModelAPI(modelName) {
    return await systemAPI('download_model', modelName)
}

export async function downloadAllModelsAPI() {
    return await systemAPI('download_all_models')
}

export async function updateModelPathAPI(path) {
    return await systemAPI('update_model_path', path)
}

export async function importModelAPI(modelName, filePath) {
    return await systemAPI('import_model', {'model_name': modelName, 'file_path':filePath})
}

export async function removeModelAPI(modelName) {
    return await systemAPI('remove_model', modelName)
}

export async function loginAPI(email, password) {
    return await systemAPI('login', {'email': email, 'password': password})
}

export async function logoutAPI() {
    return await systemAPI('logout')
}

export async function registerAPI(email, password, inviteCode) {
    return await systemAPI('register', {'email': email, 'password': password, 'invite_code': inviteCode})
}

export async function getUserInfoAPI() {
    return await systemAPI('get_user_info')
}


export async function getPlansAPI() {
    return await systemAPI('get_plans')
}

export async function getOrdersAPI(page, pageSize) {
    return await systemAPI('get_orders', {'page': page, 'page_size': pageSize})
}

export async function createOrderAPI(planId, payChannel) {
    return await systemAPI('create_order', {'plan_id': planId, 'pay_channel': payChannel})
}

export async function checkOrderStatusAPI(orderId) {
    return await systemAPI('check_order_status', {'order_id': orderId})
}

export async function releaseModelsMemoryAPI() {
    return await systemAPI('release_models_memory')
}
