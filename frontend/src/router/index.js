import { createRouter, createWebHashHistory } from 'vue-router'
import { checkIsMember } from '@/utils/permission'
import { isLoggedIn } from '@/utils/auth'

const routes = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/user',
    component: () => import('@/layouts/UserLayout.vue'),
    children: [
      {
        path: 'login',
        name: 'Login',
        component: () => import('@/views/user/Login.vue'),
        meta: {
          requiresLicense: false,
          requiresAuth: false
        }
      },
      {
        path: 'register',
        name: 'Register',
        component: () => import('@/views/user/Register.vue'),
        meta: {
          requiresLicense: false,
          requiresAuth: false
        }
      }
    ]
  },
  {
    path: '/profile',
    component: () => import('@/layouts/ProfileLayout.vue'),
    meta: { 
      requiresAuth: true,
      requiresLicense: false
    },
    children: [
      {
        path: '',
        redirect: '/profile/info'
      },
      {
        path: 'info',
        name: 'ProfileInfo',
        component: () => import('@/views/profile/ProfileInfo.vue')
      },
      {
        path: 'subscription',
        name: 'Subscription',
        component: () => import('@/views/profile/Subscription.vue')
      },
      {
        path: 'history',
        name: 'SubscriptionHistory',
        component: () => import('@/views/profile/SubscriptionHistory.vue')
      }
    ]
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/home/<USER>'),
    meta: {
      requiresLicense: false
    }
  },
  {
    path: '/license',
    name: 'License',
    component: () => import('@/views/license/index.vue'),
    meta: {
      requiresLicense: false,
      requiresAuth: true,
    }
  },
  {
    path: '/video',
    component: () => import('@/views/video/index.vue'),
    meta: {
      requiresLicense: true,
      requiresAuth: true
    },
    children: [
      {
        path: '',
        redirect: '/video/manual'
      },
      {
        path: 'manual',
        name: 'VideoManual',
        component: () => import('@/views/video/components/VideoDeWatermark.vue')
      },
      {
        path: 'auto',
        name: 'VideoAuto',
        component: () => import('@/views/video/components/VideoAuto.vue')
      }
    ]
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/settings/index.vue'),
    meta: {
      requiresLicense: true,
      requiresAuth: true
    }
  },
  {
    path: '/matting',
    component: () => import('@/views/matting/index.vue'),
    meta: {
      requiresLicense: true,
      requiresAuth: true
    },
    children: [
      {
        path: '',
        redirect: '/matting/single'
      },
      {
        path: 'single',
        name: 'SingleMatting',
        component: () => import('@/views/matting/components/SingleMatting.vue')
      },
      {
        path: 'batch',
        name: 'BatchMatting',
        component: () => import('@/views/matting/components/BatchMatting.vue')
      }
    ]
  },
  {
    path: '/inpainting',
    component: () => import('@/views/inpainting/index.vue'),
    meta: {
      requiresLicense: true,
      requiresAuth: true
    },
    children: [
      {
        path: '',
        redirect: '/inpainting/single'
      },
      {
        path: 'single',
        name: 'SingleInpainting',
        component: () => import('@/views/inpainting/components/SingleInpainting.vue')
      },
      {
        path: 'batch',
        name: 'BatchInpainting',
        component: () => import('@/views/inpainting/components/BatchInpainting.vue')
      }
    ]
  },
  {
    path: '/beautify',
    name: 'Beautify',
    component: () => import('@/views/beautify/index.vue'),
    meta: {
      requiresLicense: true,
      requiresAuth: true
    }
  },
  {
    path: '/ocr',
    component: () => import('@/views/ocr/index.vue'),
    meta: {
      requiresLicense: true,
      requiresAuth: true
    },
    children: [
      {
        path: '',
        redirect: '/ocr/single'
      },
      {
        path: 'single',
        name: 'SingleOcr',
        component: () => import('@/views/ocr/components/SingleOcr.vue')
      },
      {
        path: 'batch',
        name: 'BatchOcr',
        component: () => import('@/views/ocr/components/BatchOcr.vue')
      }
    ],
  },
  {
    path: '/screenshot',
    component: () => import('@/views/screenshot/index.vue'),
    meta: {
      requiresLicense: true,
      requiresAuth: true,
    }
  },
  {
    path: '/matting_video',
    component: () => import('@/views/matting_vidoe/index.vue'),
    meta: {
      requiresLicense: true,
      requiresAuth: true,
    },
    children: [
      {
        path: '',
        redirect: '/matting_video/single'
      },
      {
        path: 'single',
        name: 'SingleMattingVideo',
        component: () => import('@/views/matting_vidoe/components/SingleMattingVideo.vue')
      },
      {
        path: 'batch',
        name: 'BatchMattingVideo',
        component: () => import('@/views/matting_vidoe/components/BatchMattingVideo.vue')
      }
    ]
  },
  {
    path: '/convert_img',
    component: () => import('@/views/convert_img/index.vue'),
    meta: {
      requiresLicense: true,
      requiresAuth: true,
    },
    children: [
      {
        path: '',
        redirect: '/convert_img/single'
      },
      {
        path: 'single',
        name: 'SingleConvert',
        component: () => import('@/views/convert_img/components/SingleConvert.vue')
      },
      {
        path: 'batch',
        name: 'BatchConvert',
        component: () => import('@/views/convert_img/components/BatchConvert.vue')
      }
    ]
  },
  {
    path: '/real_image',
    component: () => import('@/views/real_image/index.vue'),
    meta: {
      requiresLicense: true,
      requiresAuth: true,
    },
    children: [
      {
        path: '',
        redirect: '/real_image/single'
      },
      {
        path: 'single',
        name: 'SingleRealImage',
        component: () => import('@/views/real_image/components/SingleRealImage.vue')
      },
      {
        path: 'batch',
        name: 'BatchRealImage',
        component: () => import('@/views/real_image/components/BatchRealImage.vue')
      }
    ]
  },
  {
    path: '/smart_video_split',
    component: () => import('@/views/smart_video_split/index.vue'),
    meta: {
      requiresLicense: true,
      requiresAuth: true,
    },
    children: [
      {
        path: '',
        redirect: '/smart_video_split/single'
      },
      {
        path: 'single',
        name: 'SingleSmartVideoSplit',
        component: () => import('@/views/smart_video_split/components/SingleSmartVideoSplit.vue')
      },
      {
        path: 'batch',
        name: 'BatchSmartVideoSplit',
        component: () => import('@/views/smart_video_split/components/BatchSmartVideoSplit.vue')
      }
    ]
  },{
    path: '/pintu',
    component: () => import('@/views/pintu/index.vue'),
    meta: {
      requiresLicense: false,
      requiresAuth: false,
    }
  },
  {
    path: '/compress',
    component: () => import('@/views/compress/index.vue'),
    meta: {
      requiresLicense: true,
      requiresAuth: true,
    },
    children: [
      {
        path: '',
        redirect: '/compress/single'
      },
      {
        path: 'single',
        name: 'SingleCompress',
        component: () => import('@/views/compress/components/SingleCompress.vue')
      },
      {
        path: 'batch',
        name: 'BatchCompress',
        component: () => import('@/views/compress/components/BatchCompress.vue')
      }
    ]
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})


router.beforeEach(async (to, from, next) => {

  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isLoggedIn()) {
      next('/user/login')
      return
    }
  }
 
  if (to.meta.requiresLicense) {
    const isMember = await checkIsMember()
    if (!isMember) {
      next('/profile/subscription')
      return
    }
  }

  // if (to.meta.requiresLicense) {
  //   const hasValidLicense = await checkLicenseStatus()
  //   if (!hasValidLicense) {
  //     next('/license')
  //     return
  //   }
  // }
  
  next()
})

export default router