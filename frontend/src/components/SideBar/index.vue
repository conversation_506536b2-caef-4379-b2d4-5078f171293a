<template>
  <el-aside :width="isCollapse ? '80px' : '180px'" class="aside">
    <Logo />
    <NavMenu :is-collapse="isCollapse" />
    <CollapseButton :is-collapse="isCollapse" @toggle="toggleCollapse" />
  </el-aside>
</template>

<script setup>
import { ref } from 'vue'
import Logo from './Logo.vue'
import NavMenu from './NavMenu.vue'
import CollapseButton from './CollapseButton.vue'

const isCollapse = ref(false)

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style scoped>
.aside {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-right: 1px solid var(--el-border-color-light);
}
</style> 