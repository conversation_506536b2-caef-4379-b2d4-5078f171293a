<template>
  <el-menu
    :default-active="activeMenu"
    class="el-menu-vertical"
    :collapse="isCollapse"
  >
    <el-menu-item index="/home" @click="$router.push('/home')">
      <el-icon><HomeFilled /></el-icon>
      <template #title>首页</template>
    </el-menu-item>
    
     <!-- AI视频抠图 -->
     <el-menu-item index="/matting_video" @click="$router.push('/matting_video')">
      <el-icon><Film /></el-icon>
      <template #title>视频抠图</template>
    </el-menu-item>

    <el-menu-item index="/video" @click="$router.push('/video')">
      <el-icon><VideoCameraFilled /></el-icon>
      <template #title>视频去水印</template>
    </el-menu-item>
    
    <el-menu-item index="/screenshot" @click="$router.push('/screenshot')">
      <el-icon><Picture /></el-icon>
      <template #title>截图美化</template>
    </el-menu-item>
    
    <!-- AI抠图 -->
    <el-menu-item index="/matting" @click="$router.push('/matting')">
      <el-icon><PictureFilled /></el-icon>
      <template #title>AI抠图</template>
    </el-menu-item>

    <!-- AI智能擦除 -->
    <el-menu-item index="/inpainting" @click="$router.push('/inpainting')">
      <el-icon><EditPen /></el-icon>
      <template #title>智能擦除</template>
    </el-menu-item>

    <!-- AI智能OCR -->
    <el-menu-item index="/ocr" @click="$router.push('/ocr')">
      <el-icon><Document /></el-icon>
      <template #title>智能OCR</template>
    </el-menu-item>

    <!-- 在适当位置添加以下菜单项 -->
    <el-menu-item index="/convert_img" @click="$router.push('/convert_img')">
      <el-icon><Switch /></el-icon>
      <template #title>图片格式转换</template>
    </el-menu-item>

    <el-menu-item index="/compress" @click="$router.push('/compress')">
      <el-icon>
        <picture-rounded />
      </el-icon>
      <template #title>图片压缩</template>
    </el-menu-item>

    <el-menu-item index="/real_image" @click="$router.push('/real_image')">
      <el-icon><PictureFilled /></el-icon>
      <template #title>图片高清放大修复</template>
    </el-menu-item>

    <el-menu-item index="/smart_video_split" @click="$router.push('/smart_video_split')">
      <el-icon><VideoPlay /></el-icon>
      <template #title>智能镜头分割</template>
    </el-menu-item>
    <el-menu-item index="/pintu" @click="$router.push('/pintu')">
      <el-icon><grid /></el-icon>
      <template #title>
        <span>免费拼图</span>
        <el-tag size="small" type="success" class="free-tag">FREE</el-tag>
      </template>
    </el-menu-item>

    <el-menu-item index="/settings" @click="$router.push('/settings')">
      <el-icon><Setting /></el-icon>
      <template #title>设置</template>
    </el-menu-item>
    
    <!-- <el-menu-item index="/license" @click="$router.push('/license')">
      <el-icon><Lock /></el-icon>
      <template #title>授权</template>
    </el-menu-item> -->

    <!-- 用户登录/注册/个人中心 -->
    <el-menu-item 
      :index="userLoggedIn ? '/profile' : '/user/login'"
      @click="$router.push(userLoggedIn ? '/profile/info' : '/user/login')"
    >
      <el-icon><User /></el-icon>
      <template #title>{{ userLoggedIn ? '个人中心' : '登录/注册' }}</template>
    </el-menu-item>
  </el-menu>
</template>

<script setup>
import { computed, ref, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  HomeFilled, 
  VideoCameraFilled, 
  Picture, 
  PictureFilled, 
  EditPen, 
  Document, 
  Setting, 
  Lock, 
  Film,
  Switch,
  User
} from '@element-plus/icons-vue'
import { isLoggedIn } from '@/utils/auth'
import { getUserInfoAPI } from '@/apis/system'
import { setUserInfoSession } from '@/utils/auth'

defineProps({
  isCollapse: {
    type: Boolean,
    required: true
  }
})

const route = useRoute()
const userLoggedIn = ref(isLoggedIn())
// Update login status when route changes
watch(() => route.path, () => {
  userLoggedIn.value = isLoggedIn()
})


onMounted(async   () => {
  await setUserInfoSession(getUserInfoAPI)
  userLoggedIn.value = isLoggedIn()
})

const activeMenu = computed(() => {
  const path = route.path
  if (path.startsWith('/profile')) {
    return '/profile'
  }
  // 提取主路径，忽略子路由
  return '/' + path.split('/')[1]
})
</script>

<style scoped>
.el-menu-vertical {
  border-right: none;
  flex: 1;
}

:deep(.el-menu-item) {
  height: 45px;  /* 默认是56px，改小一些 */
  line-height: 45px;
  padding: 0 16px !important;
}

:deep(.el-menu--collapse) .el-menu-item {
  padding: 0 13px !important;
}

.free-tag {
  margin-left: 8px;
  font-size: 10px;
  height: 16px;
  line-height: 14px;
  padding: 0 4px;
}
</style> 