<template>
  <div class="collapse-container">
    <el-button
      type="text"
      @click="$emit('toggle')"
      class="collapse-btn"
    >
      <el-icon :size="20">
        <component :is="isCollapse ? 'Expand' : 'Fold'" />
      </el-icon>
    </el-button>
  </div>
</template>

<script setup>
import { Fold, Expand } from '@element-plus/icons-vue'

defineProps({
  isCollapse: {
    type: Boolean,
    required: true
  }
})

defineEmits(['toggle'])
</script>

<style scoped>
.collapse-container {
  padding: 12px 8px;
  display: flex;
  justify-content: center;
  border-top: 1px solid var(--el-border-color-light);
}

.collapse-btn {
  width: 100%;
  display: flex;
  justify-content: center;
}
</style> 