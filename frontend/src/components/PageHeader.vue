<template>
  <div class="page-header">
    <div class="header-content">
      <div class="title-section">
        <div class="title-row">
          <h2 class="title">{{ title }}</h2>
          <el-button
            type="danger"
            size="small"
            @click="handleReleaseMemory"
            :loading="releasing"
            class="release-btn"
          >
            <el-icon><Delete /></el-icon>
            释放内存
          </el-button>
        </div>
        <div class="description">
          <span class="description-text">{{ description }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  releaseModelsMemoryAPI
} from '@/apis/system'
import message from '@/utils/message'

defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  tags: {
    type: Array,
    default: () => []
  }
})

import { ref } from 'vue'

const releasing = ref(false)

// 释放模型内存
const handleReleaseMemory = async () => {
  if (releasing.value) return

  releasing.value = true
  try {
    const res = await releaseModelsMemoryAPI()
    if (res.code === 200) {
      message.success('模型内存释放成功')
    } else {
      message.error(res.msg || '释放失败')
    }
  } catch (error) {
    console.log(error)
    message.error('释放失败')
  } finally {
    releasing.value = false
  }
}

</script>

<style scoped lang="scss">
.page-header {
  margin-bottom: 24px;

  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    .title-section {
      .title-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .title {
          margin: 0;
          font-size: 24px;
          font-weight: 600;
          color: var(--el-text-color-primary);
          flex: 1;
        }

        .release-btn {
          margin-left: auto;
          border-radius: 6px;
          font-size: 12px;
          padding: 8px 16px;
          transition: all 0.3s ease;

          .el-icon {
            margin-right: 4px;
          }

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(245, 108, 108, 0.3);
          }

          &.is-loading {
            pointer-events: none;
          }
        }
      }

      .description {
        text-align: left;

        .description-text {
          font-size: 14px;
          color: var(--el-text-color-secondary);
          line-height: 1.5;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    .header-content {
      padding: 0 16px;

      .title-section {
        .title-row {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;

          .title {
            font-size: 20px;
          }

          .release-btn {
            margin-left: 0;
            align-self: flex-end;
          }
        }
      }
    }
  }
}
</style>
