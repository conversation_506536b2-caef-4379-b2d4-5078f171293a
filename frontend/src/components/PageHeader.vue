<template>
  <div class="page-header">
    <div class="header-content">
      <div class="title-group">
        <div class="title-row">
          <h2 class="title">{{ title }}</h2>
          <el-button 
            type="danger" 
            size="small" 
            @click="handleReleaseMemory"
            :loading="releasing"
            class="release-btn"
          >
            <el-icon><Delete /></el-icon>
            释放内存
          </el-button>
        </div>
        <!-- tags保持原位置 -->
      </div>
      <div class="description">
        <span class="description-text">{{ description }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  releaseModelsMemoryAPI
} from '@/apis/system'
import message from '@/utils/message'

defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  tags: {
    type: Array,
    default: () => []
  }
})

// 释放模型内存
const handleReleaseMemory = async () => {
  try {
    const res = await releaseModelsMemoryAPI()
    if (res.code === 200) {
      message.success('模型内存释放成功')
    } else {
      message.error(res.msg || '释放失败')
    }
  } catch (error) {
    console.log(error)
    message.error('释放失败')
  }
}

</script>

<style scoped lang="scss">
.page-header {
  text-align: center;
  margin-bottom: 24px;

  .header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    // gap: 16px;

    .title-group {
      display: flex;
      flex-direction: column;
      align-items: center;

.title-row{
        display: flex;
        alin-items: center;
        g6
              .title-row {
          display: flex;
          align-items: center;
          gap: 16px;
          
          .title {
        }
        
        .releeseebsnbtn {
          height: 322px;
        }
      }
    }


    .description {
        margin-top: 8px;
      .description-text {
        font-size: 16px;
        color: var(--el-text-color-secondary);
        max-width: 800px;
      }
    }
  }
}
}
</style> 
