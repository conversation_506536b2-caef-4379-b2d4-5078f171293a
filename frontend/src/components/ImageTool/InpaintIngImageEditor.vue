<template>
    <div class="editor-container" v-loading="loading">
      <div class="canvas-area">
        <div class="canvas-wrapper" 
             @mouseleave="handleMouseLeave"
             @mouseenter="showBrushPreview = true">
          <canvas ref="imageCanvas" class="base-canvas"></canvas>
          <canvas ref="maskCanvas" class="mask-canvas"
            @mousedown="startDrawing"
            @mousemove="handleMouseMove"
            @mouseup="stopDrawing"
            @mouseleave="stopDrawing"
          ></canvas>
          <!-- Brush preview -->
          <div v-if="showBrushPreview" 
            class="brush-preview"
            :style="{
              width: `${brushSize}px`,
              height: `${brushSize}px`,
              left: `${cursorX}px`,
              top: `${cursorY}px`,
            }">
          </div>
        </div>
      </div>
  
      <!-- Toolbar -->
      <div class="toolbar-container">
        <div class="toolbar">
          <el-button-group class="action-group">
            <el-button 
              @click="undo" 
              :icon="RefreshLeft"
              class="action-button"
              :disabled="historyIndex <= 0"
            />
            <el-button 
              @click="redo" 
              :icon="RefreshRight"
              class="action-button"
              :disabled="historyIndex >= history.length - 1"
            />
          </el-button-group>
  
          <div class="brush-size">
            <el-slider
              v-model="brushSize"
              :min="1"
              :max="200"
              :show-tooltip="true"
              class="brush-slider"
            />
          </div>
  
          <el-button-group class="action-group">
            <el-button 
              @click="clearMask" 
              :icon="Delete"
              class="action-button"
            />
            <el-button 
              @click="saveMask" 
              :icon="Check"
              class="action-button save-button"
              :loading="loading"
            />
          </el-button-group>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import { RefreshLeft, RefreshRight, Delete, Check } from '@element-plus/icons-vue';
  
  const props = defineProps({
    imageBase64: {
      type: String,
      required: true
    }
  });
  
  const emit = defineEmits(['save']);
  
  // Canvas refs
  const imageCanvas = ref(null);
  const maskCanvas = ref(null);
  const originalImage = ref(null);
  const loading = ref(false);
  
  // Brush state
  const brushSize = ref(40);
  const isDrawing = ref(false);
  const showBrushPreview = ref(false);
  const cursorX = ref(0);
  const cursorY = ref(0);
  
  // History state
  const history = ref([]);
  const historyIndex = ref(-1);
  const imageHistory = ref([]);
  
  // Original dimensions
  const originalWidth = ref(0);
  const originalHeight = ref(0);
  
  // Initialize canvas with image
  const initCanvas = (img) => {
    if (!img || !imageCanvas.value || !maskCanvas.value) return;
  
    originalImage.value = img;
    originalWidth.value = img.width;
    originalHeight.value = img.height;
    
    // Calculate canvas size
    const maxWidth = 700;
    const maxHeight = 500;
    const scale = Math.min(maxWidth / img.width, maxHeight / img.height);
    const width = img.width * scale;
    const height = img.height * scale;
  
    // Set canvas dimensions
    [imageCanvas.value, maskCanvas.value].forEach(canvas => {
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';
    });
  
    // Draw original image
    renderImage(img);
    
    // Setup mask canvas
    const maskCtx = maskCanvas.value.getContext('2d');
    maskCtx.fillStyle = 'rgba(255, 0, 0, 0.5)';
    
    saveState();
  };
  
  // Mouse event handlers
  const handleMouseMove = (event) => {
    updateCursor(event);
    if (isDrawing.value) {
      draw(event);
    }
  };
  
  const updateCursor = (event) => {
    const rect = event.target.getBoundingClientRect();
    cursorX.value = event.clientX - rect.left;
    cursorY.value = event.clientY - rect.top;
  };
  
  const handleMouseLeave = () => {
    showBrushPreview.value = false;
    stopDrawing();
  };
  
  // Drawing functions
  const startDrawing = (event) => {
    isDrawing.value = true;
    draw(event);
  };
  
  const draw = (event) => {
    const ctx = maskCanvas.value.getContext('2d');
    const rect = event.target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    ctx.beginPath();
    ctx.arc(x, y, brushSize.value/2, 0, Math.PI * 2);
    ctx.fill();
  };
  
  const stopDrawing = () => {
    if (isDrawing.value) {
      isDrawing.value = false;
      saveState();
    }
  };
  
  // History management
  const saveState = () => {
    const maskData = maskCanvas.value.getContext('2d').getImageData(
      0, 0, maskCanvas.value.width, maskCanvas.value.height
    );
    const imageData = imageCanvas.value.getContext('2d').getImageData(
      0, 0, imageCanvas.value.width, imageCanvas.value.height
    );
    
    history.value = history.value.slice(0, historyIndex.value + 1);
    imageHistory.value = imageHistory.value.slice(0, historyIndex.value + 1);
    
    history.value.push(maskData);
    imageHistory.value.push(imageData);
    historyIndex.value++;
  };
  
  const undo = () => {
    if (historyIndex.value > 0) {
      historyIndex.value--;
      const maskCtx = maskCanvas.value.getContext('2d');
      const imageCtx = imageCanvas.value.getContext('2d');
      
      maskCtx.putImageData(history.value[historyIndex.value], 0, 0);
      imageCtx.putImageData(imageHistory.value[historyIndex.value], 0, 0);
    }
  };
  
  const redo = () => {
    if (historyIndex.value < history.value.length - 1) {
      historyIndex.value++;
      const maskCtx = maskCanvas.value.getContext('2d');
      const imageCtx = imageCanvas.value.getContext('2d');
      
      maskCtx.putImageData(history.value[historyIndex.value], 0, 0);
      imageCtx.putImageData(imageHistory.value[historyIndex.value], 0, 0);
    }
  };
  
  // Image operations
  const renderImage = (img) => {
    const ctx = imageCanvas.value.getContext('2d');
    ctx.clearRect(0, 0, imageCanvas.value.width, imageCanvas.value.height);
    ctx.drawImage(img, 0, 0, imageCanvas.value.width, imageCanvas.value.height);
  };
  
  const clearMask = () => {
    const ctx = maskCanvas.value.getContext('2d');
    ctx.clearRect(0, 0, maskCanvas.value.width, maskCanvas.value.height);
    saveState();
  };
  
  const saveMask = () => {
    // Create temporary canvas for original size mask
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = originalWidth.value;
    tempCanvas.height = originalHeight.value;
    const tempCtx = tempCanvas.getContext('2d');
    
    tempCtx.imageSmoothingEnabled = true;
    tempCtx.imageSmoothingQuality = 'high';
    
    // Scale current mask to original size
    tempCtx.drawImage(
      maskCanvas.value,
      0, 0, maskCanvas.value.width, maskCanvas.value.height,
      0, 0, originalWidth.value, originalHeight.value
    );
  
    // Emit the mask and original image
    emit('save', {
      maskBase64: tempCanvas.toDataURL('image/png', 1.0),
      originalBase64: props.imageBase64
    });
  };
  
  // Keyboard event handler
  const handleKeyboard = (event) => {
    if ((event.ctrlKey || event.metaKey) && !event.shiftKey && event.key === 'z') {
      event.preventDefault();
      undo();
    }
    
    if ((event.ctrlKey || event.metaKey) && 
        ((event.shiftKey && event.key === 'z') || event.key === 'y')) {
      event.preventDefault();
      redo();
    }
  };
  
  // Lifecycle hooks
  onMounted(() => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => initCanvas(img);
    img.src = props.imageBase64;
    
    window.addEventListener('keydown', handleKeyboard);
  });
  
  onUnmounted(() => {
    window.removeEventListener('keydown', handleKeyboard);
  });
  
  // 暴露 saveMask 方法给父组件
  defineExpose({
    saveMask
  });
  </script>
  
  <style scoped>
  .editor-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
  }
  
  .canvas-area {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    overflow: hidden;
  }
  
  .canvas-wrapper {
    position: relative;
    display: inline-block;
    border-radius: 4px;
  }
  
  .base-canvas {
    display: block;
    background-color: white;
  }
  
  .mask-canvas {
    position: absolute;
    top: 0;
    left: 0;
    cursor: crosshair;
  }
  
  .brush-preview {
    position: absolute;
    border: 2px solid #fff;
    border-radius: 50%;
    pointer-events: none;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.3);
    background-color: rgba(255, 0, 0, 0.1);
    z-index: 10;
  }
  
  .toolbar-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  
  .toolbar {
    display: flex;
    align-items: center;
    gap: 12px;
    background: #666;
    border-radius: 4px;
    padding: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  }
  
  .brush-size {
    width: 200px;
    margin: 0 4px;
  }
  
  .brush-slider {
    margin: 0;
  }
  
  .brush-slider :deep(.el-slider__runway) {
    height: 2px;
    margin: 14px 0;
    background-color: rgba(255, 255, 255, 0.2);
  }
  
  .brush-slider :deep(.el-slider__bar) {
    height: 2px;
    background-color: #fff;
  }
  
  .brush-slider :deep(.el-slider__button-wrapper) {
    width: 12px;
    height: 12px;
    top: 2px;
  }
  
  .brush-slider :deep(.el-slider__button) {
    width: 12px;
    height: 12px;
    background-color: #fff;
    border: none;
  }
  
  .action-group {
    display: flex;
  }
  
  .action-button {
    width: 32px !important;
    height: 32px !important;
    padding: 6px !important;
    border: none !important;
    background: transparent !important;
  }
  
  .action-button:hover {
    background: rgba(255, 255, 255, 0.1) !important;
  }
  
  .action-button :deep(.el-icon) {
    color: #fff;
    font-size: 14px;
  }
  
  .action-button.save-button {
    background: transparent !important;
  }
  
  .action-button.save-button:hover {
    background: rgba(255, 255, 255, 0.1) !important;
  }
  
  .action-button:disabled {
    opacity: 0.35;
  }
  
  :deep(.el-loading-mask) {
    background-color: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(5px);
  }
  </style>
  