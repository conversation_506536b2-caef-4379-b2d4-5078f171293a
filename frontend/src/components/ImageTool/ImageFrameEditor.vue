<template>
  <div class="image-frame-editor">
    <!-- 左侧画布区域 -->
    <div class="canvas-area">
      <div class="canvas-container"
           ref="canvasContainerRef"
           :style="containerStyle"
           :class="backgroundClass">
        <!-- 内容容器 -->
        <div class="content-wrapper" :style="contentWrapperStyle">
          <!-- 系统标题栏 -->
          <div class="window-titlebar" 
               :class="[frameStyle.toLowerCase().split(' ').join('-')]" 
               :style="titlebarStyle">
            <div class="window-controls">
              <div class="control-button minimize"></div>
              <div class="control-button maximize"></div>
              <div class="control-button close"></div>
            </div>
          </div>
          <!-- 内容区域 -->
          <div class="system-frame" :style="systemFrameStyle">
            <div class="image-container">
              <img v-if="imageUrl" :src="imageUrl" alt="Preview" class="preview-image">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧属性设置面板 -->
    <div class="settings-panel">
      <!-- 添加纵横比设置 -->
      <div class="setting-section">
        <div class="section-title">外框纵横比</div>
        <el-select v-model="containerAspectRatio" class="aspect-ratio-select">
          <el-option label="原始比例" value="original" />
          <el-option label="1:1 正方形" value="1:1" />
          <el-option label="4:3 标准" value="4:3" />
          <el-option label="16:9 宽屏" value="16:9" />
          <el-option label="9:16 竖屏" value="9:16" />
          <el-option label="3:2 照片" value="3:2" />
        </el-select>
      </div>

      <!-- 外边距设置 -->
      <div class="setting-section">
        <div class="section-title">外边距</div>
        <el-slider v-model="outerPadding" :min="0" :max="100" :step="1" />
      </div>

      <!-- 内边距设置 -->
      <div class="setting-section">
        <div class="section-title">内边距</div>
        <el-slider v-model="innerPadding" :min="0" :max="100" :step="1" />
      </div>

      <!-- 圆角设置 -->
      <div class="setting-section">
        <div class="section-title">圆角弧度</div>
        <el-slider v-model="borderRadius" :min="0" :max="36" :step="1" />
      </div>

      <!-- 阴影设置 -->
      <div class="setting-section">
        <div class="section-title">阴影</div>
        <el-slider v-model="shadowSize" :min="0" :max="100" :step="1" />
      </div>

      <!-- 边框样式选择 -->
      <div class="setting-section">
        <div class="section-title">边框样式</div>
        <el-select v-model="frameStyle" class="frame-style-select">
          <el-option label="macOS Light" value="macOS Light" />
          <el-option label="macOS Dark" value="macOS Dark" />
          <el-option label="Windows 11 Light" value="Windows 11 Light" />
          <el-option label="Windows 11 Dark" value="Windows 11 Dark" />
          <el-option label="无边框" value="none" />
        </el-select>
      </div>

      <!-- 背景设置 -->
      <div class="setting-section">
        <div class="section-header">
          <div class="section-title">背景</div>
          <el-button link type="primary" @click="showMoreColors = true">更多 ></el-button>
        </div>
        
        <div class="background-options">
          <!-- 快捷颜色选择 -->
          <div class="quick-colors">
            <!-- 透明选项 -->
            <!-- <div class="color-item transparent"
                 :class="{ active: backgroundType === 'transparent' }"
                 @click="setBackground('transparent')">
              <el-icon><Close /></el-icon>
            </div> -->
            <!-- 预设颜色 -->
            <div v-for="color in quickColors" 
                 :key="color"
                 class="color-item"
                 :class="{ active: backgroundColor === color && backgroundType === 'color' }"
                 :style="{ background: color }"
                 @click="setBackground('color', color)">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加颜色选择弹窗 -->
    <el-drawer
      v-model="showMoreColors"
      title="选择背景"
      :direction="rtl"
      size="300px"
      :with-header="true"
      :modal="false"
      class="color-drawer">
      <div class="color-tabs">
        <el-tabs v-model="activeColorTab">
          <el-tab-pane label="纯色" name="solid">
            <div class="color-grid">
              <!-- 透明选项 -->
              <div class="color-grid-item transparent"
                   :class="{ active: backgroundType === 'transparent' }"
                   @click="setBackground('transparent')">
                <el-icon><Close /></el-icon>
              </div>
              <!-- 纯色选项 -->
              <div v-for="color in solidColors" 
                   :key="color"
                   class="color-grid-item"
                   :class="{ active: backgroundColor === color && backgroundType === 'color' }"
                   :style="{ background: color }"
                   @click="setBackground('color', color)">
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="渐变" name="gradient">
            <div class="color-grid">
              <!-- 渐变选项 -->
              <div v-for="gradient in gradientColors" 
                   :key="gradient.value"
                   class="color-grid-item"
                   :class="{ active: gradientStyle === gradient.value && backgroundType === 'gradient' }"
                   :style="{ background: gradient.value }"
                   @click="setBackground('gradient', gradient.value)">
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { gradientColors } from '@/config/colors'
import html2canvas from 'html2canvas'

const props = defineProps({
  imageUrl: {
    type: String,
    required: true
  }
})

const canvasContainerRef = ref(null)
const imageAspectRatio = ref(1)
const containerSize = ref({ width: 0, height: 0 })

// 添加容器纵横比设置
const containerAspectRatio = ref('original')

// 状态定义
const outerPadding = ref(36)
const innerPadding = ref(20)
const borderRadius = ref(9)
const shadowSize = ref(36)
const frameStyle = ref('macOS Light')
const backgroundType = ref('color')
const backgroundColor = ref('#FFA500')
const gradientStyle = ref('linear-gradient(45deg, #FF512F, #DD2476)')
const showMoreColors = ref(false)
const activeColorTab = ref('solid')

// 快捷颜色
const quickColors = [
  '#FFA500',
  '#FFE4E4',
  '#E8F5E9',
  '#E3F2FD',
  '#87CEEB',
]

// 将颜色数据拆分为纯色和渐变
const solidColors = [
  '#FFFFFF',
  '#F5F5F5',
  '#FFE4E4',
  '#E8F5E9',
  '#E3F2FD',
  '#FCE4EC',
  '#FFF3E0',
  '#FAFAFA',
  '#F5F5F5',
  '#EEEEEE',
  '#E0E0E0',
]

// 快捷渐变
const quickGradients = gradientColors.slice(0, 6)

// 解析所选纵横比为数值
const parsedContainerAspectRatio = computed(() => {
  if (containerAspectRatio.value === 'original') {
    return imageAspectRatio.value
  }
  
  const parts = containerAspectRatio.value.split(':')
  if (parts.length === 2) {
    return parseInt(parts[0], 10) / parseInt(parts[1], 10)
  }
  
  return imageAspectRatio.value
})

// 监听图片加载以获取宽高比
watch(() => props.imageUrl, () => {
  if (props.imageUrl) {
    const img = new Image()
    img.onload = () => {
      imageAspectRatio.value = img.width / img.height
      if (containerAspectRatio.value === 'original') {
        updateContainerSize()
      }
    }
    img.src = props.imageUrl
  }
})

// 监听容器纵横比变化
watch(containerAspectRatio, () => {
  updateContainerSize()
})

// 监听窗口大小变化
onMounted(() => {
  window.addEventListener('resize', updateContainerSize)
  updateContainerSize()
  
  return () => {
    window.removeEventListener('resize', updateContainerSize)
  }
})

// 更新容器尺寸
const updateContainerSize = () => {
  if (!canvasContainerRef.value) return
  
  const container = canvasContainerRef.value
  const parentWidth = container.parentElement.clientWidth
  const parentHeight = container.parentElement.clientHeight
  const padding = outerPadding.value * 2
  
  // 使用计算好的纵横比
  const aspectRatio = parsedContainerAspectRatio.value
  
  let width = parentWidth - padding
  let height = width / aspectRatio
  
  if (height > parentHeight - padding) {
    height = parentHeight - padding
    width = height * aspectRatio
  }
  
  containerSize.value = { width, height }
}

// 计算样式
const containerStyle = computed(() => {
  let style = {
    padding: `${outerPadding.value}px`,
    width: `${containerSize.value.width}px`,
    height: `${containerSize.value.height}px`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  }

  if (backgroundType.value === 'color') {
    style.backgroundColor = backgroundColor.value
  } else if (backgroundType.value === 'gradient') {
    style.background = gradientStyle.value
  }

  return style
})

const backgroundClass = computed(() => {
  return backgroundType.value === 'transparent' ? 'checkerboard-background' : ''
})

// 计算内容包装器的样式 - 这是新增的关键部分
const contentWrapperStyle = computed(() => {
  const titlebarHeight = frameStyle.value === 'none' ? 0 : 28
  
  // 计算内容区域应该使用的最大尺寸
  const maxWidth = containerSize.value.width - (outerPadding.value * 2)
  const maxHeight = containerSize.value.height - (outerPadding.value * 2)
  
  // 保持图片原始比例
  let width, height
  
  if (containerAspectRatio.value === 'original') {
    // 如果是原始比例，直接使用最大空间
    width = maxWidth
    height = maxHeight
  } else {
    // 否则，基于图片比例计算最佳适应尺寸
    const contentAspectRatio = imageAspectRatio.value
    
    width = maxWidth
    height = width / contentAspectRatio + titlebarHeight
    
    if (height > maxHeight) {
      height = maxHeight
      width = (height - titlebarHeight) * contentAspectRatio
    }
  }
  
  return {
    width: `${width}px`,
    height: `${height}px`,
    position: 'relative',
    overflow: 'hidden'
  }
})

// 添加窗口样式计算
const titlebarStyle = computed(() => {
  const style = {
    borderRadius: `${borderRadius.value}px ${borderRadius.value}px 0 0`,
    position: 'relative',
    height: '28px',
    marginBottom: '-1px',
    width: '100%'
  }

  if (frameStyle.value === 'none') {
    style.display = 'none'
  }

  return style
})

const systemFrameStyle = computed(() => {
  const style = {
    borderRadius: `${borderRadius.value}px`,
    borderTopLeftRadius: frameStyle.value === 'none' ? `${borderRadius.value}px` : 0,
    borderTopRightRadius: frameStyle.value === 'none' ? `${borderRadius.value}px` : 0,
    padding: `${innerPadding.value}px`,
    boxShadow: shadowSize.value > 0 ? `0 ${shadowSize.value/2}px ${shadowSize.value}px rgba(0, 0, 0, 0.1)` : 'none',
    backgroundColor: '#ffffff',
    border: '1px solid #e4e4e4',
    height: frameStyle.value === 'none' ? '100%' : 'calc(100% - 28px)', // 减去标题栏高度
    boxSizing: 'border-box',
    overflow: 'hidden'
  }

  return style
})

const outerFrameStyle = computed(() => ({
  borderRadius: `${borderRadius.value + 2}px`
}))

const imageContainerStyle = computed(() => {
  // 计算图片在容器中的位置和尺寸
  const availableWidth = containerSize.value.width - (innerPadding.value * 2) - 2 // 减去边框粗细
  const availableHeight = containerSize.value.height - 
    (frameStyle.value === 'none' ? 0 : 28) - // 标题栏高度
    (innerPadding.value * 2) - 2 // 减去内边距和边框粗细
  
  // 基于可用空间计算图片尺寸，保持原始纵横比
  let imgWidth = availableWidth
  let imgHeight = imgWidth / imageAspectRatio.value
  
  if (imgHeight > availableHeight) {
    imgHeight = availableHeight
    imgWidth = imgHeight * imageAspectRatio.value
  }
  
  return {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden'
  }
})

// 保留预加载图片功能
const preloadImages = async () => {
  return new Promise((resolve) => {
    const container = canvasContainerRef.value;
    if (!container) return resolve();
    
    const images = container.querySelectorAll('img');
    if (images.length === 0) return resolve();
    
    let loadedCount = 0;
    
    const checkAllLoaded = () => {
      loadedCount++;
      if (loadedCount === images.length) {
        resolve();
      }
    };
    
    images.forEach(img => {
      if (img.complete) {
        checkAllLoaded();
      } else {
        img.onload = checkAllLoaded;
        img.onerror = checkAllLoaded;
      }
    });
  });
};

const exportImage = async () => {
  try {
    if (!canvasContainerRef.value) {
      throw new Error('Canvas container not found');
    }
    
    // 确保所有图像已加载
    await preloadImages();
    
    // 使用html2canvas高质量捕获DOM
    const canvas = await html2canvas(canvasContainerRef.value, {
      backgroundColor: null,
      scale: 4, // 提高缩放倍率，从2提高到4以获得更高分辨率
      useCORS: true, // 处理跨域图片
      allowTaint: true,
      logging: false,
      imageTimeout: 0, // 不设置图片加载超时
      onclone: (document, element) => {
        // 确保克隆的元素内的图片完全加载，并设置正确的跨域属性
        const images = element.querySelectorAll('img');
        images.forEach(img => {
          if (!img.complete) {
            img.setAttribute('crossorigin', 'anonymous');
          }
        });
      }
    });
    
    // 转换为高质量PNG
    const imageData = canvas.toDataURL('image/png', 1.0);
    
    return imageData;
  } catch (error) {
    console.error('Error exporting image:', error);
    throw error;
  }
}

// 更新设置背景的方法
const setBackground = (type, value) => {
  backgroundType.value = type
  if (type === 'color') {
    backgroundColor.value = value
  } else if (type === 'gradient') {
    gradientStyle.value = value
  }
}

// 暴露方法给父组件
defineExpose({
  exportImage
})
</script>

<style scoped>
.image-frame-editor {
  display: flex;
  gap: 24px;
  height: 100%;
  padding: 20px;
}

.canvas-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: auto;
  padding: 20px;
}

.canvas-container {
  box-sizing: border-box;
  margin: auto;
}

.content-wrapper {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.checkerboard-background {
  background-image:
    linear-gradient(45deg, #ccc 25%, transparent 25%),
    linear-gradient(-45deg, #ccc 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, #ccc 75%),
    linear-gradient(-45deg, transparent 75%, #ccc 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

.system-frame {
  position: relative;
  transition: all 0.3s ease;
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
}

.settings-panel {
  width: 300px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  overflow-y: auto;
}

.setting-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin-bottom: 12px;
  font-weight: 500;
}

.frame-style-select {
  width: 100%;
}

.background-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-colors {
  display: flex;
  gap: 8px;
  margin: 12px 0;
  overflow-x: auto;
  padding: 4px;
}

.color-item,
.color-grid-item {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--el-border-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 4px;
}

.color-item.transparent,
.color-grid-item.transparent {
  background: #fff;
  position: relative;
}

.color-item.transparent::before,
.color-grid-item.transparent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #ccc 25%, transparent 25%),
              linear-gradient(-45deg, #ccc 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #ccc 75%),
              linear-gradient(-45deg, transparent 75%, #ccc 75%);
  background-size: 10px 10px;
  background-position: 0 0, 0 5px, 5px -5px, -5px 0;
  border-radius: 7px;
}

.color-item.active,
.color-grid-item.active {
  border: 2px solid var(--el-color-primary);
  transform: scale(1.05);
}

.color-item:hover,
.color-grid-item:hover {
  transform: scale(1.05);
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  padding: 16px 0;
}

.window-controls {
  position: absolute;
  top: 8px;
  display: flex;
  z-index: 1;
}

.macos-light .window-controls,
.macos-dark .window-controls {
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  gap: 8px;
  padding: 0 4px;
}

.macos-light .control-button,
.macos-dark .control-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: all 0.1s ease;
  margin: 0 4px;
}

.windows-11-light .window-controls,
.windows-11-dark .window-controls {
  right: 0;
  top: 0;
  height: 100%;
}

.windows-11-light .control-button,
.windows-11-dark .control-button {
  width: 46px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.windows,
.windows-controls,
.macos,
.macos-controls,
.background-type,
.gradient-select,
.gradient-option {
  display: none;
}

.window-titlebar {
  position: relative;
  height: 28px;
  z-index: 1;
  display: flex;
  align-items: center;
}

.macos-light,
.macos-dark {
  background: #e9e9e9;
  border: 1px solid #d1d1d1;
}

.macos-dark {
  background: #1f1f1f;
  border: 1px solid #404040;
}

.windows-11-light {
  background: #ffffff;
  border-bottom: 1px solid #e0e0e0;
}

.windows-11-dark {
  background: #1f1f1f;
  border-bottom: 1px solid #2b2b2b;
}

.macos-light .close { background: #ff5f57; border: 1px solid #e0443e; }
.macos-light .minimize { background: #febc2e; border: 1px solid #e1a116; }
.macos-light .maximize { background: #28c940; border: 1px solid #27aa35; }

.macos-dark .close { background: #ff5f57; border: 1px solid rgba(0, 0, 0, 0.3); }
.macos-dark .minimize { background: #febc2e; border: 1px solid rgba(0, 0, 0, 0.3); }
.macos-dark .maximize { background: #28c940; border: 1px solid rgba(0, 0, 0, 0.3); }

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.color-drawer :deep(.el-drawer__body) {
  padding: 0;
}

.color-tabs {
  padding: 20px;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  padding: 16px 0;
}

.color-grid-item {
  aspect-ratio: 1;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--el-border-color-light);
  display: flex;
  align-items: center;
  justify-content: center;
}

.windows-11-light,
.windows-11-dark {
  position: relative;
  display: flex;
  justify-content: flex-end;
}

.windows-11-light .control-button,
.windows-11-dark .control-button {
  width: 30px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
}

.windows-11-light .control-button:hover {
  background-color: rgba(0, 0, 0, 0.06);
}

.windows-11-light .close:hover {
  background-color: #c42b1c;
  color: #ffffff;
}

.windows-11-dark .control-button:hover {
  background-color: rgba(255, 255, 255, 0.06);
}

.windows-11-dark .close:hover {
  background-color: #c42b1c;
  color: #ffffff;
}

/* Windows 11 控制按钮图标 */
.windows-11-light .minimize::before,
.windows-11-dark .minimize::before {
  content: '';
  position: absolute;
  width: 10px;
  height: 1px;
  background: currentColor;
  top: 50%;
}

.windows-11-light .maximize::before,
.windows-11-dark .maximize::before {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  border: 1px solid currentColor;
  top: calc(50% - 5px);
}

.windows-11-light .close::before,
.windows-11-dark .close::before,
.windows-11-light .close::after,
.windows-11-dark .close::after {
  content: '';
  position: absolute;
  width: 14px;
  height: 1px;
  background: currentColor;
  top: 50%;
}

.windows-11-light .close::before,
.windows-11-dark .close::before {
  transform: rotate(45deg);
}

.windows-11-light .close::after,
.windows-11-dark .close::after {
  transform: rotate(-45deg);
}

/* 设置按钮颜色 */
.windows-11-light .control-button {
  color: #000000;
}

.windows-11-dark .control-button {
  color: #ffffff;
}

.aspect-ratio-select {
  width: 100%;
  margin-bottom: 12px;
}
</style> 