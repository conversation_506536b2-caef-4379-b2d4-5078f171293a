from setuptools import setup, Extension
from Cython.Build import cythonize
import os
import platform

# --- Configuration ---
# Directories containing Python modules to be Cythonized
# Paths are relative to this setup.py script (i.e., backend/)
MODULE_DIRS_TO_CYTHONIZE = ["api", "ai_tools", "conf", "utilities"]
# Exclude specific files from Cythonization (relative paths from backend/)
# Example: EXCLUDE_FILES = {"api/do_not_compile_this.py"}

# api下的所有文件，除了server.py
EXCLUDE_FILES = set(
    os.path.join("api", f) for f in os.listdir("api") if f != "system_api.py"
)

EXCLUDE_FILES.add("utilities/log.py")
EXCLUDE_FILES.add("utilities/response.py")

# Exclude specific directories from being searched for .py files
# Useful for __pycache__ or test directories within your module dirs
EXCLUDE_SUBDIRS = {"__pycache__"}
# --- End Configuration ---


def delete_compiled_files(base_dirs):
    """Deletes specified compiled files only from the directories listed in base_dirs."""
    extensions_to_delete = (".so", ".c", ".pyd")

    for dir_path in base_dirs:
        if not os.path.isdir(dir_path):
            print(f"Warning: Directory '{dir_path}' not found for cleaning.")
            continue
        for root, _, files in os.walk(dir_path):
            for file_name in files:
                if file_name.endswith(extensions_to_delete):
                    filepath = os.path.join(root, file_name)
                    try:
                        os.remove(filepath)
                        print(f"Deleted: {filepath}")
                    except OSError as e:
                        print(f"Error deleting {filepath}: {e}")


delete_compiled_files(MODULE_DIRS_TO_CYTHONIZE)


def find_py_files(base_dirs):
    """Finds all .py files in the specified base_dirs, respecting exclusions."""
    py_files = []
    for base_dir in base_dirs:
        for root, dirs, files in os.walk(base_dir, topdown=True):
            # Filter out excluded subdirectories
            dirs[:] = [
                d
                for d in dirs
                if d not in EXCLUDE_SUBDIRS
                and os.path.join(root, d) not in EXCLUDE_SUBDIRS
            ]
            for file_name in files:
                if file_name.endswith(".py"):
                    filepath = os.path.join(root, file_name)
                    # Convert to relative path for exclusion check if needed,
                    # though full path comparison is fine if EXCLUDE_FILES uses full relative paths
                    if filepath not in EXCLUDE_FILES:
                        print(filepath)
                        py_files.append(filepath)
    return py_files


extensions = []
py_files_to_compile = find_py_files(MODULE_DIRS_TO_CYTHONIZE)

if not py_files_to_compile:
    print("No Python files found to compile. Check MODULE_DIRS_TO_CYTHONIZE and paths.")
else:
    print(f"Found {len(py_files_to_compile)} Python files to compile:")
    # for f in py_files_to_compile:
    #     print(f"  - {f}")


for py_file in py_files_to_compile:
    # Module name is derived from the file path relative to the current directory (backend/)
    # e.g., "api/system_api.py" becomes "api.system_api"
    module_path_without_ext = os.path.splitext(py_file)[0]
    module_name = module_path_without_ext.replace(os.sep, ".")

    extensions.append(
        Extension(
            name=module_name,
            sources=[py_file],
            # You can add other compiler/linker options here if needed
            # extra_compile_args=["-O3"],
            # extra_link_args=[],
        )
    )

# Optional: Compiler directives for Cython
# 'embedsignature': True allows help() to show function signatures
# 'language_level': '3' or '3str' for Python 3 syntax
cython_compiler_directives = {
    "language_level": "3",
    "embedsignature": True,
    # 'profile': True, # Enable for profiling with cProfile
    # 'linetrace': True, # Enable for line_profiler
}

setup(
    name="LingXiangToolboxCompiledModules",  # 这个名称用于构建过程
    ext_modules=(
        cythonize(
            extensions,
            compiler_directives=cython_compiler_directives,
            # force=True,  # 取消注释以总是重新编译
            # quiet=True,  # 取消注释以减少 Cython 化期间的详细输出
        )
        if extensions
        else []
    ),  # 仅当存在扩展时才调用 cythonize
    packages=MODULE_DIRS_TO_CYTHONIZE,  # <--- 添加这一行
    # 包含 zip_safe=False 是 Cython 扩展的一个好习惯
    zip_safe=False,
)


print("\nSetup script finished.")
if not extensions:
    print("WARNING: No extensions were defined. Ensure your Python files were found.")
