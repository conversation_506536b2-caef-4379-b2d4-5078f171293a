import mimetypes
import os
import sys
import time
import traceback
import webview
from webview import WebViewException


from api import API
from conf.setting import settings
from conf.config import config

from utilities.log import init_logging, logger
from api.video_api import VideoDeWatermarkAPI
from api.system_api import SystemAPI
from api.server import APIServer
from api.image_api import ImageMattingAPI
from api.inpainting_api import InpaintingAPI
from api.ppocr_api import PPOCRAPI
from api.convert_image_api import ConvertImageAPI
from api.real_image import RealImageAPI
from api.smart_video_split import SmartVideoSplitAPI
from api.compress_api import CompressImageAPI
from pathlib import Path
from ai_tools.utils import get_api_server_url


def main():
    # 初始化临时目录

    mimetypes.add_type("application/javascript", ".js")
    # 初始化日志
    init_logging(log_name="backend")
    VERSION = settings.VERSION
    port = config.get("api_server.port", 11112)
    api_server = APIServer(host="127.0.0.1", port=port)
    api_server.start()

    if settings.DEBUG:
        url = os.getenv("DEV_URL", "http://localhost:4000")
    else:
        url = f"{get_api_server_url()}"

    api = API()
    api_class_list = [
        VideoDeWatermarkAPI,
        SystemAPI,
        ImageMattingAPI,
        InpaintingAPI,
        PPOCRAPI,
        ConvertImageAPI,
        RealImageAPI,
        SmartVideoSplitAPI,
        CompressImageAPI,
    ]
    for api_class in api_class_list:
        api.add_apis(api_class)

    try:
        x = config.get("window.x", 0)
        y = config.get("window.y", 0)
        width = config.get("window.width", 1000)
        height = config.get("window.height", 800)
        if x < 0 or y < 0:
            x = 100
            y = 25
            width = 1024
            height = 800
            config.save("window.x", x)
            config.save("window.y", y)
            config.save("window.width", width)
            config.save("window.height", height)
        print(url)
        window = webview.create_window(
            f"{settings.TOOL_NAME} - {VERSION}",
            url=url,
            js_api=api,
            width=width,
            height=height,
            x=x,
            y=y,
            on_top=config.get("window.on_top", False),
            confirm_close=True,
        )
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Start window error: {traceback.format_exc()}")
        sys.exit(1)

    logger.info(f"Debug: {settings.DEBUG}")
    logger.info(f"load url: {url}")

    def on_moved(x, y):
        config.save("window.x", max(100, int(x)))
        config.save("window.y", max(23, int(y)))

    def on_resized(width, height):
        config.save("window.width", max(1024, int(width)))
        config.save("window.height", max(800, int(height)))

    def on_close():
        logger.info("process closed")
        api_server.stop()
        window.destroy()

    def on_closing():
        # 防止窗口置顶,确认退出框不展示在最前面
        time.sleep(0.01)
        if window.on_top:
            window.on_top = False

    def bind(bind_window):
        bind_window.events.closed += on_close
        bind_window.events.closing += on_closing
        bind_window.events.resized += on_resized
        bind_window.events.moved += on_moved

    try:
        storage_path = Path.home() / ".lx_cache" / "webview"
        webview.settings["ALLOW_DOWNLOADS"] = True
        webview.start(
            bind,
            window,
            debug=settings.DEBUG,
            http_server=True,
            private_mode=False,
            storage_path=str(storage_path),
        )
    except WebViewException:
        traceback.print_exc()
        logger.error(f"Webview.start() error: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
