[project]
name = "backend"
version = "0.1.6"
description = "Default template for PDM package"
authors = [
    {name = "xiaobin", email = "<EMAIL>"},
]
dependencies = [
    "pywebview==5.4",
    "loguru==0.7.3",
    "pydantic==2.10.4",
    "pydantic-settings==2.7.1",
    "pyinstaller==6.11.0",
    "Pillow==11.1.0",
    "opencv-python==*********",
    "requests==2.32.3",
    "ffmpeg-python==0.2.0",
    "onnxruntime==1.20.1",
    "fastapi==0.115.11",
    "uvicorn==0.34.0",
    "numpy==1.26.4",
    "cryptography==44.0.0",
    "psd-tools==1.10.7",
    "boto3==1.36.26",
    "paddleocr==2.9.1",
    "paddlepaddle==2.6.2",
    "dnspython>=2.7.0",
    "scenedetect==0.6.6",
    "openai==1.86.0",
]
requires-python = "==3.12.*"
readme = "README.md"
license = {text = "MIT"}


[project.optional-dependencies]
win = [
    "wmi==1.5.1",
]
mac = [
    "dmgbuild>=1.6.4",
]
[tool.pdm]
distribution = false


[tool.pdm.scripts]
dev = { cmd = "python main.py", env = {DEBUG = "True"} }
build-front = "python build.py -t f"
production-mac = "python build.py -t p"
zip = "python build.py -t z"
start = "python main.py"
build-debug-win = "pyinstaller --noconfirm --clean main_win.spec --distpath dist/debug_win -- --debug"
build-win = "pyinstaller --noconfirm --clean main_win.spec --distpath dist/win"
build-mac = "pyinstaller --noconfirm --clean main_mac.spec --distpath dist/mac"
build-m1 = "pyinstaller --noconfirm --clean main_m1.spec --distpath dist/m1"

