import os
import sys
import site

# 修复 site.USER_SITE 可能为 None 的问题
if site.USER_SITE is None:
    site.USER_SITE = ""

# 设置 PaddlePaddle 相关环境变量
bundle_dir = os.path.dirname(sys.executable)
if getattr(sys, "frozen", False):
    # 在打包环境中
    paddle_dir = os.path.join(bundle_dir, "paddle")
    os.environ["PADDLE_ROOT"] = paddle_dir
    os.environ["PADDLE_BINARY_PATH"] = os.path.join(paddle_dir, "libs")
    os.environ["LD_LIBRARY_PATH"] = (
        os.path.join(paddle_dir, "libs") + ":" + os.environ.get("LD_LIBRARY_PATH", "")
    )
    os.environ["DYLD_LIBRARY_PATH"] = (
        os.path.join(paddle_dir, "libs") + ":" + os.environ.get("DYLD_LIBRARY_PATH", "")
    )

    # 修补 paddle.fluid.core 模块
    def patch_paddle_core():
        try:
            import paddle.fluid.core

            # 替换 set_paddle_lib_path 函数
            def patched_set_paddle_lib_path():
                return os.path.join(paddle_dir, "libs")

            paddle.fluid.core.set_paddle_lib_path = patched_set_paddle_lib_path
        except ImportError:
            pass

    # 在导入 paddle 之前应用补丁
    sys.path.insert(0, os.path.join(bundle_dir))
