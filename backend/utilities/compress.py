from PIL import Image
import os
import subprocess
import sys
import os
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent.parent


def get_pngquant_path():
    """获取打包后的 pngquant 可执行文件路径"""
    if sys.platform == "win32":
        return str(BASE_DIR / "pngquant" / "pngquant.exe")
    else:  # macOS and Linux
        return str(BASE_DIR / "pngquant" / "pngquant")


def compress_png_with_cli(input_path, output_path, quality_max=80):
    """
    通过调用 pngquant 命令行工具进行压缩
    """
    quality_min = quality_max - 15
    if quality_min < 0:
        quality_min = 0
    pngquant_executable = get_pngquant_path()

    if not os.path.exists(pngquant_executable):
        print(f"错误: 未找到 pngquant 程序 at '{pngquant_executable}'")
        return False

    command = [
        pngquant_executable,
        "--force",  # 覆盖输出文件
        "--quality",
        f"{quality_min}-{quality_max}",  # 设置质量范围
        "--output",
        output_path,  # 指定输出文件
        "--",  # 表示文件名参数开始
        input_path,
    ]

    try:
        # 使用 subprocess.run 来执行命令
        process = subprocess.run(command, check=True, capture_output=True, text=True)
        print("压缩成功!")
        print("STDOUT:", process.stdout)
        return True
    except FileNotFoundError:
        print(f"错误: 无法执行 pngquant。请确保 '{pngquant_executable}' 存在且可执行。")
        return False
    except subprocess.CalledProcessError as e:
        print("压缩过程中出错:")
        print("Return code:", e.returncode)
        print("STDERR:", e.stderr)
        return False


def compress_jpg_webp(input_path, output_path=None, quality=85):
    ext = os.path.splitext(input_path)[1].lower()
    img = Image.open(input_path)
    # 去除元数据
    img_no_exif = Image.new(img.mode, img.size)
    img_no_exif.putdata(list(img.getdata()))
    img = img_no_exif
    # 设置输出路径
    if not output_path:
        output_path = input_path

    save_params = {}
    if ext in [".jpg", ".jpeg", ".webp"]:
        save_params["quality"] = quality
        save_params["optimize"] = True  # 尝试进一步压缩
    if ext == ".webp":
        save_params["method"] = 6  # WebP 压缩强度
    img.save(output_path, format=img.format, **save_params)


def can_compress_file(file_path):
    # png jpg webp
    ext = os.path.splitext(file_path)[1].lower()
    if ext in [".png", ".jpg", ".jpeg", ".webp"]:
        return True
    return False


def compress_image(input_path, output_path=None, quality=85):
    """
    压缩 PNG、JPEG、WebP 图像
    :param input_path: 输入文件路径
    :param output_path: 输出文件路径（默认覆盖）
    :param quality: 压缩质量（JPEG/WebP 有效）
    """

    # PNG 压缩：颜色量化到 256 色
    ext = os.path.splitext(input_path)[1].lower()
    if ext == ".png":
        compress_png_with_cli(input_path, output_path, quality)
    else:
        compress_jpg_webp(input_path, output_path, quality)
    original_size = os.path.getsize(input_path)
    new_size = os.path.getsize(output_path)
    print(f"{input_path} 压缩完成: {original_size/1024:.1f}KB → {new_size/1024:.1f}KB")
    return original_size, new_size


if __name__ == "__main__":
    # 示例：单文件压缩
    imgs = ["擦除后.png", "擦除前.jpg", "抠图后.png", "people.png"]
    for img in imgs:
        save_path = f"commpress-{img}"
        compress_image(img, save_path, quality=60)
