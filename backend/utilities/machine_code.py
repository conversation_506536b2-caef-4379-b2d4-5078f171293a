import hashlib
import platform
import subprocess
import re
from pathlib import Path
import sys
import traceback
import time
import os
import json


try:
    from utilities.log import logger
except ImportError:
    BASE_DIR = Path(__file__).resolve().parent.parent
    sys.path.append(str(BASE_DIR))
    from utilities.log import logger


# 缓存文件路径
CACHE_DIR = os.path.join(os.path.expanduser("~"), ".lx_cache")
CACHE_FILE = os.path.join(CACHE_DIR, "machine_id.json")


def get_disk_serial(max_retries=3, retry_delay=1):
    """获取硬盘序列号，带有重试机制"""
    for attempt in range(max_retries):
        try:
            if platform.system() == "Windows":
                import wmi

                c = wmi.WMI()
                # 按照序列号排序，确保获取顺序一致
                for disk in sorted(c.Win32_DiskDrive(), key=lambda x: x.Index):
                    if disk.SerialNumber:
                        return disk.SerialNumber.strip()
            elif platform.system() == "Darwin":  # macOS
                # 首先尝试获取内部磁盘，这更加稳定
                cmd = ["diskutil", "info", "disk0"]
                try:
                    output = subprocess.check_output(cmd).decode("utf-8")
                    match = re.search(r"(?i)Serial Number:\s+([^\n]+)", output)
                    if match:
                        return match.group(1).strip()
                except:
                    pass

                # 备用方法
                cmd = ["system_profiler", "SPNVMeDataType", "SPSerialATADataType"]
                output = subprocess.check_output(cmd).decode("utf-8")
                # 查找第一个硬盘的序列号
                match = re.search(r"Serial Number: +([^\n]+)", output)
                if match:
                    return match.group(1).strip()
        except Exception:
            logger.error(
                f"Error getting disk serial (attempt {attempt+1}): {traceback.format_exc()}"
            )
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
    return "UNKNOWN_DISK"


def get_mac_hardware_info():
    """获取Mac系统的硬件信息"""
    try:
        # 首先尝试使用 system_profiler 命令获取硬件UUID（最稳定）
        cmd = ["system_profiler", "SPHardwareDataType"]
        output = subprocess.check_output(cmd).decode("utf-8")

        # 尝试获取硬件UUID（这是最稳定的标识符）
        uuid_match = re.search(r"Hardware UUID: +([^\n]+)", output)
        if uuid_match:
            return f"UUID:{uuid_match.group(1).strip()}"

        # 尝试获取系统序列号
        serial_match = re.search(r"Serial Number \(system\): +([^\n]+)", output)
        if serial_match:
            return f"SYS:{serial_match.group(1).strip()}"

        # 尝试获取型号标识符和板号的组合
        model_match = re.search(r"Model Identifier: +([^\n]+)", output)
        board_id_match = re.search(r"Board ID: +([^\n]+)", output)
        if model_match and board_id_match:
            return f"MODEL:{model_match.group(1).strip()}:{board_id_match.group(1).strip()}"
        elif model_match:
            return f"MODEL:{model_match.group(1).strip()}"

    except subprocess.CalledProcessError:
        # system_profiler 命令失败，尝试使用 ioreg 命令
        try:
            cmd = ["ioreg", "-rd1", "-c", "IOPlatformExpertDevice"]
            output = subprocess.check_output(cmd).decode("utf-8")

            # 查找 IOPlatformUUID（最稳定）
            uuid_match = re.search(r'"IOPlatformUUID" = "([^"]+)"', output)
            if uuid_match:
                return f"UUID:{uuid_match.group(1).strip()}"

            # 查找 IOPlatformSerialNumber
            serial_match = re.search(r'"IOPlatformSerialNumber" = "([^"]+)"', output)
            if serial_match:
                return f"SYS:{serial_match.group(1).strip()}"

        except Exception:
            logger.error(f"Error getting Mac hardware info: {traceback.format_exc()}")

    return "UNKNOWN_HARDWARE"


def get_hardware_id():
    """获取CPU序列号或主板序列号"""
    if platform.system() == "Windows":
        try:
            import wmi

            c = wmi.WMI()

            # 尝试获取主板UUID（最稳定的标识符）
            for csproduct in c.Win32_ComputerSystemProduct():
                if (
                    csproduct.UUID
                    and csproduct.UUID != "00000000-0000-0000-0000-000000000000"
                ):
                    return f"UUID:{csproduct.UUID.strip()}"

            # 尝试获取主板序列号
            for board in c.Win32_BaseBoard():
                if board.SerialNumber and board.SerialNumber.strip() not in [
                    "0",
                    "00",
                    "To be filled by O.E.M.",
                ]:
                    return f"MB:{board.SerialNumber.strip()}"

            # 尝试获取BIOS序列号
            for bios in c.Win32_BIOS():
                if bios.SerialNumber and bios.SerialNumber.strip() not in [
                    "0",
                    "00",
                    "To be filled by O.E.M.",
                ]:
                    return f"BIOS:{bios.SerialNumber.strip()}"

            # 尝试获取CPU ID（主要适用于Intel CPU）
            for cpu in c.Win32_Processor():
                if cpu.ProcessorId:
                    return f"CPU:{cpu.ProcessorId.strip()}"

        except Exception as e:
            logger.error(f"Error getting hardware ID: {traceback.format_exc()}")

    elif platform.system() == "Darwin":  # macOS
        return get_mac_hardware_info()

    return "UNKNOWN_HARDWARE"


def load_cached_machine_code():
    """从缓存加载机器码"""
    try:
        if os.path.exists(CACHE_FILE):
            with open(CACHE_FILE, "r") as f:
                data = json.load(f)
                return data.get("machine_code")
    except Exception:
        logger.error(f"Error loading cached machine code: {traceback.format_exc()}")
    return None


def save_machine_code_to_cache(machine_code, hardware_info):
    """将机器码保存到缓存"""
    try:
        if not os.path.exists(CACHE_DIR):
            os.makedirs(CACHE_DIR)

        data = {
            "machine_code": machine_code,
            "hardware_info": hardware_info,
            "created_at": time.time(),
        }

        with open(CACHE_FILE, "w") as f:
            json.dump(data, f)
    except Exception:
        logger.error(f"Error saving machine code to cache: {traceback.format_exc()}")


def get_machine_code():
    """
    生成机器唯一识别码
    基于硬盘序列号和硬件ID生成唯一的机器码
    """
    # 首先尝试从缓存加载
    cached_code = load_cached_machine_code()
    if cached_code:
        return cached_code

    try:
        # 获取硬件信息
        disk_serial = get_disk_serial()
        hardware_id = get_hardware_id()
        system_type = platform.system()

        # 组合信息并生成hash
        hardware_str = f"OS:{system_type}#DISK:{disk_serial}#{hardware_id}".encode(
            "utf-8"
        )
        hardware_info = {
            "system": system_type,
            "disk_serial": disk_serial,
            "hardware_id": hardware_id,
        }
    except Exception as e:
        logger.error(f"获取硬件信息失败，将使用备用方案: {traceback.format_exc()}")
        system_type = platform.system()
        node_name = platform.node()  # 主机名
        # 使用备用信息生成
        hardware_str = f"OS:{system_type}#NODE:{node_name}".encode("utf-8")
        hardware_info = {
            "system": system_type,
            "node_name": node_name,
            "fallback": True,
        }

    machine_code = hashlib.md5(hardware_str).hexdigest()

    # 格式化为更易读的形式 (每4个字符加一个横杠)
    formatted_code = "-".join(
        machine_code[i : i + 4] for i in range(0, len(machine_code), 4)
    )

    # 保存到缓存
    save_machine_code_to_cache(formatted_code, hardware_info)

    return formatted_code


def get_system_info():
    """获取系统信息（用于调试）"""
    return {
        "操作系统": platform.system(),
        "系统版本": platform.version(),
        "硬盘序列号": get_disk_serial(),
        "硬件ID": get_hardware_id(),
        "机器码": get_machine_code(),
    }


if __name__ == "__main__":
    # 打印详细的系统信息
    info = get_system_info()
    for key, value in info.items():
        print(f"{key}: {value}")
