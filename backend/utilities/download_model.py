# 下载模型
# epoch_100.onnx
# lama_fp32.onnx
# migan.onnx
# modnet.onnx
# rmbg14.onnx
# tiny_232.onnx
# mac_ffmpeg/ffmpeg
# mac_ffmpeg/ffprobe
# win_ffmpeg/ffmpeg.exe
# win_ffmpeg/ffprobe.exe

# ppocr
# det_model.onnx
# rec_model.onnx
# cls_model.onnx


from pathlib import Path
import os
import boto3
import sys
from botocore.exceptions import ClientError
import platform

BASE_DIR = Path(__file__).resolve().parent.parent

try:
    from conf.config import config
    from utilities.log import logger
except ImportError:
    sys.path.append(str(BASE_DIR))
    from conf.config import config
    from utilities.log import logger

bucket_name = "lingxiangtools"


def resume_download_from_r2(bucket_name, object_key, file_path):
    """
    从R2下载文件
    bucket_name: 桶名
    object_key: 文件名
    file_path: 本地文件路径
    """
    # 配置 Cloudflare R2 连接
    s3 = boto3.client(
        "s3",
        endpoint_url="https://9c43530b4f1f0a2ba9ec6aaaa6ceb6b5.r2.cloudflarestorage.com",
        aws_access_key_id="d1c9c4684481362c422f70a923bebf98",
        aws_secret_access_key="405bfcf72f4e22524b10f5a200992a6b2bd28f3ac5f822d4c83682c48a5cdd26",
        region_name="auto",
    )

    try:
        # 获取远程文件信息
        head = s3.head_object(Bucket=bucket_name, Key=object_key)
        total_size = head["ContentLength"]

        # 检查本地文件状态
        file_exists = os.path.exists(file_path)
        current_size = os.path.getsize(file_path) if file_exists else 0

        # 验证本地文件有效性
        if file_exists:
            if current_size > total_size:
                logger.info("本地文件异常，重新下载...")
                os.remove(file_path)
                current_size = 0
            elif current_size == total_size:
                logger.info("文件已存在且完整")
                return

        # 断点续传核心逻辑
        with open(file_path, "ab" if file_exists else "wb") as f:
            while current_size < total_size:
                # 计算剩余下载范围
                range_header = f"bytes={current_size}-{total_size-1}"

                try:
                    response = s3.get_object(
                        Bucket=bucket_name, Key=object_key, Range=range_header
                    )

                    # 分块写入（5MB chunks）
                    chunk_size = 5 * 1024 * 1024
                    for chunk in response["Body"].iter_chunks(chunk_size):
                        f.write(chunk)
                        f.flush()
                        os.fsync(f.fileno())

                        current_size += len(chunk)

                except ClientError as e:
                    if e.response["Error"]["Code"] == "416":
                        logger.info("范围请求无效，可能文件已修改，重新下载...")
                        os.remove(file_path)
                        return resume_download_from_r2(
                            bucket_name, object_key, file_path
                        )
                    else:
                        raise
        logger.info("下载完成！")

    except ClientError as e:
        logger.error(e.response)
        error_code = e.response["Error"]["Code"]
        error_messages = {"404": "文件不存在", "403": "认证失败", "416": "无效范围请求"}
        logger.error(f"错误 ({error_code}): {error_messages.get(error_code, '未知错误')}")


def download_migan():
    base_path = Path(config.get("model_dir"))
    if not base_path.exists():
        base_path.mkdir(parents=True, exist_ok=True)
    resume_download_from_r2(
        bucket_name=bucket_name,
        object_key="migan.onnx",
        file_path=str(base_path / "m_repair_tiny.onnx"),
    )


def download_modnet():
    base_path = Path(config.get("model_dir"))
    if not base_path.exists():
        base_path.mkdir(parents=True, exist_ok=True)
    resume_download_from_r2(
        bucket_name=bucket_name,
        object_key="modnet.onnx",
        file_path=str(base_path / "people_segmentation.onnx"),
    )


def download_segmentation_quick():
    base_path = Path(config.get("model_dir"))
    if not base_path.exists():
        base_path.mkdir(parents=True, exist_ok=True)
    resume_download_from_r2(
        bucket_name=bucket_name,
        object_key="rm14.onnx",
        file_path=str(base_path / "segmentation_quick.onnx"),
    )


def download_tiny_232():
    base_path = Path(config.get("model_dir"))
    if not base_path.exists():
        base_path.mkdir(parents=True, exist_ok=True)
    resume_download_from_r2(
        bucket_name=bucket_name,
        object_key="tiny_232.onnx",
        file_path=str(base_path / "segmentation_normal.onnx"),
    )


def download_epoch_100():
    base_path = Path(config.get("model_dir"))
    if not base_path.exists():
        base_path.mkdir(parents=True, exist_ok=True)
    resume_download_from_r2(
        bucket_name=bucket_name,
        object_key="epoch_100.onnx",
        file_path=str(base_path / "segmentation_high.onnx"),
    )


def download_lama_fp32():
    base_path = Path(config.get("model_dir"))
    if not base_path.exists():
        base_path.mkdir(parents=True, exist_ok=True)
    resume_download_from_r2(
        bucket_name=bucket_name,
        object_key="lama_fp32.onnx",
        file_path=str(base_path / "m_repair_normal.onnx"),
    )


def download_ffmpeg():
    base_path = Path(config.get("model_dir"))
    if platform.system() == "Windows":
        save_path = base_path / "win_ffmpeg"
        save_path.mkdir(parents=True, exist_ok=True)
        resume_download_from_r2(
            bucket_name=bucket_name,
            object_key="ffmpeg.exe",
            file_path=str(save_path / "ffmpeg.exe"),
        )
        resume_download_from_r2(
            bucket_name=bucket_name,
            object_key="ffprobe.exe",
            file_path=str(save_path / "ffprobe.exe"),
        )
    elif platform.system() == "Darwin":
        save_path = base_path / "mac_ffmpeg"
        save_path.mkdir(parents=True, exist_ok=True)
        resume_download_from_r2(
            bucket_name=bucket_name,
            object_key="ffmpeg",
            file_path=str(save_path / "ffmpeg"),
        )
        resume_download_from_r2(
            bucket_name=bucket_name,
            object_key="ffprobe",
            file_path=str(save_path / "ffprobe"),
        )
    else:
        raise OSError("Unsupported operating system")


def download_ppocr():
    base_path = Path(config.get("model_dir"))
    if not base_path.exists():
        base_path.mkdir(parents=True, exist_ok=True)
    resume_download_from_r2(
        bucket_name=bucket_name,
        object_key="det_model.onnx",
        file_path=str(base_path / "det_model.onnx"),
    )
    resume_download_from_r2(
        bucket_name=bucket_name,
        object_key="rec_model.onnx",
        file_path=str(base_path / "rec_model.onnx"),
    )
    resume_download_from_r2(
        bucket_name=bucket_name,
        object_key="cls_model.onnx",
        file_path=str(base_path / "cls_model.onnx"),
    )
    logger.info("PPOCR模型下载完成！")


def download_real_general():
    base_path = Path(config.get("model_dir"))
    if not base_path.exists():
        base_path.mkdir(parents=True, exist_ok=True)
    resume_download_from_r2(
        bucket_name=bucket_name,
        object_key="real_general.onnx",
        file_path=str(base_path / "real_general.onnx"),
    )
    logger.info("通用图像处理模型下载完成！")


def download_real_anime():
    base_path = Path(config.get("model_dir"))
    if not base_path.exists():
        base_path.mkdir(parents=True, exist_ok=True)
    resume_download_from_r2(
        bucket_name=bucket_name,
        object_key="real_anime.onnx",
        file_path=str(base_path / "real_anime.onnx"),
    )
    logger.info("动漫图像处理模型下载完成！")


if __name__ == "__main__":
    # 使用示例
    # download_migan()
    download_ppocr()
