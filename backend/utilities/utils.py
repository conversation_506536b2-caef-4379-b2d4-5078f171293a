import base64
from PIL import Image
from io import BytesIO
import os
import requests
from pathlib import Path
import numpy as np
from cv2 import blur, GaussianBlur
import cv2
from scipy import ndimage
from psd_tools import PSDImage
from psd_tools.api.layers import PixelLayer
from typing import Union, Tuple
import random
import math
import re
import io
import hashlib
from PIL import ImageSequence, UnidentifiedImageError


def is_image(filename):
    return any(
        filename.lower().endswith(ext)
        for ext in [".jpg", ".jpeg", ".png", ".gif", ".webp"]
    )


def is_video(video_path: str) -> bool:
    valid_extensions = (".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv")
    return video_path.lower().endswith(valid_extensions)


def img_to_base64(image_path: str):
    # 检查是否是ICNS文件
    if image_path.lower().endswith(".icns"):
        try:
            with Image.open(image_path) as icns:
                # ICNS文件包含多个尺寸的图标，获取最大尺寸的那个
                largest_size = max(icns.size)
                img = icns.resize((largest_size, largest_size))

                # 转换为PNG格式
                buffer = BytesIO()
                img.save(buffer, format="PNG")
                img_str = base64.b64encode(buffer.getvalue()).decode("utf-8")
                return f"data:image/png;base64,{img_str}"
        except Exception as e:
            print(f"Error converting ICNS to base64: {str(e)}")
            return None

    # 原有的处理逻辑
    with Image.open(image_path) as img:
        # Check if the image is in WebP format
        if img.format == "WEBP":
            # Convert the image to a format supported by pywebview (e.g., PNG or JPEG)
            img = img.convert("RGBA")
            buffer = BytesIO()
            img.save(buffer, format="PNG")
            img_format = "image/png"
        else:
            buffer = BytesIO()
            img.save(buffer, format=img.format)
            img_format = f"image/{img.format.lower()}"

        # Encode the image to base64
        img_str = base64.b64encode(buffer.getvalue()).decode("utf-8")

        # Create the data URL
        data_url = f"data:{img_format};base64,{img_str}"

    return data_url


# Image 转base64
def image_obj_to_base64(image_obj: Image):
    buffered = BytesIO()
    image_obj.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")
    return "data:image/png;base64," + img_str


# base64 转 png
def base64_to_image(base64_data, save_path: str, dpi=(72, 72)):
    image_data = base64.b64decode(base64_data.split(",")[1])
    image_obj = Image.open(BytesIO(image_data))
    _, extension = os.path.splitext(save_path)
    if extension.lower() == ".jpg" or extension.lower() == ".jpeg":
        image_obj = image_obj.convert("RGB")
        image_obj.save(save_path, "JPEG")
    else:
        image_obj.save(save_path, extension[1:].upper(), dpi=dpi)
    return save_path


def base64_to_image_obj(base64_data):
    image_data = base64.b64decode(base64_data.split(",")[1])
    return Image.open(BytesIO(image_data))


def format_size(size_bytes: Union[int, float]) -> str:
    # 格式化文件大小
    for unit in ["B", "KB", "MB", "GB", "TB"]:
        if size_bytes < 1024:
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024
    return f"{size_bytes:.2f} PB"


def read_image(img: Union[str, BytesIO, Image.Image]) -> tuple[Image.Image, str]:
    """
    读取图片并返回图片对象和格式。

    Args:
        img: 图片路径、URL、base64字符串或BytesIO对象

    Returns:
        tuple: (Image对象, 图片格式字符串)
    """
    if isinstance(img, Image.Image):
        if img.format is None:
            return img, "PNG"
        return img, img.format.upper()

    if isinstance(img, BytesIO):
        try:
            image = Image.open(img)
            return image, image.format.upper()
        except Exception as e:
            raise ValueError(f"Failed to open image from BytesIO: {str(e)}")

    if isinstance(img, str):
        if img.startswith("http"):
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36"
            }
            try:
                response = requests.get(img, timeout=10, headers=headers)
                response.raise_for_status()
                image = Image.open(BytesIO(response.content))
                return image, image.format.upper()
            except requests.exceptions.RequestException as e:
                raise Exception(f"请求图像时出错: {str(e)}")
            except UnidentifiedImageError:
                raise Exception("无法识别的图像格式")
            except Exception as e:
                raise Exception(f"处理图像时出错: {str(e)}")

        elif ";base64," in img:
            try:
                base64_string = img.split(";base64,")[-1]
                image_data = base64.b64decode(base64_string)
                image = Image.open(BytesIO(image_data))
                return image, image.format.upper()
            except Exception as e:
                raise ValueError(f"Failed to decode base64 image: {str(e)}")

        elif Path(img).exists():
            try:
                image = Image.open(fp=img, mode="r")
                return image, image.format.upper()
            except Exception as e:
                raise ValueError(f"Failed to open image file: {str(e)}")
        else:
            raise FileNotFoundError(f"File {img} not found.")
    else:
        raise TypeError(f"Unsupported image type: {type(img)}")


def FB_blur_fusion_foreground_estimator_cpu(image, FG, B, alpha, r=90):
    if isinstance(image, Image.Image):
        image = np.array(image) / 255.0
    blurred_alpha = cv2.blur(alpha, (r, r))[:, :, None]

    blurred_FGA = cv2.blur(FG * alpha, (r, r))
    blurred_FG = blurred_FGA / (blurred_alpha + 1e-5)

    blurred_B1A = cv2.blur(B * (1 - alpha), (r, r))
    blurred_B = blurred_B1A / ((1 - blurred_alpha) + 1e-5)
    FG = blurred_FG + alpha * (image - alpha * blurred_FG - (1 - alpha) * blurred_B)
    FG = np.clip(FG, 0, 1)
    return FG, blurred_B


def FB_blur_fusion_foreground_estimator_cpu_2(image, alpha, r=90):
    # Thanks to the source: https://github.com/Photoroom/fast-foreground-estimation
    alpha = alpha[:, :, None]
    FG, blur_B = FB_blur_fusion_foreground_estimator_cpu(image, image, image, alpha, r)
    return FB_blur_fusion_foreground_estimator_cpu(image, FG, blur_B, alpha, r=6)[0]


def refine_foreground(image, mask, r=90):
    """both image and mask are in range of [0, 1]"""
    if mask.size != image.size:
        mask = mask.resize(image.size)
    image_np = np.array(image, dtype=np.float32) / 255.0
    alpha_np = np.array(mask, dtype=np.float32) / 255.0
    estimated_foreground = FB_blur_fusion_foreground_estimator_cpu_2(
        image_np, alpha_np, r=r
    )
    image_masked = Image.fromarray((estimated_foreground * 255.0).astype(np.uint8))
    image_masked.putalpha(mask.resize(image.size))
    return image_masked

def refine_foreground_bak(image: Image.Image, mask: Image.Image, r=90):
    if mask.size != image.size:
        mask = mask.resize(image.size)
    image = image.convert("RGB")
    image_array = np.array(image) / 255.0
    alpha_array = np.array(mask) / 255.0
    estimated_foreground = FB_blur_fusion_foreground_estimator_2(
        image_array, alpha_array, r=r
    )
    image_masked = Image.fromarray((estimated_foreground * 255.0).astype(np.uint8))
    image_masked.putalpha(mask.resize(image.size))
    return image_masked


def FB_blur_fusion_foreground_estimator_2(image: np.array, alpha: np.array, r=90):
    # Thanks to the source: https://github.com/Photoroom/fast-foreground-estimation
    alpha = alpha[:, :, None]
    F, blur_B = FB_blur_fusion_foreground_estimator(image, image, image, alpha, r)
    return FB_blur_fusion_foreground_estimator(image, F, blur_B, alpha, r=6)[0]


def FB_blur_fusion_foreground_estimator(image, F, B, alpha, r=90):
    if isinstance(image, Image.Image):
        image = np.array(image) / 255.0
    blurred_alpha = blur(alpha, (r, r))[:, :, None]

    blurred_FA = blur(F * alpha, (r, r))
    blurred_F = blurred_FA / (blurred_alpha + 1e-5)

    blurred_B1A = blur(B * (1 - alpha), (r, r))
    blurred_B = blurred_B1A / ((1 - blurred_alpha) + 1e-5)
    F = blurred_F + alpha * (image - alpha * blurred_F - (1 - alpha) * blurred_B)
    F = np.clip(F, 0, 1)
    return F, blurred_B

def hex_to_rgb(hex_color):
    # 移除前缀 '#'
    hex_color = hex_color.lstrip("#")
    # 将十六进制颜色转换为 RGB 元组，确保每个值是整数
    return tuple(int(hex_color[i : i + 2], 16) for i in (0, 2, 4))


def parse_color(color_str):
    """解析不同格式的颜色值"""
    # 处理渐变色
    if color_str.startswith("linear-gradient"):
        # 提取渐变色中的第一个颜色作为背景色
        colors = re.findall(r"#[0-9a-fA-F]{6}", color_str)
        if colors:
            return hex_to_rgb(colors[0])
        return (255, 255, 255)  # 默认白色

    # 处理 rgba 格式
    if color_str.startswith("rgba"):
        rgba = re.findall(r"[\d.]+", color_str)
        if len(rgba) == 4:
            return tuple(map(lambda x: int(float(x)), rgba[:3]))
        return (255, 255, 255)

    # 处理普通十六进制颜色
    if color_str.startswith("#"):
        return hex_to_rgb(color_str)

    # 处理 transparent
    if color_str == "transparent":
        return None

    # 默认返回白色
    return (255, 255, 255)


def save_bs64_image_add_bg(base64_data, hex_color, save_path):
    if hex_color == "transparent":
        base64_to_image(base64_data, save_path)
        return save_path
    # 读取图片
    image_data = base64.b64decode(base64_data.split(",")[1])
    image = Image.open(BytesIO(image_data)).convert("RGBA")
    # 将十六进制颜色转换为 RGB
    background_color = hex_to_rgb(hex_color)

    # 创建带有背景颜色的新图像
    background_image = Image.new("RGBA", image.size, background_color + (255,))

    # 粘贴解码后的图像到背景图像上
    background_image.paste(image, (0, 0), image)

    # 保存合成后的图像
    background_image.save(save_path, "PNG")

    return save_path


def parse_gradient(gradient_str):
    """解析渐变色字符串，返回渐变的颜色和角度"""
    # 提取角度和颜色
    angle = 45  # 默认角度

    # 更新角度匹配模式，支持更多格式
    angle_match = re.search(r"(\d+)deg|linear-gradient\(([\d.]+)deg", gradient_str)
    if angle_match:
        angle = int(float(angle_match.group(1) or angle_match.group(2)))

    # 更新颜色匹配模式，支持更多格式
    colors = []
    # 匹配十六进制颜色
    hex_colors = re.findall(r"#[0-9a-fA-F]{6}", gradient_str)
    if hex_colors:
        colors.extend(hex_colors)

    # 匹配 rgb/rgba 颜色
    rgb_colors = re.findall(r"rgb[a]?\s*\([\s\d,\.]+\)", gradient_str)
    for rgb in rgb_colors:
        # 转换 rgb/rgba 为十六进制
        values = re.findall(r"\d+", rgb)
        if len(values) >= 3:
            r, g, b = map(int, values[:3])
            hex_color = f"#{r:02x}{g:02x}{b:02x}"
            colors.append(hex_color)

    if len(colors) < 2:
        return None

    return {"angle": angle, "colors": [hex_to_rgb(c) for c in colors]}


def create_gradient_background(size, gradient_info):
    """创建渐变背景"""
    width, height = size
    image = Image.new("RGBA", size, (0, 0, 0, 0))

    angle = gradient_info["angle"]
    colors = gradient_info["colors"]

    # 调整角度计算（CSS 渐变角度和数学角度的转换）
    angle = (angle + 90) % 360  # CSS 渐变角度转换为数学角度

    # 计算渐变的起点和终点
    angle_rad = math.radians(angle)
    diagonal = math.sqrt(width**2 + height**2)

    # 计算渐变线的起点和终点
    center_x = width / 2
    center_y = height / 2

    start_x = center_x - math.cos(angle_rad) * diagonal / 2
    start_y = center_y - math.sin(angle_rad) * diagonal / 2
    end_x = center_x + math.cos(angle_rad) * diagonal / 2
    end_y = center_y + math.sin(angle_rad) * diagonal / 2

    # 使用 numpy 进行向量化计算以提高性能
    x, y = np.meshgrid(np.arange(width), np.arange(height))
    dx = x - start_x
    dy = y - start_y

    gradient_length = math.sqrt((end_x - start_x) ** 2 + (end_y - start_y) ** 2)
    dot_product = dx * (end_x - start_x) + dy * (end_y - start_y)
    ratio = np.clip(dot_product / (gradient_length * gradient_length), 0, 1)

    # 创建输出数组
    output = np.zeros((height, width, 4), dtype=np.uint8)

    # 处理开始和结束的颜色
    mask_start = ratio <= 0
    mask_end = ratio >= 1

    output[mask_start] = colors[0] + (255,)
    output[mask_end] = colors[-1] + (255,)

    # 处理渐变部分
    mask_middle = ~(mask_start | mask_end)
    segment_count = len(colors) - 1
    segment_length = 1.0 / segment_count

    for i in range(segment_count):
        segment_mask = (
            mask_middle
            & (ratio >= i * segment_length)
            & (ratio < (i + 1) * segment_length)
        )
        if not np.any(segment_mask):
            continue

        segment_ratio = (ratio[segment_mask] - i * segment_length) / segment_length
        color1 = np.array(colors[i])
        color2 = np.array(colors[i + 1])

        output[segment_mask, :3] = (
            color1 * (1 - segment_ratio[:, np.newaxis])
            + color2 * segment_ratio[:, np.newaxis]
        ).astype(np.uint8)
        output[segment_mask, 3] = 255

    return Image.fromarray(output)


def base64_set_bg(base64_data, color_str):
    """设置图片背景色"""
    if color_str == "transparent":
        return base64_data

    try:
        image_data = base64.b64decode(base64_data.split(",")[1])
        image = Image.open(BytesIO(image_data)).convert("RGBA")

        if color_str.startswith("linear-gradient"):
            # 解析渐变信息
            gradient_info = parse_gradient(color_str)
            if gradient_info:
                # 创建渐变背景
                background_image = create_gradient_background(image.size, gradient_info)
            else:
                # 如果渐变解析失败，使用第一个颜色作为纯色背景
                background_color = parse_color(color_str)
                background_image = Image.new(
                    "RGBA", image.size, background_color + (255,)
                )
        else:
            # 解析普通颜色
            background_color = parse_color(color_str)
            if background_color is None:  # transparent
                return base64_data
            background_image = Image.new("RGBA", image.size, background_color + (255,))

        # 如果原图有 alpha 通道，使用 alpha 通道作为蒙版
        if image.mode == "RGBA":
            background_image.paste(image, (0, 0), image)
        else:
            background_image.paste(image, (0, 0))

        return image_obj_to_base64(background_image)

    except Exception as e:
        print(f"Error in base64_set_bg: {str(e)}")
        return base64_data


def img_set_bg(image: Image, color_str):
    """设置图片背景色"""
    if color_str == "transparent":
        return image

    try:
        image = image.convert("RGBA")

        if color_str.startswith("linear-gradient"):
            # 解析渐变信息
            gradient_info = parse_gradient(color_str)
            if gradient_info:
                # 创建渐变背景
                background_image = create_gradient_background(image.size, gradient_info)
            else:
                # 如果渐变解析失败，使用第一个颜色作为纯色背景
                background_color = parse_color(color_str)
                background_image = Image.new(
                    "RGBA", image.size, background_color + (255,)
                )
        else:
            # 解析普通颜色
            background_color = parse_color(color_str)
            if background_color is None:  # transparent
                return image
            background_image = Image.new("RGBA", image.size, background_color + (255,))

        # 如果原图有 alpha 通道，使用 alpha 通道作为蒙版
        if image.mode == "RGBA":
            background_image.paste(image, (0, 0), image)
        else:
            background_image.paste(image, (0, 0))

        return background_image

    except Exception as e:
        print(f"Error in base64_set_bg: {str(e)}")
        return image


def base64_to_psd(base64_data, save_path, origin_image=None):
    image_data = base64.b64decode(base64_data.split(",")[1])
    image_obj = Image.open(BytesIO(image_data)).convert("RGBA")
    image_to_psd(image_obj, save_path, origin_image=origin_image)


def image_to_psd(image_obj: Image, save_path, origin_image=None):
    if image_obj.mode != "RGBA":
        image_obj = image_obj.convert("RGBA")

    psd = PSDImage.frompil(image_obj)
    if origin_image:
        pixel_layer_origin = PixelLayer.frompil(
            origin_image, psd, layer_name="Origin Image"
        )
        psd.append(pixel_layer_origin)
    # Create a new layer for the image
    pixel_layer = PixelLayer.frompil(image_obj, psd)
    # Set the pixel layer to be visible
    pixel_layer.visible = True

    psd.append(pixel_layer)

    psd.save(save_path)


def save_bs64_image(
    base64_data, hex_color, extension, origin_image, file_path, dpi=(72, 72)
):
    file_path = str(file_path)
    # 如果是psd格式，则需要先读取origin_image，使用read_image读取origin_image
    base64_data = base64_set_bg(base64_data, hex_color)
    if extension == "psd":
        origin_image, _ = read_image(origin_image)
        image_to_psd(base64_to_image_obj(base64_data), file_path, origin_image)
    else:
        # 如果hex_color是transparent且extension是jpg/jpeg，需要设置白色背景
        if hex_color == "transparent" and (
            extension.lower() == "jpg" or extension.lower() == "jpeg"
        ):
            image_obj = base64_to_image_obj(base64_data)
            # 创建白色背景
            white_bg = Image.new("RGBA", image_obj.size, (255, 255, 255, 255))
            # 将图像合成到白色背景上
            white_bg.paste(image_obj, (0, 0), image_obj)
            # 转换为RGB模式（去除alpha通道）
            white_bg = white_bg.convert("RGB")
            white_bg.save(file_path, "JPEG")
        else:
            base64_to_image(base64_data, file_path, dpi=dpi)


def remove_small_regions(
    image: Image.Image,
    min_size: int = 100,
    connectivity: int = 2,
    alpha_threshold: int = 128,
) -> Image.Image:
    """
    移除图像中的小型孤立区域，包括半透明区域

    Args:
        image: 输入的RGBA图片
        min_size: 最小保留区域大小（像素数），小于此值的区域将被移除
        connectivity: 连通性类型，1表示4连通，2表示8连通
        alpha_threshold: alpha通道的阈值，小于此值的像素将被视为背景

    Returns:
        清理后的图片
    """
    # 确保图片是RGBA模式
    if image.mode != "RGBA":
        image = image.convert("RGBA")

    # 获取alpha通道
    alpha = np.array(image.split()[3])

    # 对半透明区域进行预处理
    # 1. 首先用高斯模糊平滑alpha通道，减少噪点
    alpha_smooth = GaussianBlur(alpha, (3, 3), 0)

    # 2. 使用自适应阈值进行二值化，更好地处理半透明区域
    alpha_np = alpha_smooth > alpha_threshold

    # 标记连通区域
    structure = (
        np.ones((3, 3))
        if connectivity == 2
        else np.array([[0, 1, 0], [1, 1, 1], [0, 1, 0]])
    )
    labeled_array, num_features = ndimage.label(alpha_np, structure=structure)

    # 计算每个区域的大小
    component_sizes = np.bincount(labeled_array.ravel())

    # 创建掩码，标记要保留的区域
    keep_mask = component_sizes > min_size
    keep_mask[0] = False  # 确保背景不被保留

    # 应用掩码
    cleaned_alpha = keep_mask[labeled_array]

    # 创建新的清理后的图像
    cleaned_image = Image.new("RGBA", image.size, (0, 0, 0, 0))

    # 将清理后的mask应用到原始图像
    cleaned_alpha_img = Image.fromarray((cleaned_alpha * 255).astype(np.uint8))
    cleaned_image.paste(image, mask=cleaned_alpha_img)

    return cleaned_image


def crop_main_subject(
    image_path: str,
    image: Image.Image,
    aspect_ratio: Union[str, None] = None,
    target_size: Union[Tuple[int, int], None] = None,
    padding: int = 10,
    is_matting: bool = False,
) -> Image.Image:
    """
    裁剪图片主体并放置在指定尺寸的透明背景中。

    Args:
        image: 输入的RGBA图片
        aspect_ratio: 目标宽高比，格式为 "w:h"，如 "1:1", "3:4", "9:16"
        target_size: 目标尺寸 (width, height)，与 aspect_ratio 互
        padding: 边距像素值

    Returns:
        处理后的图片

    注意：必须指定 aspect_ratio 或 target_size 其中之一，但不能同时指定
    """
    if aspect_ratio == "custom" and target_size is None:
        raise ValueError("Must specify either aspect_ratio or target_size")
    if aspect_ratio != "custom" and target_size is not None:
        raise ValueError("Cannot specify both aspect_ratio and target_size")

    # 确保图片是RGBA模式
    if image.mode != "RGBA":
        image = image.convert("RGBA")

    # 获取主体区域的边界框
    alpha = image.split()[3]
    bbox = alpha.getbbox()
    if not bbox:
        return image

    # 计算主体区域的中心点和尺寸
    x1, y1, x2, y2 = bbox
    center_x = (x1 + x2) // 2
    center_y = (y1 + y2) // 2
    width = x2 - x1
    height = y2 - y1

    # 确保裁剪区域是正方形
    size = max(width, height)
    half_size = size // 2

    # 计算正方形裁剪区域
    crop_x1 = max(0, center_x - half_size)
    crop_y1 = max(0, center_y - half_size)
    crop_x2 = min(image.width, center_x + half_size)
    crop_y2 = min(image.height, center_y + half_size)
    if not is_matting:
        new_bbox = (
            max(0, bbox[0] - padding),
            max(0, bbox[1] - padding),
            min(image.width, bbox[2] + padding),
            min(image.height, bbox[3] + padding),
        )
        cropped = read_image(image_path)[0].crop(new_bbox)
        if aspect_ratio != "custom":
            # 解析宽高比
            croped_aspect_ratio = cropped.width / cropped.height

            w_ratio, h_ratio = map(int, aspect_ratio.split(":"))
            tart_aspect_ratio = w_ratio / h_ratio
            if croped_aspect_ratio > tart_aspect_ratio:
                # 图片比目标更宽，以宽度为准
                new_width = cropped.width
                new_height = int(cropped.width / tart_aspect_ratio)
            else:
                # 图片比目标更高，以高度为准
                new_height = cropped.height
                new_width = int(cropped.height * tart_aspect_ratio)

            # 把图像按照宽高比重置中
            cropped = cropped.resize((new_width, new_height), Image.LANCZOS)
        else:
            cropped = cropped.resize(target_size, Image.LANCZOS)
        return cropped

    cropped = image.crop((crop_x1, crop_y1, crop_x2, crop_y2))
    if aspect_ratio != "custom":
        # 解析宽高比
        w_ratio, h_ratio = map(int, aspect_ratio.split(":"))

        # 计算新的尺寸（包含padding）
        base_width = cropped.width + (padding * 2)
        base_height = int(base_width * h_ratio / w_ratio)

        # 创建目标尺寸的透明背景
        result = Image.new("RGBA", (base_width, base_height), (0, 0, 0, 0))

        # 计算粘贴位置（居中）
        paste_x = padding
        paste_y = (base_height - cropped.height) // 2

    else:  # target_size
        target_width, target_height = target_size

        # 计算缩放比例
        scale_w = (target_width - padding * 2) / cropped.width
        scale_h = (target_height - padding * 2) / cropped.height
        scale = min(scale_w, scale_h)

        # 缩放图片
        new_width = int(cropped.width * scale)
        new_height = int(cropped.height * scale)
        cropped = cropped.resize((new_width, new_height), Image.LANCZOS)

        # 创建目标尺寸的透明背景
        result = Image.new("RGBA", target_size, (0, 0, 0, 0))

        # 计算粘贴位置（居中）
        paste_x = (target_width - new_width) // 2
        paste_y = (target_height - new_height) // 2

    # 粘贴图片到透明背景上
    result.paste(cropped, (paste_x, paste_y))

    return result


def compute_sdf(mask: np.array) -> np.array:
    """计算二维图像的有向距离场 (SDF)"""
    binary = np.where(mask > 127, 1, 0).astype(np.uint8)
    dist_transform = cv2.distanceTransform(binary, cv2.DIST_L2, 3)
    dist_transform_inv = cv2.distanceTransform(1 - binary, cv2.DIST_L2, 3)
    sdf = dist_transform - dist_transform_inv
    return sdf


def draw_solid_stroke(
    image: Image.Image, stroke_width=2, stroke_color=(255, 0, 0)
) -> Image.Image:
    """绘制实线描边"""
    img_array = np.array(image)
    alpha = img_array[:, :, 3]
    sdf = compute_sdf(alpha)
    offset = stroke_width

    # 创建实线描边mask，使用高斯模糊实现平滑效果
    inner_mask = (sdf >= 0).astype(np.uint8) * 255
    outer_mask = (
        np.logical_and(
            abs(sdf + offset) <= stroke_width,  # 增加4个像素的范围用于平滑
            sdf < 2,  # 向内部延伸2个像素
        ).astype(np.uint8)
        * 255
    )

    # 应用高斯模糊实现平滑边缘
    stroke_mask = cv2.GaussianBlur(outer_mask, (5, 5), 1.5)
    inner_mask = cv2.GaussianBlur(inner_mask, (5, 5), 1.5)

    # 合并mask，确保内部不会被描边覆盖
    stroke_mask = np.where(inner_mask > 127, 0, stroke_mask)

    # 创建输出图像
    result = np.zeros((img_array.shape[0], img_array.shape[1], 4), dtype=np.uint8)
    result[:, :, :3] = np.array(stroke_color, dtype=np.uint8)[None, None, :]
    result[:, :, 3] = stroke_mask

    return Image.alpha_composite(Image.fromarray(result), image)


def draw_dashed_stroke(
    image: Image.Image, stroke_width=2, stroke_color=(255, 0, 0)
) -> Image.Image:
    """绘制虚线描边"""
    img_array = np.array(image)
    alpha = img_array[:, :, 3]
    sdf = compute_sdf(alpha)
    offset = stroke_width * 1.2

    # 创建基础mask
    binary = np.where(abs(sdf + offset) <= stroke_width / 2, 1, 0).astype(np.uint8)

    # 使用形态学操作平滑轮廓
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

    # 获取轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
    dot_mask = np.zeros_like(sdf)

    # 记录已绘制线段的中心点
    drawn_centers = set()
    min_distance = stroke_width * 3  # 最小间距

    for contour in contours:
        # 计算轮廓长度
        perimeter = cv2.arcLength(contour, True)

        # 基本参数设置
        dash_spacing = stroke_width * 4  # 点之间的间距
        dash_length = stroke_width * 2  # 虚线段的长度
        num_points = max(int(perimeter / dash_spacing), 1)

        # 均匀采样点
        for i in range(num_points):
            # 计算当前点的位置
            idx = int((i * len(contour)) / num_points)
            current_point = contour[idx][0]

            # 检查是否与已有点太近
            too_close = False
            for center in drawn_centers:
                dist = np.sqrt(
                    (current_point[0] - center[0]) ** 2
                    + (current_point[1] - center[1]) ** 2
                )
                if dist < min_distance:
                    too_close = True
                    break

            if too_close:
                continue

            # 计算下一个点以获取方向
            next_idx = int(((i + 1) * len(contour)) / num_points)
            next_point = contour[next_idx % len(contour)][0]

            # 计算方向向量
            dx = next_point[0] - current_point[0]
            dy = next_point[1] - current_point[1]
            length = np.sqrt(dx * dx + dy * dy)

            if length > 0:
                # 归一化方向向量
                dx = dx / length
                dy = dy / length

                # 计算虚线段的起点和终点
                dash_length = stroke_width * random.randint(1, 3)  # 调整长条长度
                start_x = int(current_point[0] - dx * dash_length * 0.5)
                start_y = int(current_point[1] - dy * dash_length * 0.5)
                end_x = int(current_point[0] + dx * dash_length * 0.5)
                end_y = int(current_point[1] + dy * dash_length * 0.5)

                # 确保坐标在图像范围内
                start_x = max(0, min(start_x, dot_mask.shape[1] - 1))
                start_y = max(0, min(start_y, dot_mask.shape[0] - 1))
                end_x = max(0, min(end_x, dot_mask.shape[1] - 1))
                end_y = max(0, min(end_y, dot_mask.shape[0] - 1))

                # 绘制小长条
                cv2.line(
                    dot_mask,
                    (start_x, start_y),
                    (end_x, end_y),
                    1,
                    thickness=stroke_width,
                    lineType=cv2.LINE_AA,
                )

                # 记录已绘制的中心点
                drawn_centers.add((current_point[0], current_point[1]))

    # 创建最终mask
    stroke_mask = (dot_mask * 255).astype(np.uint8)

    # 应用高斯模糊使边缘更平滑
    stroke_mask = cv2.GaussianBlur(stroke_mask, (3, 3), 0.5)

    # 创建输出图像
    result = np.zeros((img_array.shape[0], img_array.shape[1], 4), dtype=np.uint8)
    result[:, :, :3] = np.array(stroke_color, dtype=np.uint8)[None, None, :]
    result[:, :, 3] = stroke_mask

    # 合成最终图像
    final_result = Image.alpha_composite(Image.fromarray(result), image)

    # 添加细微的外描边效果
    outer_stroke = np.zeros_like(result)
    outer_stroke[:, :, :3] = np.array(stroke_color, dtype=np.uint8)[None, None, :]
    outer_stroke[:, :, 3] = cv2.dilate(stroke_mask, kernel, iterations=1)

    return Image.alpha_composite(Image.fromarray(outer_stroke), final_result)


def add_stroke(
    iamge_obj: Image.Image,
    stroke_width=2,
    stroke_color=(255, 0, 0),
    stroke_type="solid",
) -> Image.Image:
    """
    为图像添加描边并保存

    参数:
    stroke_width: 描边宽度
    stroke_color: 描边颜色 (R,G,B)
    stroke_type: 描边类型，"solid"表示实线，"dashed"表示虚线
    """
    stroke_color = parse_color(stroke_color)
    if stroke_type == "solid":
        result = draw_solid_stroke(iamge_obj, stroke_width, stroke_color)
    else:  # dashed
        result = draw_dashed_stroke(iamge_obj, stroke_width, stroke_color)

    return result


def get_image_format(image_path: str) -> str:
    """
    获取图片的真实格式。

    Args:
        image_path: 图片路径

    Returns:
        图片格式字符串(大写)，如 'JPEG', 'PNG' 等
    """
    try:
        with Image.open(image_path) as img:
            return img.format.upper()
    except Exception as e:
        raise ValueError(f"Failed to get image format: {str(e)}")


def process_gif(
    image_path: str,
    segment_func,
    is_edge_optimization: bool,
    edge_value: int,
    mode: str,
    remove_background: bool,
    payload: dict,
    save_path: str,
    background_color: str = None,
) -> str:
    """
    处理GIF图片的所有帧
    """
    # 打开GIF文件
    with Image.open(image_path) as gif:
        # 保存原始GIF的参数
        duration = gif.info.get("duration", 100)  # 默认100ms
        loop = gif.info.get("loop", 0)  # 0表示无限循环

        frames = []
        # 处理每一帧
        for frame in ImageSequence.Iterator(gif):
            # 将帧转换为RGBA模式并创建新的透明背景
            frame = frame.convert("RGBA")

            # # 创建一个全新的透明背景图像
            # transparent_bg = Image.new('RGBA', frame.size, (0, 0, 0, 0))
            # # 将当前帧合并到透明背景上
            # transparent_bg.paste(frame, (0, 0), frame.split()[3])

            # 保存当前帧到临时文件

            temp_buffer = io.BytesIO()
            frame.save(temp_buffer, format="PNG")
            # transparent_bg.save(temp_buffer, format='PNG')
            temp_buffer.seek(0)

            try:
                # 处理当前帧
                processed_frame = segment_func(
                    temp_buffer,
                    is_edge_optimization=is_edge_optimization,
                    edge_value=edge_value,
                )

                # 确保处理后的帧是RGBA模式
                if processed_frame.mode != "RGBA":
                    processed_frame = processed_frame.convert("RGBA")

                # 根据mode处理帧
                if mode == "stroke":
                    processed_frame = process_stroke_frame(
                        processed_frame, payload, remove_background, frame
                    )
                elif mode == "crop":
                    processed_frame = process_crop_frame(
                        processed_frame, payload, remove_background, image_path
                    )

                # 确保最终帧是P模式（带透明通道的调色板模式）
                if processed_frame.mode != "P":
                    processed_frame = processed_frame.convert("RGBA")
                    # 量化图像以减少颜色数量，同时保持透明度
                    processed_frame = processed_frame.quantize(
                        method=2, colors=255, dither=Image.FLOYDSTEINBERG
                    )
                if background_color and background_color != "transparent":
                    base_64_processed_frame = image_obj_to_base64(processed_frame)
                    base_64_processed_frame = base64_set_bg(
                        base_64_processed_frame, background_color
                    )
                    processed_frame = base64_to_image_obj(base_64_processed_frame)
                frames.append(processed_frame)
            except Exception as e:
                print(f"Frame processing error: {e}")
                continue

        if not frames:
            raise ValueError("No frames were successfully processed")

        # 创建新的GIF
        output_buffer = io.BytesIO()
        frames[0].save(
            output_buffer,
            format="GIF",
            save_all=True,
            append_images=frames[1:],
            duration=duration,
            loop=loop,
            optimize=False,
            disposal=2,  # 设置为2表示每帧都清除前一帧
            transparency=0,  # 设置透明色索引
        )
        # 转换为base64
        output_buffer.seek(0)
        # 保存为gif文件
        with open(save_path, "wb") as f:
            f.write(output_buffer.getvalue())
        return (
            "data:image/gif;base64,"
            + base64.b64encode(output_buffer.getvalue()).decode()
        )


def process_stroke_frame(frame, payload, remove_background, original_frame):
    """处理描边模式的单帧"""
    stroke_style = payload.get("stroke_style", "solid")
    stroke_color = payload.get("stroke_color", (255, 0, 0))
    stroke_width = payload.get("stroke_width", 2)

    if stroke_style == "solid":
        stroke_image = add_stroke(
            frame,
            stroke_width=stroke_width,
            stroke_color=stroke_color,
            stroke_type="solid",
        )
    else:
        stroke_image = add_stroke(
            frame,
            stroke_width=stroke_width,
            stroke_color=stroke_color,
            stroke_type="dashed",
        )

    if not remove_background:
        # 将描边图像合成到原始帧上
        original_frame.paste(stroke_image, (0, 0), stroke_image)
        return original_frame
    return stroke_image


def process_crop_frame(frame, payload, remove_background, image_path):
    """处理裁剪模式的单帧"""
    crop_ratio = payload.get("crop_ratio", "1:1")
    subject_retention = payload.get("subject_retention", True)

    if crop_ratio == "custom":
        width = payload.get("width", 1024)
        height = payload.get("height", 1024)
        return crop_main_subject(
            image_path,
            frame,
            crop_ratio,
            target_size=(width, height),
            padding=subject_retention,
            is_matting=remove_background,
        )
    else:
        return crop_main_subject(
            image_path,
            frame,
            crop_ratio,
            padding=subject_retention,
            is_matting=remove_background,
        )


def remove_small_regions(
    image: Image.Image,
    min_size: int = 100,
    connectivity: int = 2,
    alpha_threshold: int = 128,
) -> Image.Image:
    """
    移除图像中的小型孤立区域，包括半透明区域

    Args:
        image: 输入的RGBA图片
        min_size: 最小保留区域大小（像素数），小于此值的区域将被移除
        connectivity: 连通性类型，1表示4连通，2表示8连通
        alpha_threshold: alpha通道的阈值，小于此值的像素将被视为背景

    Returns:
        清理后的图片
    """
    # 确保图片是RGBA模式
    if image.mode != "RGBA":
        image = image.convert("RGBA")

    # 获取alpha通道
    alpha = np.array(image.split()[3])

    # 对半透明区域进行预处理
    # 1. 首先用高斯模糊平滑alpha通道，减少噪点
    alpha_smooth = cv2.GaussianBlur(alpha, (3, 3), 0)

    # 2. 使用自适应阈值进行二值化，更好地处理半透明区域
    alpha_np = alpha_smooth > alpha_threshold

    # 标记连通区域
    structure = (
        np.ones((3, 3))
        if connectivity == 2
        else np.array([[0, 1, 0], [1, 1, 1], [0, 1, 0]])
    )
    labeled_array, num_features = ndimage.label(alpha_np, structure=structure)

    # 计算每个区域的大小
    component_sizes = np.bincount(labeled_array.ravel())

    # 创建掩码，标记要保留的区域
    keep_mask = component_sizes > min_size
    keep_mask[0] = False  # 确保背景不被保留

    # 应用掩码
    cleaned_alpha = keep_mask[labeled_array]

    # 创建新的清理后的图像
    cleaned_image = Image.new("RGBA", image.size, (0, 0, 0, 0))

    # 将清理后的mask应用到原始图像
    cleaned_alpha_img = Image.fromarray((cleaned_alpha * 255).astype(np.uint8))
    cleaned_image.paste(image, mask=cleaned_alpha_img)

    return cleaned_image


def calculate_sha256(file_path):
    """计算文件的 SHA256 哈希值"""
    sha256 = hashlib.sha256()
    with open(file_path, "rb") as f:
        # 逐块读取文件并更新哈希值
        for chunk in iter(lambda: f.read(4096), b""):
            sha256.update(chunk)
    return sha256.hexdigest()


def is_video_file(file_path: str) -> bool:
    """检查文件是否为视频文件"""
    return file_path.lower().endswith(
        (".mp4", ".avi", ".mkv", ".mov", ".flv", ".wmv", ".webm")
    )


def calculate_md5(file_path: str, chunk_size: int = 8192) -> str:
    """
    快速、高效地计算一个文件的MD5哈希值。

    通过分块读取文件，此函数可以处理非常大的文件而不会消耗大量内存。

    Args:
        file_path (str): 目标文件的路径。
        chunk_size (int, optional): 每次读取的块大小（字节）。
                                    默认为 8192 字节（8KB）。

    Returns:
        str: 文件的MD5哈希值（32个字符的十六进制字符串）。

    Raises:
        FileNotFoundError: 如果指定的文件路径不存在。
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"错误: 文件未找到 '{file_path}'")

    md5_hash = hashlib.md5()
    
    with open(file_path, "rb") as f:
        while (chunk := f.read(chunk_size)):
            md5_hash.update(chunk)
            
    return md5_hash.hexdigest()