import dns.resolver
import os
import re
from typing import Tuple

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))


def is_valid_email_format(email: str) -> bool:
    """
    Check if the email format is valid using regex pattern.

    Args:
        email: Email address to validate

    Returns:
        bool: True if email format is valid, False otherwise
    """
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return bool(re.match(pattern, email))


def is_disposable(email: str) -> bool:
    """
    Check if the email domain is from a disposable email service.

    Args:
        email: Email address to check

    Returns:
        bool: True if email is from disposable domain, False otherwise
    """
    # Load disposable email domains from file
    with open(os.path.join(BASE_DIR, "assets", "domains.txt"), "r") as f:
        disposable_domains = set(line.strip().lower() for line in f)
    domain = email.split("@")[-1].lower()
    return domain in disposable_domains


def has_valid_mx(domain: str) -> bool:
    """
    Verify if the domain has valid MX records.

    Args:
        domain: Domain name to check

    Returns:
        bool: True if domain has valid MX records, False otherwise
    """
    try:
        answers = dns.resolver.resolve(domain, "MX")
        return len(answers) > 0
    except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer, dns.resolver.NoNameservers):
        return False
    except Exception:
        return False


def validate_email(email: str) -> Tuple[bool, str]:
    """
    Comprehensive email validation including format, disposable check and MX record verification.

    Args:
        email: Email address to validate

    Returns:
        Tuple[bool, str]: (is_valid, error_message)
    """
    try:
        # Convert email to lowercase for consistent validation
        email = email.lower().strip()

        # Check basic email format
        if not is_valid_email_format(email):
            return False, "Invalid email format"

        # Check for disposable email
        if is_disposable(email):
            return False, "Disposable email addresses are not allowed"

        # Verify MX records
        domain = email.split("@")[-1]
        if not has_valid_mx(domain):
            return False, f"Domain '{domain}' does not have valid MX records"

        return True, "Email is valid"

    except Exception as e:
        return False, f"Validation error: {str(e)}"


if __name__ == "__main__":
    print(validate_email("<EMAIL>"))
    print(validate_email("<EMAIL>"))
    print(validate_email("<EMAIL>"))
    print(validate_email("<EMAIL>"))
    print(validate_email("<EMAIL>"))
