import struct
import time
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend
import platform
import ctypes
import sys
import os
from enum import Enum


class LicenseType(Enum):
    """授权类型"""

    PERMANENT = 0  # 永久授权
    TIME_LIMITED = 1  # 限时授权


class SecureLicenseVerifier:
    # 文件格式版本
    VERSION = 1
    # 文件魔数
    MAGIC = b"SLIC"

    def __init__(self, public_key_path=None):
        """初始化验证器
        :param public_key_path: 公钥文件路径
        """
        self._load_public_key(public_key_path)
        self._check_debugger()
        self._init_security()

    def _check_debugger(self):
        """检测调试器"""
        if platform.system() == "Windows":
            if ctypes.windll.kernel32.IsDebuggerPresent():
                sys.exit(1)

    def _init_security(self):
        """初始化安全环境"""
        # 混淆关键数据
        self._key_data = bytes(
            [
                x ^ 0x55
                for x in self.public_key.public_bytes(
                    encoding=serialization.Encoding.DER,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo,
                )
            ]
        )

    def _unpack_license_data(self, decrypted_data):
        """解包授权数据"""
        try:
            # 首先读取机器码长度（前2个字节）
            machine_code_length = struct.unpack("!H", decrypted_data[:2])[0]

            # 计算完整的格式字符串
            format_str = f"!H {machine_code_length}s B Q Q"
            total_size = struct.calcsize(format_str)

            # 解析所有数据
            length, machine_code, license_type, created_at, expiry = struct.unpack(
                format_str, decrypted_data[:total_size]
            )

            return {
                "machine_code": machine_code.decode("utf-8"),
                "license_type": LicenseType(license_type),
                "created_at": created_at,
                "expiry_date": expiry,
            }
        except Exception as e:
            raise

    def _load_public_key(self, public_key_path):
        """加载公钥"""
        if public_key_path is None:
            public_key_str = """
            -----BEGIN PUBLIC KEY-----
            MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwyrfC7mtQjITpkATljoF
            y6SQPp5wOB7HMBzK9sDUM1CYDUAcaZo4Sc2PYNCQvhQ5O06zFiUf6rPPDDq7EoP3
            +EiYCAfZrD4zJoCDXEugbZBGvDnaxXVRM/cUy6NwvxtUMb8zMVUK7I8fURLl5joI
            G8Wy3USXwyzUtllATSPcVWkg5f8sQ0w2EaD6Kz6xX+ot3ClY15vcEUPsBWlY/dEo
            8GtDnBJVxOv91f5wzMBN/o9G/hI8hHzfegIwvZNnkRkKUmF9Pj7M1yZFU6ycgcCx
            yDb1DQOnOqWo2wOobEY4IDKSRST3ukAb4/Akt9NUF3TsgWPQykC++GzB/u0X4L9i
            dwIDAQAB
            -----END PUBLIC KEY-----
            """
            self.public_key = serialization.load_pem_public_key(
                public_key_str.encode(), backend=default_backend()
            )
        else:
            with open(public_key_path, "rb") as key_file:
                self.public_key = serialization.load_pem_public_key(
                    key_file.read(), backend=default_backend()
                )

    def verify_license(self, license_data, machine_code):
        """验证授权文件"""
        try:
            # 如果输入是文件路径，读取文件内容
            if isinstance(license_data, str):
                with open(license_data, "rb") as f:
                    license_data = f.read()

            # 解析文件头
            header_format = (
                "4s H H I I"  # 修改格式：魔数(4) + 版本(2) + 保留字节(2) + 签名长度(4) + 数据长度(4)
            )
            header_size = struct.calcsize(header_format)
            magic, version, reserved, signature_size, data_size = struct.unpack(
                header_format, license_data[:header_size]
            )
            # 验证文件格式
            if magic != self.MAGIC:
                return False, "无效的授权文件格式"
            if version != self.VERSION:
                return False, "不支持的授权文件版本"

            # 提取签名和数据
            pos = header_size
            signature = license_data[pos : pos + signature_size]
            pos += signature_size
            license_content = license_data[pos : pos + data_size]
            # 使用公钥验证签名
            try:
                self.public_key.verify(
                    signature,
                    license_content,
                    padding.PSS(
                        mgf=padding.MGF1(hashes.SHA256()),
                        salt_length=padding.PSS.MAX_LENGTH,
                    ),
                    hashes.SHA256(),
                )
            except Exception as e:
                return False, "签名验证失败，授权文件可能被篡改"

            # 解析授权数据
            license_info = self._unpack_license_data(license_content)

            # 验证机器码
            if license_info["machine_code"] != machine_code:
                self._log_verification("机器码不匹配", machine_code)
                return False, "机器码不匹配"

            # 根据授权类型验证
            if license_info["license_type"] == LicenseType.TIME_LIMITED:
                if (
                    time.time() > license_info["expiry_date"]
                    or int(license_info["created_at"]) > time.time()
                ):
                    self._log_verification("授权已过期", machine_code)
                    return False, "授权已过期"
                remaining_days = (license_info["expiry_date"] - time.time()) / (
                    24 * 60 * 60
                ) + 1
                self._log_verification(f"验证通过，剩余 {int(remaining_days)} 天", machine_code)
                return True, f"验证通过，剩余 {int(remaining_days)} 天"
            else:
                self._log_verification("验证通过（永久授权）", machine_code)
                return True, "验证通过（永久授权）"

        except Exception as e:
            self._log_verification(f"验证过程出现异常: {str(e)}", machine_code)
            return False, f"验证过程出错: {str(e)}"

    def _log_verification(self, result, machine_code):
        """记录验证结果"""
        try:
            log_dir = os.path.join(os.path.expanduser("~"), ".lx_cache", "logs")
            os.makedirs(log_dir, exist_ok=True)

            log_file = os.path.join(log_dir, "verification.log")
            with open(log_file, "a") as f:
                f.write(f"{time.time()},{machine_code},{result}\n")
        except:
            pass


if __name__ == "__main__":
    pass
