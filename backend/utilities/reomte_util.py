# 实现远程服务交互，报告登录、注册、获取授权、状态查询

import requests
import base64
import json
from cryptography.fernet import Fernet
from typing import Optional
import base64
import time


class FernetCrypto:
    """
    使用 Fernet 实现的对称加密类
    基于 AES-128-CBC 的加密方案，使用固定密钥
    """

    # 固定的 Fernet 密钥 (32 字节的 base64 编码字符串)
    # 在实际应用中，建议将密钥存储在环境变量或配置文件中
    FIXED_KEY = b"ul6Dgvrt4RPLciao3eoiOj6Y_Z2hSYB8coO0hd96MBs="  # 请替换为你的固定密钥

    def __init__(self):
        """
        初始化 Fernet 加密器
        """
        try:
            self.fernet = Fernet(self.FIXED_KEY)
        except Exception as e:
            raise ValueError(f"初始化 Fernet 失败: {str(e)}")

    def decrypt(self, encrypted_data: str) -> Optional[str]:
        """
        解密加密后的数据

        Args:
            encrypted_data: 加密后的 base64 编码字符串

        Returns:
            解密后的原始字符串

        Raises:
            ValueError: 当输入数据无效或解密失败时
        """
        try:
            if not encrypted_data:
                raise ValueError("解密数据不能为空")

            # 将 base64 字符串转换回字节
            encrypted_bytes = base64.b64decode(encrypted_data.encode("utf-8"))
            # 解密数据
            decrypted_data = self.fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode("utf-8")
        except Exception as e:
            raise ValueError(f"解密失败: {str(e)}")


fernet_crypto = FernetCrypto()


def parse_jwt_token(token):
    """解析jwt token"""
    token = fernet_crypto.decrypt(token)
    payload = token.split(".")[1]
    payload = base64.b64decode(payload)
    return json.loads(payload)


class RemoteUtil:
    def __init__(self, url):
        self.url = url
        self.headers = {
            "Content-Type": "application/json",
            "X-API-Source": "lxt-client",
        }

    def login(self, email, password):
        """登录"""
        url = f"{self.url}/api/v1/auth/login"
        data = {"email": email, "password": password}
        response = requests.post(url, json=data, headers=self.headers, timeout=10)
        return response.json()

    def register(self, email, password, invite_code, machine_code):
        """注册"""
        url = f"{self.url}/api/v1/auth/register"
        if invite_code:
            data = {
                "email": email,
                "password": password,
                "invite_code": invite_code,
                "machine_code": machine_code,
            }
        else:
            data = {"email": email, "password": password, "machine_code": machine_code}
        response = requests.post(url, json=data, headers=self.headers, timeout=10)
        return response.json()

    def get_user_info(self, token):
        """获取用户信息"""
        url = f"{self.url}/api/v1/users/info"
        self.headers["Authorization"] = f"Bearer {token}"
        response = requests.get(url, headers=self.headers, timeout=10)
        return response.json()

    # /api/v1/membership/trial
    def activate_trial(self, token):
        """试用"""
        url = f"{self.url}/api/v1/membership/trial"
        self.headers["Authorization"] = f"Bearer {token}"
        response = requests.post(url, headers=self.headers, timeout=10)
        return response.json()

    # /api/v1/membership/order
    def create_order(self, token, plan_id, pay_channel):
        """下单"""
        url = f"{self.url}/api/v1/membership/order"
        self.headers["Authorization"] = f"Bearer {token}"
        data = {"plan_id": plan_id, "pay_channel": pay_channel}
        response = requests.post(url, json=data, headers=self.headers, timeout=10)
        return response.json()

    def check_order_status(self, token, order_id):
        """检查订单状态"""
        url = f"{self.url}/api/v1/membership/order/check"
        self.headers["Authorization"] = f"Bearer {token}"
        parames = {"order_id": order_id}
        response = requests.get(
            url=url, params=parames, headers=self.headers, timeout=10
        )
        return response.json()

    def parser_jwt_token(self, token):
        """解析jwt token"""
        payload = token.split(".")[1]
        payload = base64.b64decode(payload)
        return json.loads(payload)

    # /api/v1/membership/plans
    def get_plans(self, token):
        """获取套餐"""
        url = f"{self.url}/api/v1/membership/plans"
        self.headers["Authorization"] = f"Bearer {token}"
        response = requests.get(url, headers=self.headers, timeout=10)
        return response.json()

    # /api/v1/membership/orders
    def get_orders(self, token, page, page_size):
        """获取订单"""
        url = f"{self.url}/api/v1/membership/orders"
        self.headers["Authorization"] = f"Bearer {token}"
        params = {"page": page, "page_size": page_size}
        response = requests.get(url, headers=self.headers, params=params, timeout=10)
        return response.json()
