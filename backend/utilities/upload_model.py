from pathlib import Path
import sys
import boto3
from botocore.exceptions import ClientError

BASE_DIR = Path(__file__).resolve().parent.parent

try:
    from utilities.log import logger
except ImportError:
    sys.path.append(str(BASE_DIR))
    from utilities.log import logger


def upload_model_to_r2(
    file_path: str, object_key: str, bucket_name: str = "lingxiangtools"
):
    """
    上传模型文件到R2存储
    Args:
        file_path: 本地文件路径
        object_key: 在R2中的文件名
        bucket_name: 桶名称，默认为lingxiangtools
    """
    # 配置 Cloudflare R2 连接
    s3 = boto3.client(
        "s3",
        endpoint_url="",
        aws_access_key_id="",
        aws_secret_access_key="",
        region_name="auto",
    )

    try:
        file_path = Path(file_path)
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 使用 upload_file 方法上传文件
        s3.upload_file(
            str(file_path),
            bucket_name,
            object_key,
        )
        logger.info(f"文件 {file_path.name} 上传成功！")
        return True

    except ClientError as e:
        error_code = e.response["Error"]["Code"]
        error_messages = {
            "403": "认证失败",
            "NoSuchBucket": "存储桶不存在",
            "InvalidAccessKeyId": "无效的访问密钥",
            "SignatureDoesNotMatch": "签名不匹配",
        }
        error_msg = error_messages.get(error_code, "未知错误")
        logger.error(f"上传失败 ({error_code}): {error_msg}")
        logger.error(e)
        return False
    except Exception as e:
        logger.error(f"上传过程中发生错误: {str(e)}")
        return False


if __name__ == "__main__":
    # 使用示例
    model_path = "epoch_100.onnx"
    upload_model_to_r2(model_path, "epoch_100.onnx")
