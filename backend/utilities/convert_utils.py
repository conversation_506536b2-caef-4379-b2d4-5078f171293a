from PIL import Image
import os
from pathlib import Path


def can_convert_file(filename):
    """
    ["PNG", "JPEG", "GIF", "BMP", "WEBP", "ICO", "ICNS", "TIFF"]
    """
    return any(
        filename.lower().endswith(ext)
        for ext in [
            ".png",
            ".jpg",
            ".jpeg",
            ".gif",
            ".bmp",
            ".webp",
            ".ico",
            ".icns",
            ".tiff",
        ]
    )


def get_file_base_name(file_path):
    base_name = Path(file_path).stem
    return base_name


def gif_to_image(gif_path, output_format, output_path):
    if os.path.isfile(output_path):
        output_path = os.path.dirname(output_path)
    if not os.path.exists(output_path):
        os.makedirs(output_path, exist_ok=True)
    with Image.open(gif_path) as gif:
        # 获取文件名（不包括扩展名）和目录
        base_name = os.path.basename(output_path)
        for frame in range(gif.n_frames):
            gif.seek(frame)
            # 创建新的文件名，包含帧序号
            frame_filename = f"{base_name}_frame_{frame:03d}.{output_format.lower()}"
            frame_path = os.path.join(output_path, frame_filename)

            # 如果输出格式是JPEG，需要转换为RGB模式
            if output_format.lower() in ["jpg", "jpeg"]:
                gif.convert("RGB").save(frame_path, output_format.upper())
            else:
                gif.save(frame_path, output_format.upper())
    return True


def convert_image_format(input_path, output_format, output_path=None, quality=None):
    # 打开原始图片
    image = Image.open(input_path)

    # 特殊处理 ICNS 和 ICO 源文件
    if input_path.lower().endswith((".icns", ".ico")):
        try:
            # 获取最大尺寸的图标
            largest_size = max(image.size)
            image = image.resize((largest_size, largest_size))

            # 如果是带透明通道的图标，保持 RGBA 模式
            if image.mode == "RGBA":
                image = image.convert("RGBA")
            else:
                image = image.convert("RGB")
        except Exception as e:
            print(f"Error processing icon file: {str(e)}")
            return False

    # 确保输出格式合法并转换格式
    try:
        if output_format.lower() in ["jpg", "jpeg"]:
            # JPEG 不支持透明通道，需要转换为 RGB
            image = image.convert("RGB")
            if quality:
                image.save(output_path, "JPEG", quality=quality)
            else:
                image.save(output_path, "JPEG")

        elif output_format.lower() == "png":
            # PNG 支持透明通道，保持 RGBA 模式
            if image.mode == "RGBA":
                image.save(output_path, "PNG")
            else:
                image.convert("RGB").save(output_path, "PNG")

        elif output_format.lower() == "gif":
            # GIF 支持透明通道
            image.save(output_path, "GIF")

        elif output_format.lower() == "bmp":
            # BMP 通常不支持透明通道
            image.convert("RGB").save(output_path, "BMP")

        elif output_format.lower() == "ico":
            image.save(output_path, "ICO")

        elif output_format.lower() == "icns":
            image.save(output_path, "ICNS")

        elif output_format.lower() == "webp":
            # WebP 支持透明通道
            if quality:
                image.save(output_path, "WEBP", quality=quality)
            else:
                image.save(output_path, "WEBP")

        elif output_format.lower() == "pdf":
            # PDF 通常不需要透明通道
            image.convert("RGB").save(output_path, "PDF")

        elif output_format.lower() == "tiff":
            # TIFF 支持透明通道
            image.save(output_path, "TIFF")

        else:
            return False

        return True

    except Exception as e:
        print(f"Error converting image: {str(e)}")
        return False
