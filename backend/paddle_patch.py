import os
import sys
import site

# 确保 site.USER_SITE 不为 None
if site.USER_SITE is None:
    site.USER_SITE = ""

# 如果在打包环境中
if getattr(sys, "frozen", False):
    # 修补 paddle.fluid.core 模块
    def patch_paddle():
        bundle_dir = os.path.dirname(sys.executable)
        paddle_dir = os.path.join(bundle_dir, "paddle")

        # 设置环境变量
        os.environ["PADDLE_ROOT"] = paddle_dir
        os.environ["PADDLE_BINARY_PATH"] = os.path.join(paddle_dir, "libs")

        # 修改 sys.modules 中的 paddle.fluid.core
        if "paddle.fluid.core" in sys.modules:
            core_module = sys.modules["paddle.fluid.core"]

            def patched_set_paddle_lib_path():
                return os.path.join(paddle_dir, "libs")

            core_module.set_paddle_lib_path = patched_set_paddle_lib_path

    patch_paddle()
