# -*- mode: python ; coding: utf-8 -*-
import sys
import argparse

parser = argparse.ArgumentParser()
parser.add_argument("--debug", action="store_true")
options = parser.parse_args()

block_cipher = None

# 分析步骤，收集所需的文件和依赖项
a = Analysis(
    ["main.py"],
    pathex=[],
    binaries=[
        ('./pngquant/pngquant.exe', './pngquant')
    ],
    datas=[
        ('./web', 'web'),  # 收集 web 目录
        ('./assets', 'assets'),
        ('./.venv/lib/site-packages/paddleocr', 'paddleocr'),
        ('./api/*.pyd', 'api'),
        ('./ai_tools/*.pyd', 'ai_tools'),
        ('./conf/*.pyd', 'conf'),
        ('./utilities/*.pyd', 'utilities'),  
    ],
    #  'api', 'ai_tools', 'conf', 'utilities',
    hiddenimports=[
        'api',
        'utilities.log',
        'utilities.response',
        'loguru', 'cryptography', 'boto3','wmi',
        'shapely', 'pyclipper', 'imghdr', 'imgaug', 'lmdb', 'beautifulsoup4',
        'albumentations', 'albucore', 'fire', 'fonttools', 'rapidfuzz', 'tqdm', 'docx', 
        'paddle',
        'paddleocr',
        'requests',
        'psd_tools',
        'pydantic_settings',
        'ffmpeg',
        'onnxruntime',
        'cryptography.fernet',
        'dns.resolver',
        'scenedetect',
        'scenedetect.detectors',
        'scenedetect.video_splitter'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 根据是否为调试模式设置不同的 EXE 和 COLLECT 参数
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name="灵象工具箱",
    debug=options.debug,  # 设置调试模式
    bootloader_ignore_signals=False,
    strip=False,  # 非调试模式时移除符号表
    upx=not options.debug,  # 非调试模式时使用 UPX 压缩
    console=options.debug,  # 调试模式时显示控制台窗口
    disable_windowed_traceback=not options.debug,  # 非调试模式时禁用窗口化的回溯
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon="assets/logo.ico",
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=not options.debug,  # 非调试模式时使用 UPX 压缩
    upx_exclude=[],
    name="灵象工具箱_debug" if options.debug else "灵象工具箱",
)
