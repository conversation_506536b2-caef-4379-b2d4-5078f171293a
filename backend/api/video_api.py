from ai_tools.ffmpeg_tool import FFmpegTool
from ai_tools.migan import MiganInpainting
from ai_tools.lama import LamaInpainting
from ai_tools.ppocr_tool import PPOCR
from utilities.log import logger
from utilities.response import res200, res500
from pathlib import Path
import os
from PIL import Image
from utilities.utils import read_image
import time
from api.utils import get_segment_func
from utilities.utils import img_set_bg


def process_frame_batch(args):
    """批量处理帧的函数"""
    frame_paths, mask_image_path, output_paths, progress_dict = args
    try:
        with MiganInpainting() as migan:
            results = []
            for i, (frame_path, output_path) in enumerate(
                zip(frame_paths, output_paths)
            ):
                frame_img = Image.open(frame_path)
                mask_img = Image.open(mask_image_path)
                processed_frame = migan.process_image(frame_img, mask_img, 512)
                processed_frame.save(output_path, "JPEG", quality=95, optimize=True)
                results.append(True)

                # 更新进度
                progress_dict["completed"] += 1

            return results
    except Exception as e:
        logger.error(f"Error processing frame batch: {e}")
        return [False] * len(frame_paths)


class VideoDeWatermarkAPI:
    name = "video_de_watermark"

    def get_frame_image(self, payload):
        """获取指定时间点的视频帧"""
        try:
            base64_data = FFmpegTool().get_frame_base64(
                payload["video_path"], payload["time_position"]
            )
            return res200(data={"frame_base64": base64_data})
        except Exception as e:
            logger.error(f"Error in get_frame_image: {e}")
            return res500(str(e))

    def process_video_with_mask(self, payload):
        """处理带水印的视频 - 流式处理"""
        try:
            video_path = payload["video_path"]
            selected_model = payload["model_type"]

            self.notify_progress({"progress": 0, "message": "正在分析视频..."})
            video_info = FFmpegTool().get_video_info(video_path)

            self.notify_progress({"progress": 10, "message": "正在初始化AI模型..."})

            mask_data = payload.get("mask_data", None)
            mask_image, _ = read_image(mask_data["maskBase64"])

            if selected_model == "fast":
                inpainting_model = MiganInpainting()
            elif selected_model == "quality":
                inpainting_model = LamaInpainting()
            else:
                return res500("Invalid model type")

            def process_frame(frame_img):
                return inpainting_model.inpaint(frame_img, mask_image)

            output_path = str(
                Path(video_path).parent
                / f"{Path(video_path).stem}_processed_{time.strftime('%Y%m%d%H%M%S')}{Path(video_path).suffix}"
            )

            self.notify_progress({"progress": 10, "message": "正在处理视频帧..."})
            FFmpegTool().process_video_stream(
                video_path,
                output_path,
                process_frame,
                video_info,
                source_video=video_path,
                notify_callback=self.notify_progress,
            )

            self.notify_progress({"progress": 100, "message": "处理完成"})
            return res200(data={"output_path": output_path})

        except Exception as e:
            logger.error(f"Error in process_video_with_mask: {e}")
            self.notify_progress({"progress": 0, "message": f"处理失败: {str(e)}"})
            return res500(str(e))

    def get_video_info(self, video_path: str):
        try:
            data = FFmpegTool().get_video_info(video_path)
            return res200(data=data)
        except Exception as e:
            return res500(f"Error in video_info: {e}")

    def auto_remove_watermark(self, payload):
        """自动去除视频中的水印 - 流式处理"""
        try:
            video_path = payload["video_path"]
            model_type = payload["model_type"]

            self.notify_progress({"progress": 0, "message": "正在分析视频..."})
            video_info = FFmpegTool().get_video_info(video_path)

            self.notify_progress({"progress": 10, "message": "正在初始化AI模型..."})

            if model_type == "fast":
                inpainting_model = MiganInpainting()
                add_box = 3
            elif model_type == "quality":
                inpainting_model = LamaInpainting()
                add_box = 2
            else:
                return res500("Invalid model type")
            ppocr_tool = PPOCR()

            def process_frame(frame_img):
                mask_image = ppocr_tool.ppocr_mask_from_image(
                    frame_img, add_box=add_box
                )
                return inpainting_model.inpaint(frame_img, mask_image)

            output_path = str(
                Path(video_path).parent
                / f"{Path(video_path).stem}_auto_processed_{time.strftime('%Y%m%d%H%M%S')}{Path(video_path).suffix}"
            )

            self.notify_progress({"progress": 10, "message": "正在处理视频帧..."})

            FFmpegTool().process_video_stream(
                video_path,
                output_path,
                process_frame,
                video_info,
                source_video=video_path,
                notify_callback=self.notify_progress,
            )

            self.notify_progress({"progress": 100, "message": "处理完成"})
            return res200(data={"output_path": output_path})
        except Exception as e:
            logger.error(f"Error in auto_remove_watermark: {e}")
            self.notify_progress({"progress": 0, "message": f"处理失败: {str(e)}"})
            return res500(str(e))

    def auto_matting_video(self, payload):
        """视频抠图 - 流式处理"""
        try:
            video_path = payload["video_path"]
            ai_model = payload["ai_model"]
            is_edge_optimization = payload.get("is_edge_optimization", False)
            edge_value = payload.get("edge_value", 90)
            background_color = payload.get("background_color", "transparent")
            background_color_image = payload.get("background_color_image", "")

            self.notify_progress({"progress": 0, "message": "正在分析视频..."})
            video_info = FFmpegTool().get_video_info(video_path)

            self.notify_progress({"progress": 10, "message": "正在初始化AI模型..."})

            segment_func = get_segment_func(ai_model)
            bg_img = None
            if background_color_image:
                try:
                    bg_img, _ = read_image(background_color_image)
                    # Pre-resize background to match video dimensions
                    bg_img = bg_img.resize(
                        (video_info["width"], video_info["height"]), Image.LANCZOS
                    )
                except Exception as e:
                    logger.error(
                        f"Error processing background image, falling back to color: {e}"
                    )
                    bg_img = None

            def process_frame(frame_img):
                no_bg_image = segment_func(
                    frame_img,
                    is_edge_optimization=is_edge_optimization,
                    edge_value=edge_value,
                )

                if bg_img:
                    processed_frame = Image.new("RGBA", frame_img.size, (0, 0, 0, 0))
                    processed_frame.paste(bg_img, (0, 0))
                    processed_frame.paste(no_bg_image, (0, 0), no_bg_image)
                    return processed_frame
                elif background_color == "transparent":
                    return no_bg_image  # Must be RGB for ffmpeg
                else:
                    return img_set_bg(no_bg_image, background_color)

            output_path = str(
                Path(video_path).parent
                / f"{Path(video_path).stem}_matted_{time.strftime('%Y%m%d%H%M%S')}.mp4"  # output as mp4
            )

            self.notify_progress({"progress": 10, "message": "正在处理视频帧..."})
            FFmpegTool().process_video_stream(
                video_path,
                output_path,
                process_frame,
                video_info,
                source_video=video_path,
                notify_callback=self.notify_progress,
            )

            self.notify_progress({"progress": 100, "message": "处理完成"})
            return res200(data={"output_path": output_path})
        except Exception as e:
            import traceback

            traceback.print_exc()
            logger.error(f"Error in auto_matting_video: {e}")
            self.notify_progress({"progress": 0, "message": f"处理失败: {str(e)}"})
            return res500(str(e))
