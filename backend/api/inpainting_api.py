import time
from utilities.utils import (
    image_obj_to_base64,
    base64_to_image_obj,
    read_image,
    get_image_format,
)
from utilities.log import logger
from utilities.response import res200, res500
from ai_tools.migan import MiganInpainting
from ai_tools.lama import LamaInpainting
from datetime import datetime
from pathlib import Path
from PIL import Image
import os


class InpaintingAPI:
    name = "inpainting"

    def __init__(self):
        pass

    def inpainting(self, payload: dict) -> dict:
        origin_image = payload.get("origin_image")
        mask_base64 = payload.get("mask_base64")
        start_time = time.time()
        model_type = payload.get("model_type", "fast")
        try:
            origin_image, _ = read_image(origin_image)
            mask_image, _ = read_image(mask_base64)
            if model_type == "fast":
                inpainting_model = MiganInpainting()
            elif model_type == "quality":
                inpainting_model = LamaInpainting()
            else:
                return res500("Invalid model type")
            processed_frame = inpainting_model.inpaint(origin_image, mask_image)
            result_base64 = image_obj_to_base64(processed_frame)
            logger.info(f"Inpainting image time: {time.time() - start_time}")
            return res200(data={"result_base64": result_base64})
        except Exception as e:
            logger.error(f"Error in inpainting: {e}")
            return res500("Error in inpainting")

    def batch_process(self, payload):
        """批量处理图片"""
        try:
            output_folder = payload["output_folder"]
            mask_base64 = payload["mask_base64"]
            model_type = payload["model_type"]
            image_list = payload["image_list"]

            # 确保输出目录存在
            os.makedirs(output_folder, exist_ok=True)

            # 读取蒙版图片
            mask_image, _ = read_image(mask_base64)

            # 选择模型
            if model_type == "fast":
                inpainting_model = MiganInpainting()
            else:
                inpainting_model = LamaInpainting()

            total_files = len(image_list)
            processed_files = []

            # 发送开始处理的通知
            self.notify_progress(
                {
                    "progress": 0,
                    "status": "start",
                    "total": total_files,
                    "current": 0,
                    "message": "开始处理",
                }
            )

            for idx, image_file in enumerate(image_list, 1):
                try:
                    # 发送当前文件开始处理的通知
                    self.notify_progress(
                        {
                            "progress": (idx - 1) / total_files * 100,
                            "status": "processing",
                            "total": total_files,
                            "current": idx,
                            "file": image_file["image_name"],
                            "message": f'正在处理: {image_file["image_name"]}',
                        }
                    )

                    input_path = image_file["image_path"]
                    output_path = os.path.join(
                        output_folder, f"processed_{image_file['image_name']}"
                    )

                    # 处理图片
                    image, image_format = read_image(input_path)
                    processed_image = inpainting_model.inpaint(image, mask_image)
                    processed_image.save(output_path, image_format)

                    processed_files.append(
                        {
                            "name": image_file["image_name"],
                            "status": "success",
                            "path": output_path,
                        }
                    )

                    # 发送当前文件处理完成的通知
                    self.notify_progress(
                        {
                            "progress": idx / total_files * 100,
                            "status": "file_success",
                            "total": total_files,
                            "current": idx,
                            "file": image_file["image_name"],
                            "message": f'完成处理: {image_file["image_name"]}',
                        }
                    )

                except Exception as e:
                    error_msg = str(e)
                    logger.error(
                        f"Error processing {image_file['image_name']}: {error_msg}"
                    )
                    processed_files.append(
                        {
                            "name": image_file["image_name"],
                            "status": "error",
                            "error": error_msg,
                        }
                    )

                    # 发送错误通知
                    self.notify_progress(
                        {
                            "progress": idx / total_files * 100,
                            "status": "file_error",
                            "total": total_files,
                            "current": idx,
                            "file": image_file["image_name"],
                            "error": error_msg,
                            "message": f'处理失败: {image_file["image_name"]}',
                        }
                    )

            # 检查最终状态
            has_errors = any(f["status"] == "error" for f in processed_files)

            # 发送完成通知
            self.notify_progress(
                {
                    "progress": 100,
                    "status": "complete",
                    "total": total_files,
                    "success_count": len(
                        [f for f in processed_files if f["status"] == "success"]
                    ),
                    "error_count": len(
                        [f for f in processed_files if f["status"] == "error"]
                    ),
                    "has_errors": has_errors,
                    "message": "处理完成" if not has_errors else "处理完成，但有部分失败",
                }
            )

            return res200(
                data={
                    "processed_files": processed_files,
                    "output_folder": output_folder,
                    "has_errors": has_errors,
                }
            )

        except Exception as e:
            logger.error(f"Batch process error: {e}")
            self.notify_progress(
                {
                    "progress": 0,
                    "status": "error",
                    "error": str(e),
                    "message": "处理过程发生错误",
                }
            )
            return res500(str(e))
