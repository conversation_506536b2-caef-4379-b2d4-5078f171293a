import time
from utilities.utils import (
    image_obj_to_base64,
    is_image,
    save_bs64_image,
    add_stroke,
    crop_main_subject,
    read_image,
    get_image_format,
    process_gif,
    remove_small_regions,
)
from utilities.log import logger
from utilities.response import res200, res500
import os
from pathlib import Path
from datetime import datetime
from ai_tools.utils import get_api_server_url
from PIL import Image
import traceback

from api.utils import get_segment_func
import urllib.parse

class ImageMattingAPI:
    name = "image_matting"

    def __init__(self):
        pass

    def predict(self, payload: dict) -> dict:
        """
        Predict the no-background image of the input image.

        :param payload: The payload of the input image.
        :return: The response object with the no-background image in base64 format.
        """
        sart_time = time.time()
        image_path = payload["image_path"]
        is_edge_optimization = payload.get("is_edge_optimization", False)
        mode = payload.get("mode", "normal")
        edge_value = payload.get("edge_value", 90)
        ai_model = payload.get("ai_model", "fast")
        remove_background = payload.get("remove_background", False)
        background_color = payload.get("background_color", "transparent")
        # 检查是否为GIF格式
        origin_img, image_format = read_image(image_path)
        # 是否去除小区域
        is_remove_small_regions = payload.get("is_remove_small_regions", False)
        is_remove_small_regions_value = payload.get(
            "is_remove_small_regions_value", 100
        )
        background_color_image = payload.get("background_color_image", None)

        if image_format == "GIF":
            if Path(image_path).exists():
                save_path = Path(image_path).parent / f"output_{time.time()}.gif"
            else:
                # 桌面
                save_path = Path.home() / f"output_{time.time()}.gif"
            try:
                # 处理GIF的所有帧
                gif_frames = process_gif(
                    image_path,
                    segment_func=get_segment_func(ai_model),
                    is_edge_optimization=is_edge_optimization,
                    edge_value=edge_value,
                    mode=mode,
                    remove_background=remove_background,
                    payload=payload,
                    save_path=str(save_path),
                    background_color=background_color,
                )
                logger.info(f"Predicted GIF time: {time.time() - sart_time}")
                return res200(
                    data={
                        "no_bg_image": gif_frames,
                        "is_gif": True,
                        "save_path": str(save_path),
                    }
                )
            except Exception as e:
                logger.error(f"Error in predict GIF: {e} {image_path}")
                return res500("Error in predict GIF")

        # 获取分割函数
        segment_func = get_segment_func(ai_model)

        crop_to_content = bool(mode == "cut")
        try:
            no_bg_image = segment_func(
                image_path,
                is_edge_optimization=is_edge_optimization,
                edge_value=edge_value,
                crop_to_content=crop_to_content,
            )
            if is_remove_small_regions:
                no_bg_image = remove_small_regions(
                    no_bg_image, is_remove_small_regions_value
                )
            logger.info(f"Predicted image time: {time.time() - sart_time}")
        except Exception as e:
            logger.error(f"Error in predict: {traceback.format_exc()} {image_path}")
            return res500(f"模型{ai_model}加载失败，请检查模型大小是否匹配")

        # 根据mode选择不同的处理模式
        if mode == "stroke":
            stroke_style = payload.get("stroke_style", "solid")
            stroke_color = payload.get("stroke_color", (255, 0, 0))
            stroke_width = payload.get("stroke_width", 2)
            if stroke_style == "solid":
                stroke_image = add_stroke(
                    no_bg_image,
                    stroke_width=stroke_width,
                    stroke_color=stroke_color,
                    stroke_type="solid",
                )
            else:
                stroke_image = add_stroke(
                    no_bg_image,
                    stroke_width=stroke_width,
                    stroke_color=stroke_color,
                    stroke_type="dashed",
                )
            if not remove_background:
                # 把stroke_image 到原图上
                origin_img.paste(
                    stroke_image, (0, 0), mask=stroke_image
                )  # 使用 alpha 通道作为 mask
                no_bg_image = origin_img
            else:
                no_bg_image = stroke_image
        elif mode == "crop":
            crop_ratio = payload.get("crop_ratio", "1:1")
            subject_retention = payload.get("subject_retention", 0)

            if crop_ratio == "custom":
                width = payload.get("width", 1024)
                height = payload.get("height", 1024)
                no_bg_image = crop_main_subject(
                    image_path,
                    no_bg_image,
                    crop_ratio,
                    target_size=(width, height),
                    padding=subject_retention,
                    is_matting=remove_background,
                )
            else:
                no_bg_image = crop_main_subject(
                    image_path,
                    no_bg_image,
                    crop_ratio,
                    padding=subject_retention,
                    is_matting=remove_background,
                )
        processed_frame = no_bg_image
        if background_color_image:
            # 使用图片作为背景
            try:
                # 读取背景图片
                bg_img, _ = read_image(background_color_image)

                # 调整背景图片大小以适应视频帧
                # 这里使用 "fill" 模式，确保背景覆盖整个视频帧
                bg_aspect = bg_img.width / bg_img.height
                frame_aspect = no_bg_image.width / no_bg_image.height

                if bg_aspect > frame_aspect:
                    # 背景图片更宽，按高度缩放
                    new_height = no_bg_image.height
                    new_width = int(new_height * bg_aspect)
                    bg_img = bg_img.resize((new_width, new_height), Image.LANCZOS)
                    # 居中裁剪
                    left = (bg_img.width - no_bg_image.width) // 2
                    bg_img = bg_img.crop(
                        (left, 0, left + no_bg_image.width, no_bg_image.height)
                    )
                else:
                    # 背景图片更高，按宽度缩放
                    new_width = no_bg_image.width
                    new_height = int(new_width / bg_aspect)
                    bg_img = bg_img.resize((new_width, new_height), Image.LANCZOS)
                    # 居中裁剪
                    top = (bg_img.height - no_bg_image.height) // 2
                    bg_img = bg_img.crop(
                        (0, top, no_bg_image.width, top + no_bg_image.height)
                    )

                # 创建新图像并合成
                processed_frame = Image.new("RGBA", no_bg_image.size, (0, 0, 0, 0))
                processed_frame.paste(bg_img, (0, 0))
                processed_frame.paste(no_bg_image, (0, 0), no_bg_image)
            except Exception as e:
                logger.error(f"Error processing background image: {traceback.format_exc()}")
                # 如果背景图片处理失败，使用纯色背景
        no_bg_image_base64 = image_obj_to_base64(processed_frame)

        return res200(
            data={
                "no_bg_image": no_bg_image_base64,
            }
        )

    def batch_predict(self, parameters):
        """批量抠图接口"""
        try:
            image_path = parameters.get("image_path")
            mode = parameters.get("mode", "custom")
            save_format = parameters.get("save_format", "png")
            background_color = parameters.get("background_color", "transparent")
            background_color_image = parameters.get("background_color_image", None)
            output_folder = parameters.get("output_folder")
            input_folder = parameters.get("input_folder")
            is_edge_optimization = parameters.get("is_edge_optimization", False)
            edge_value = parameters.get("edge_value", 90)
            ai_model = parameters.get("ai_model", "superfast")
            remove_background = parameters.get("remove_background", False)

            is_remove_small_regions = parameters.get("is_remove_small_regions", False)
            is_remove_small_regions_value = parameters.get(
                "is_remove_small_regions_value", 100
            )

            file_name_mode = parameters.get("file_name_mode", "original")

            crop_to_content = bool(mode == "cut")
            dpi = parameters.get("dpi", "72")
            dpi = (int(dpi), int(dpi))

            # 如果没有指定输出目录，在输入目录下创建result文件夹
            if not output_folder:
                output_folder = os.path.join(input_folder, "result")

            # 确保输出目录存在
            os.makedirs(output_folder, exist_ok=True)

            # 生成输出文件名
            file_name = Path(image_path).stem
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            if file_name_mode == "original":
                output_name = f"{file_name}.{save_format}"
            else:
                output_name = f"{file_name}-{timestamp}.{save_format}"
            output_path = os.path.join(output_folder, output_name)

            # 检查是否为GIF格式
            image_format = get_image_format(image_path)
            if image_format == "GIF":
                output_name = f"{file_name}-{timestamp}.gif"
                output_path = os.path.join(output_folder, output_name)
                try:
                    process_gif(
                        image_path,
                        segment_func=get_segment_func(ai_model),
                        is_edge_optimization=is_edge_optimization,
                        edge_value=edge_value,
                        mode=mode,
                        remove_background=remove_background,
                        payload=parameters,
                        save_path=str(output_path),
                        background_color=background_color,
                    )
                    return res200(
                        data={
                            "result_path": output_path,
                            "result_url": f"{get_api_server_url()}/local_file?file_path={urllib.parse.quote(output_path)}",
                        }
                    )
                except Exception as e:
                    logger.error(f"Error in batch predict GIF: {e}")
                    return res500(f"GIF处理失败: {str(e)}")

            # 获取分割函数
            segment_func = get_segment_func(ai_model)

            try:
                no_bg_image = segment_func(
                    image_path,
                    is_edge_optimization=is_edge_optimization,
                    edge_value=edge_value,
                    crop_to_content=crop_to_content,
                )
                if is_remove_small_regions:
                    no_bg_image = remove_small_regions(
                        no_bg_image, is_remove_small_regions_value
                    )

            except Exception as e:
                logger.error(f"Error in predict: {e} {image_path}")
                return res500("Error in predict")

            if mode == "stroke":
                stroke_style = parameters.get("stroke_style", "solid")
                stroke_color = parameters.get("stroke_color", (255, 0, 0))
                stroke_width = parameters.get("stroke_width", 2)
                if stroke_style == "solid":
                    no_bg_image = add_stroke(
                        no_bg_image,
                        stroke_width=stroke_width,
                        stroke_color=stroke_color,
                        stroke_type="solid",
                    )
                else:
                    no_bg_image = add_stroke(
                        no_bg_image,
                        stroke_width=stroke_width,
                        stroke_color=stroke_color,
                        stroke_type="dashed",
                    )

            if mode == "crop":
                crop_ratio = parameters.get("crop_ratio", "1:1")
                subject_retention = parameters.get("subject_retention", 0)
                if crop_ratio == "custom":
                    width = parameters.get("width", 1024)
                    height = parameters.get("height", 1024)
                    no_bg_image = crop_main_subject(
                        image_path,
                        no_bg_image,
                        crop_ratio,
                        target_size=(width, height),
                        padding=subject_retention,
                        is_matting=remove_background,
                    )
                else:
                    no_bg_image = crop_main_subject(
                        image_path,
                        no_bg_image,
                        crop_ratio,
                        padding=subject_retention,
                        is_matting=remove_background,
                    )

            processed_frame = no_bg_image
            if background_color_image:
                # 使用图片作为背景
                try:
                    # 读取背景图片
                    bg_img, _ = read_image(background_color_image)

                    # 调整背景图片大小以适应视频帧
                    # 这里使用 "fill" 模式，确保背景覆盖整个视频帧
                    bg_aspect = bg_img.width / bg_img.height
                    frame_aspect = no_bg_image.width / no_bg_image.height

                    if bg_aspect > frame_aspect:
                        # 背景图片更宽，按高度缩放
                        new_height = no_bg_image.height
                        new_width = int(new_height * bg_aspect)
                        bg_img = bg_img.resize((new_width, new_height), Image.LANCZOS)
                        # 居中裁剪
                        left = (bg_img.width - no_bg_image.width) // 2
                        bg_img = bg_img.crop(
                            (left, 0, left + no_bg_image.width, no_bg_image.height)
                        )
                    else:
                        # 背景图片更高，按宽度缩放
                        new_width = no_bg_image.width
                        new_height = int(new_width / bg_aspect)
                        bg_img = bg_img.resize((new_width, new_height), Image.LANCZOS)
                        # 居中裁剪
                        top = (bg_img.height - no_bg_image.height) // 2
                        bg_img = bg_img.crop(
                            (0, top, no_bg_image.width, top + no_bg_image.height)
                        )

                    # 创建新图像并合成
                    processed_frame = Image.new("RGBA", no_bg_image.size, (0, 0, 0, 0))
                    processed_frame.paste(bg_img, (0, 0))
                    processed_frame.paste(no_bg_image, (0, 0), no_bg_image)
                except Exception as e:
                    logger.error(f"Error processing background image: {e}")
                    # 如果背景图片处理失败，使用纯色背景
            no_bg_image_base64 = image_obj_to_base64(processed_frame)

            save_bs64_image(
                no_bg_image_base64,
                background_color,
                save_format,
                image_path,
                output_path,
                dpi=dpi,
            )
            return res200(
                data={
                    "result_path": output_path,
                    "result_url": f"{get_api_server_url()}/local_file?file_path={urllib.parse.quote(output_path)}",
                }
            )

        except Exception as e:
            logger.error(f"Error in batch_predict: {str(e)}")
            return res500(f"处理失败: {str(e)}")
