#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# File: __init__.py.py
import subprocess
import platform
import os
from utilities.utils import (
    image_obj_to_base64,
    read_image,
    img_to_base64,
    base64_set_bg,
    save_bs64_image,
    is_image,
    base64_to_image_obj,
    format_size,
)
from PIL import Image
from utilities.response import res200, res500
import webbrowser
import webview
from pathlib import Path
from utilities.log import logger
from datetime import datetime
import json
from ai_tools.utils import get_api_server_url
import traceback
import urllib.parse


class API:
    def __init__(self):
        """初始化 API 类"""
        self._temp_files = {}  # 用于跟踪临时文件

    def _serialize_for_js(self, data):
        """将 Python 数据类型转换为 JavaScript 兼容的格式"""

        def convert(obj):
            if isinstance(obj, bool):
                return str(obj).lower()  # True -> 'true', False -> 'false'
            if isinstance(obj, (dict, list)):
                return json.dumps(obj, default=convert)
            return str(obj)

        if isinstance(data, dict):
            return {k: convert(v) for k, v in data.items()}
        return convert(data)

    def notify_progress(self, data):
        """通知前端当前处理进度"""
        window = webview.windows[0]
        # 转换数据为 JavaScript 兼容格式
        js_data = self._serialize_for_js(data)
        js_code = (
            'window.dispatchEvent(new CustomEvent("ProcessProgress", {'
            f'"detail": {json.dumps(js_data)}'
            "}))"
        )
        window.evaluate_js(js_code)

    def get_video_url(self, file_path: str):
        """获取视频文件的 URL"""
        try:
            if not os.path.exists(file_path):
                return res500("文件不存在")

            # 返回相对 URL（使用 FastAPI 服务器的端口）
            return res200(
                data={
                    "video_url": f"{get_api_server_url()}/local_file?file_path={urllib.parse.quote(file_path)}"
                }
            )
        except Exception as e:
            logger.error(f"Error in get_video_url: {e}")
            return res500(str(e))

    def open_and_select_file(self, file_path):
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.warning(f"文件不存在: {file_path}")
                return res500(f"文件不存在: {file_path}")

            system = platform.system()

            if system == "Windows":
                # 在Windows系统上打开文件夹并选中文件
                subprocess.call(["explorer", "/select,", os.path.normpath(file_path)])
            elif system == "Darwin":  # macOS
                # 在macOS系统上打开Finder并选中文件
                subprocess.call(["open", "-R", file_path])
            elif system == "Linux":
                # 在Linux系统上打开文件管理器并选中文件
                # 使用xdg-open无法直接选中文件，因此这里只是打开文件夹
                subprocess.call(["xdg-open", os.path.dirname(file_path)])
            else:
                return res500("不支持的操作系统")

            return res200(msg="文件已打开")

        except Exception as e:
            logger.error(f"Error in open_and_select_file: {e}")
            return res500(f"打开文件失败: {e}")

    def add_apis(self, APIClass):
        for method_name in dir(APIClass):
            method = getattr(APIClass, method_name)
            if callable(method) and not method_name.startswith("_"):
                setattr(API, f"{APIClass.name}__{method_name}", method)

    def get_local_file_base64(self, file):
        try:
            img = img_to_base64(file)
            return res200(data={"base64_image": img})
        except Exception as e:
            return res500(f"Error in get_local_file_base64: {e}")

    def open_link(self, link):
        webbrowser.open(link)

    def open_img_file(self):
        try:
            file_types = (
                "Image Files (*.jpg;*.png;*.gif;*.bmp;*.webp)",
                "All files (*.*)",
            )
            result = webview.windows[0].create_file_dialog(
                webview.OPEN_DIALOG, allow_multiple=False, file_types=file_types
            )
            print(result)
            # mac 下 webp 文件无法打开，需要转换为 base64
            if result:
                selected_file = result[0]
                # if selected_file.lower().endswith(".webp"):
                #     image_url = img_to_base64(selected_file)
                # else:
                image_url = (
                    f"{get_api_server_url()}/local_file?file_path={urllib.parse.quote(selected_file)}"
                )
            else:
                selected_file = None
                image_url = None
            return res200(data={"selected_file": selected_file, "image_url": image_url})

        except Exception as e:
            return res500(f"Error in open_img_file: {e}")

    def open_convert_img_file(self):
        try:
            file_types = (
                "Image Files (*.jpg;*.png;*.gif;*.bmp;*.webp;*.tiff;*.ico;*.icns)",
                "All files (*.*)",
            )
            result = webview.windows[0].create_file_dialog(
                webview.OPEN_DIALOG, allow_multiple=False, file_types=file_types
            )
            # mac 下 webp 文件无法打开，需要转换为 base64
            if result:
                selected_file = result[0]
                if (
                    # selected_file.lower().endswith(".webp") or
                    selected_file.lower().endswith(".tiff")
                    or selected_file.lower().endswith(".ico")
                    or selected_file.lower().endswith(".icns")
                ):
                    image_url = img_to_base64(str(selected_file))
                else:
                    image_url = (
                        f"{get_api_server_url()}/local_file?file_path={urllib.parse.quote(selected_file)}"
                    )
            else:
                selected_file = None
                image_url = None
            return res200(data={"selected_file": selected_file, "image_url": image_url})

        except Exception as e:
            logger.error(
                f"Error in open_convert_img_file traceback: {traceback.format_exc()}"
            )
            return res500(f"Error in open_convert_img_file: {e}")

    def get_img_base64(self, url):
        try:
            image, _ = read_image(url)
            img = image_obj_to_base64(image)
            return res200(data={"base64_image": img})
        except Exception as e:
            import traceback

            logger.error(f"Error in get_img_base64 url: {url} : {e}")
            logger.error(f"Error in get_img_base64 traceback: {traceback.format_exc()}")
            return res500(f"Error in get_img_base64: {e}")

    def get_local_file_path(self, file_type=None):
        try:
            if file_type:
                file_types = (file_type, "All files (*.*)")
            result = webview.windows[0].create_file_dialog(
                webview.OPEN_DIALOG, allow_multiple=False, file_types=file_types
            )
            if isinstance(result, tuple):
                return res200(data={"selected_file": result[0]})
            else:
                return res200(data={"selected_file": result})
        except Exception as e:
            return res500(f"Error in open_img_file: {e}")

    def open_video_file(self, multiple=False):
        try:
            file_types = (
                "Video Files (*.mp4;*.avi;*.mov;*.mkv;*.wmv;*.flv)",
                "All files (*.*)",
            )
            result = webview.windows[0].create_file_dialog(
                webview.OPEN_DIALOG,
                allow_multiple=bool(multiple),
                file_types=file_types,
            )
            return res200(data={"selected_files": result if result else []})
        except Exception as e:
            return res500(f"Error in open_video_file: {e}")

    def get_base64_set_bg(
        self,
        playload: dict,
    ):
        base64_data = playload.get("base64_data")
        hex_color = playload.get("hex_color")
        try:
            result_base64 = base64_set_bg(base64_data, hex_color)
        except Exception as e:
            logger.error(f"save_png_add_bg_dialog error: {e}")
            return res500(f"save_png_add_bg_dialog error: {e}")
        return res200(data={"base64_data": result_base64})

    def save_image_dialog(self, playload: dict):
        base64_data = playload.get("base64_data")
        file_name = playload.get("file_name", None)
        extension = playload.get("extension", "png")
        hex_color = playload.get("hex_color", "transparent")
        origin_image = playload.get("origin_image", None)
        background_color_image = playload.get("background_color_image", None)
        dpi = playload.get("dpi", "72")
        dpi = (int(dpi), int(dpi))
        if file_name:
            # 获取文件的名字，比如 a.png 获取 a
            file_name = Path(file_name).stem
        else:
            file_name = f"result"
        file_path = f"{file_name}-{datetime.now().strftime('%Y%m%d%H%M%S')}.{extension}"
        result = webview.windows[0].create_file_dialog(
            webview.SAVE_DIALOG, directory="", save_filename=file_path
        )
        if not result:
            return res500("已取消")
        if background_color_image:
            # 使用图片作为背景
            try:
                no_bg_image = base64_to_image_obj(base64_data)
                # 读取背景图片
                bg_img, _ = read_image(background_color_image)

                # 调整背景图片大小以适应视频帧
                # 这里使用 "fill" 模式，确保背景覆盖整个视频帧
                bg_aspect = bg_img.width / bg_img.height
                frame_aspect = no_bg_image.width / no_bg_image.height

                if bg_aspect > frame_aspect:
                    # 背景图片更宽，按高度缩放
                    new_height = no_bg_image.height
                    new_width = int(new_height * bg_aspect)
                    bg_img = bg_img.resize((new_width, new_height), Image.LANCZOS)
                    # 居中裁剪
                    left = (bg_img.width - no_bg_image.width) // 2
                    bg_img = bg_img.crop(
                        (left, 0, left + no_bg_image.width, no_bg_image.height)
                    )
                else:
                    # 背景图片更高，按宽度缩放
                    new_width = no_bg_image.width
                    new_height = int(new_width / bg_aspect)
                    bg_img = bg_img.resize((new_width, new_height), Image.LANCZOS)
                    # 居中裁剪
                    top = (bg_img.height - no_bg_image.height) // 2
                    bg_img = bg_img.crop(
                        (0, top, no_bg_image.width, top + no_bg_image.height)
                    )

                # 创建新图像并合成
                processed_frame = Image.new("RGBA", no_bg_image.size, (0, 0, 0, 0))
                processed_frame.paste(bg_img, (0, 0))
                processed_frame.paste(no_bg_image, (0, 0), no_bg_image)
            except Exception as e:
                logger.error(f"Error processing background image: {e}")
                # 如果背景图片处理失败，使用纯色背景
            base64_data = image_obj_to_base64(processed_frame)

        try:
            save_bs64_image(
                base64_data, hex_color, extension, origin_image, result, dpi=dpi
            )
        except Exception as e:
            import traceback

            logger.error(f"save_image_dialog traceback: {traceback.format_exc()}")
            return res500(f"save_image_dialog error: {e}")
        return res200(data={"selected_file": file_path})

    def open_folder_dialog(self, initial_directory=""):
        result = webview.windows[0].create_file_dialog(
            webview.FOLDER_DIALOG, directory=initial_directory
        )
        # 兼容pywebview 5.2版本，windows下返回的结果为str,mac下返回的结果为tuple的bug
        if isinstance(result, tuple):
            folder_path = result[0]
        elif isinstance(result, str):
            folder_path = result
        else:
            folder_path = ""
        return res200({"folder_path": folder_path})

    def get_folder_images(self, folder_path: str) -> dict:
        """
        Get the list of images in the input folder.


        :param folder_path: The path of the input folder.

        :return: The response object with the list of images in the folder.

        """
        try:
            image_list = []
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    if is_image(file):
                        image_path = os.path.join(root, file)
                        tem = {
                            "image_name": os.path.basename(image_path),
                            "image_path": image_path,
                            "image_url": f"{get_api_server_url()}/local_file?file_path={urllib.parse.quote(image_path)}",
                            "status": "",
                            "result_path": "",
                        }
                        image_list.append(tem)
            return res200(data={"image_list": image_list, "folder_path": folder_path})
        except Exception as e:
            logger.error(f"Error in get_folder_images: {e} {folder_path}")
            return res500("Error in get_folder_images")

    def open_save_dialog(self, save_filename):
        result = webview.windows[0].create_file_dialog(
            webview.SAVE_DIALOG,
            save_filename=save_filename,
        )
        return result

    def open_directory(self, directory_path):
        """在系统默认文件管理器中打开指定目录

        Args:
            directory_path (str): 要打开的目录路径

        Returns:
            bool: 是否成功打开目录
        """
        try:
            system = platform.system()
            # 确保路径是绝对路径
            abs_path = os.path.abspath(directory_path)
            logger.info(f"Opening directory: {abs_path}")

            # 检查路径是否存在
            if not os.path.exists(abs_path):
                logger.warning(f"目录不存在: {abs_path}")

                # 尝试查找最接近的存在的父目录
                current_path = abs_path
                while (
                    not os.path.exists(current_path)
                    and current_path != "/"
                    and current_path != ""
                ):
                    current_path = os.path.dirname(current_path)

                if current_path != "/" and current_path != "":
                    logger.info(f"使用最接近的存在的父目录: {current_path}")
                    abs_path = current_path
                else:
                    logger.error("无法找到有效的父目录")
                    return res500("目录不存在且无法找到有效的父目录")

            if system == "Windows":
                # 对于Windows，直接使用shell=False，避免路径解析问题
                subprocess.run(["explorer", "/select,", abs_path], shell=False)
            elif system == "Darwin":  # macOS
                try:
                    # 对于macOS，使用NSWorkspace API通过AppleScript打开目录
                    # 这种方法对处理中文和特殊字符的路径更可靠
                    applescript = f"""
                    tell application "Finder"
                        open POSIX file "{abs_path}"
                        activate
                    end tell
                    """
                    subprocess.run(["osascript", "-e", applescript])
                except Exception as e:
                    logger.error(f"Error using AppleScript: {e}")
                    try:
                        # 后备方案：使用转义的路径和open命令
                        if os.path.isdir(abs_path):
                            subprocess.run(["open", abs_path])
                        else:
                            # 如果是文件，打开其所在目录
                            parent_dir = os.path.dirname(abs_path)
                            subprocess.run(["open", parent_dir])
                    except Exception as e2:
                        logger.error(f"Fallback open attempt failed: {e2}")
                        try:
                            # 最后的尝试：打开用户主目录
                            home_dir = os.path.expanduser("~")
                            subprocess.run(["open", home_dir])
                        except Exception as e3:
                            logger.error(f"All attempts failed: {e3}")
            elif system == "Linux":
                subprocess.run(["xdg-open", abs_path])
            else:
                return res500("不支持的操作系统")

            return res200(msg="目录已打开")
        except Exception as e:
            logger.error(f"Error in open_directory: {e}")
            return res500(str(e))

    def get_image_url(self, image_path: str):
        if (
            image_path.lower().endswith(".webp")
            or image_path.lower().endswith(".tiff")
            or image_path.lower().endswith(".ico")
            or image_path.lower().endswith(".icns")
        ):
            image_url = img_to_base64(image_path)
        elif image_path.endswith("-GIF"):
            try:
                tem_img_path = [
                    os.path.join(image_path, i)
                    for i in os.listdir(os.path.dirname(image_path))
                    if i.endswith(".png")
                ][0]
                image_url = (
                    f"{get_api_server_url()}/local_file?file_path={urllib.parse.quote(tem_img_path)}"
                )
            except Exception as e:
                image_url = None
        else:
            image_url = f"{get_api_server_url()}/local_file?file_path={urllib.parse.quote(image_path)}"
        return res200(data={"image_url": image_url})

    def get_image_info(self, image_path: str):
        """获取图片信息"""
        try:
            if not os.path.exists(image_path):
                return res500("文件不存在")

            # 获取文件大小
            file_size = os.path.getsize(image_path)
            size_str = format_size(int(file_size))

            # 获取图片格式和尺寸
            try:
                with Image.open(image_path) as img:
                    format_name = img.format or "Unknown"
                    width, height = img.size

                    return res200(
                        data={
                            "format": format_name,
                            "width": width,
                            "height": height,
                            "size": file_size,
                            "size_str": size_str,
                            "path": image_path,
                        }
                    )
            except Exception as e:
                # 如果无法读取图片信息，至少返回文件大小
                return res200(
                    data={
                        "format": "Unknown",
                        "width": 0,
                        "height": 0,
                        "size": file_size,
                        "size_str": size_str,
                        "path": image_path,
                    }
                )

        except Exception as e:
            import traceback

            print(traceback.format_exc())
            logger.error(f"Error in get_image_info: {e}")
            return res500(f"获取图片信息失败: {e}")
