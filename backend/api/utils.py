from ai_tools.rmbg import ImageSegmentation
from ai_tools.modnet import MODNetSegmentation
from ai_tools.brn import BiRefNetAPP, BiRefNetEpoch100, BiRefNetTiny232, BiRefNetRMBG2, BiRefNetBase
import gc
from ai_tools.lama import LamaInpainting
from ai_tools.migan import MiganInpainting
from ai_tools.real_esrgan import RealEsrGanGeneral, RealEsrGanAnime
from ai_tools.ppocr_tool import PPOCR


def get_segment_func(ai_model):
    """获取对应的分割函数"""
    if ai_model == "modnet":
        return MODNetSegmentation().segment_image
    elif ai_model == "fast":
        return BiRefNetAPP(model_type="tiny_232").segment_image
    elif ai_model == "quality":
        return BiRefNetAPP(model_type="epoch_100").segment_image
    elif ai_model == "rmbg2":
        return BiRefNetAPP(model_type="rmbg2").segment_image
    elif ai_model == "rmbg14":
        return ImageSegmentation().segment_image
    elif ai_model == "superfast":
        return ImageSegmentation().segment_image


def release_all_models():
    """释放所有模型的内存"""
    try:
       
        # 释放所有可能的模型实例
        model_classes = [
            MODNetSegmentation,
            ImageSegmentation,
            BiRefNetBase,  # 添加基类
            BiRefNetEpoch100,
            BiRefNetTiny232,
            BiRefNetRMBG2,
        ]

        # 添加其他模型类（如果导入成功）
        try:
            model_classes.extend([
                LamaInpainting,
                MiganInpainting,
                RealEsrGanGeneral,
                RealEsrGanAnime,
                PPOCR
            ])
        except NameError:
            pass  # 如果某些类没有导入成功，跳过

        released_count = 0
        for model_class in model_classes:
            try:
                if hasattr(model_class, '_instance') and model_class._instance is not None:
                    print(f"释放模型: {model_class.__name__}")

                    # 使用force_release方法强制释放（如果可用）
                    if hasattr(model_class._instance, 'force_release'):
                        model_class._instance.force_release()
                    else:
                        # 回退到普通release方法
                        # 强制重置引用计数为1，确保能够释放
                        if hasattr(model_class, '_reference_count'):
                            model_class._reference_count = 1

                        # 调用release方法
                        model_class._instance.release()

                        # 强制清空实例
                        model_class._instance = None
                        if hasattr(model_class, '_initialized'):
                            model_class._initialized = False

                    released_count += 1
                    print(f"成功释放模型: {model_class.__name__}")

            except Exception as e:
                print(f"释放模型 {model_class.__name__} 失败: {str(e)}")
                continue

        # 尝试清理ONNX Runtime全局状态
        try:
            import onnxruntime as ort
            # 清理ONNX Runtime的全局状态（如果有的话）
            if hasattr(ort, 'get_all_providers'):
                # 重新初始化providers可能有助于清理内存
                pass
        except Exception as e:
            logger.warning(f"清理ONNX Runtime全局状态失败: {e}")

        # 强制垃圾回收
        gc.collect()

        print(f"总共释放了 {released_count} 个模型实例")
        return True

    except Exception as e:
        from utilities.log import logger
        print(f"释放模型内存失败: {str(e)}")
        return False