from ai_tools.rmbg import ImageSegmentation
from ai_tools.modnet import MODNetSegmentation
from ai_tools.brn import BiRefNetAPP, BiRefNetEpoch100, BiRefNetTiny232, BiRefNetRMBG2


def get_segment_func(ai_model):
    """获取对应的分割函数"""
    if ai_model == "modnet":
        return MODNetSegmentation().segment_image
    elif ai_model == "fast":
        return BiRefNetAPP(model_type="tiny_232").segment_image
    elif ai_model == "quality":
        return BiRefNetAPP(model_type="epoch_100").segment_image
    elif ai_model == "rmbg2":
        return BiRefNetAPP(model_type="rmbg2").segment_image
    elif ai_model == "rmbg14":
        return ImageSegmentation().segment_image
    elif ai_model == "superfast":
        return ImageSegmentation().segment_image


def release_all_models():
    """释放所有模型的内存"""
    try:
        # 释放所有可能的模型实例
        model_classes = [
            MODNetSegmentation,
            ImageSegmentation,
            BiRefNetEpoch100, 
            BiRefNetTiny232, 
            BiRefNetRMBG2
        ]
        
        for model_class in model_classes:
            print(hasattr(model_class, '_instance'), model_class._instance)
            if hasattr(model_class, '_instance') and model_class._instance is not None:
                model_class._instance.release()
        return True
    except Exception as e:
        from utilities.log import logger
        logger.error(f"释放模型内存失败: {str(e)}")
        return False
