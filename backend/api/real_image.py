from utilities.response import res500, res200, res400
from utilities.log import logger
from ai_tools.utils import get_api_server_url
import os
from datetime import datetime
from pathlib import Path
from ai_tools.real_esrgan import RealEsrGanAPP
from utilities.utils import image_obj_to_base64, base64_to_image
import webview
import urllib.parse



class RealImageAPI:
    name = "real_image"

    def single_real_image(self, playload):
        try:
            input_path = playload["input_path"]
            model_type = playload["model_type"]
            scale = playload.get("scale", 4)
            real_image_model = RealEsrGanAPP(model_type=model_type)
            result = real_image_model.predict(input_path, scale=scale)
            result = image_obj_to_base64(result)
            return res200(data={"result": result})
        except KeyError:
            return res500("Invalid input")
        except Exception as e:
            import traceback

            traceback.print_exc()
            logger.error(f"Error in real image: {e} {input_path}")
            return res500("Error in real image")

    def save_image_dialog(self, playload):
        try:
            base64_data = playload.get("base64_data")
            origin_image = playload.get("origin_image", None)

            if os.path.exists(origin_image):
                file_name = os.path.basename(origin_image)
            else:
                file_name = f"result-{datetime.now().strftime('%Y%m%d%H%M%S')}.png"

            result = webview.windows[0].create_file_dialog(
                webview.SAVE_DIALOG, directory="", save_filename=file_name
            )
            if not result:
                return res500("已取消")
            result = base64_to_image(base64_data, str(result))
            return res200(data={"result_path": result})
        except Exception as e:
            logger.error(f"Error in save image dialog: {e}")
            return res500("Error in save image dialog")

    def batch_real_image(self, playload):
        try:
            image_path = playload.get("image_path")
            folder_path = playload.get("folder_path")
            model_type = playload.get("model_type")
            scale = playload.get("scale", 4)
            save_folder = playload.get("save_folder")
            logger.info(f"Save folder: {save_folder}")
            if not save_folder:
                save_folder = os.path.join(folder_path, "result")
            if not os.path.exists(save_folder):
                os.makedirs(save_folder, exist_ok=True)
            # 相对位置
            relative_path = os.path.relpath(image_path, folder_path)
            save_path = os.path.join(save_folder, relative_path)
            save_dir = os.path.dirname(save_path)
            if not os.path.exists(save_dir):
                os.makedirs(save_dir, exist_ok=True)
            real_image_model = RealEsrGanAPP(model_type=model_type)
            result = real_image_model.predict(image_path, scale=scale)
            result.save(save_path)
            return res200(
                data={
                    "result_path": save_path,
                    "result_url": f"{get_api_server_url()}/local_file?file_path={urllib.parse.quote(save_path)}",
                }
            )
        except Exception as e:
            logger.error(f"Error in convert image: {e} {image_path}")
            return res500("Error in convert image")
