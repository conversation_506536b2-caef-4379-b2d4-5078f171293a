import time
from utilities.utils import read_image
from utilities.log import logger
from utilities.response import res200, res500
from ai_tools.ppocr_tool import PPOCR
import numpy as np
from pathlib import Path
import traceback


class PPOCRAPI:
    name = "ppocr"

    def __init__(self):
        pass

    def ppocr(self, payload: dict) -> dict:
        image_path = payload.get("image_path")
        try:
            image, _ = read_image(image_path)
            # 确保图像是 RGB 格式（3通道）
            if image.mode == "RGBA":
                image = image.convert("RGB")
            ndarray = np.array(image)
            result = PPOCR().ocr(ndarray, cls=True)

            # 从结果中提取文字
            if result and isinstance(result, tuple) and len(result) >= 2:
                texts = [text_tuple[0] for text_tuple in result[1]]
                result_txt = "\n".join(texts)
            else:
                result_txt = ""
            return res200(data={"result": result_txt})
        except Exception as e:
            logger.error(f"Error in ppocr: {e}")
            return res500("Error in ppocr")

    def ppocr_from_folder(self, payload: dict) -> dict:
        input_folder = payload.get("input_folder", "")
        output_folder = payload.get("output_folder", "")
        image_path = payload.get("image_path")

        try:
            # 读取并预处理图像，与 ppocr 方法保持一致
            image, _ = read_image(image_path)
            # 确保图像是 RGB 格式（3通道）

            image = image.convert("RGB")
            ndarray = np.array(image)

            result = PPOCR().ocr(ndarray, cls=True)

            if result and isinstance(result, tuple) and len(result) >= 2:
                texts = [text_tuple[0] for text_tuple in result[1]]
                result_txt = "\n".join(texts)
            else:
                result_txt = ""

            if output_folder:
                relative_path = Path(image_path).relative_to(input_folder)
                save_path = (
                    Path(output_folder) / relative_path
                ).parent / f"{str(Path(image_path).stem)}.txt"

                if not Path(output_folder).exists():
                    Path(output_folder).mkdir(parents=True, exist_ok=True)
            else:
                save_path = Path(image_path).with_suffix(".txt")

            with open(save_path, "w") as f:
                f.write(result_txt)

            return res200(data={"result": str(save_path)})
        except Exception as e:
            logger.error(f"Error in ppocr_from_folder: {traceback.format_exc()}")
            return res500("Error in ppocr_from_folder")
