from utilities.response import res500, res200, res400
from utilities.log import logger
from ai_tools.ffmpeg_tool import FFmpegTool
from utilities.utils import is_video_file
from pathlib import Path
import os
from ai_tools.utils import get_api_server_url
import urllib.parse

class SmartVideoSplitAPI:
    name = "smart_video_split"

    def smart_video_split(self, payload):
        try:
            video_path = payload["input_path"]
            save_folder = payload.get("save_folder", "")
            threshold = float(payload.get("threshold", 27.0))

            if not save_folder:
                output_dir = Path(video_path).parent / "result"

            if not output_dir.exists():
                output_dir.mkdir(parents=True, exist_ok=True)

            if not is_video_file(video_path):
                return res400(data={"message": "Invalid video file"})
            output_dir = FFmpegTool().smart_video_split(
                video_path, str(output_dir), threshold
            )
            return res200(data={"output_dir": str(output_dir)})
        except Exception as e:
            logger.error(f"Error in smart video split: {e}")
            return res500(f"Error in smart video split: {e}")

    def get_folder_smart_video_split(self, folder_path):
        try:
            split_videos = []
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    video_path = os.path.join(root, file)
                    if is_video_file(video_path):
                        video_url = (
                            f"{get_api_server_url()}/local_file?file_path={urllib.parse.quote(video_path)}"
                        )
                        tem = {
                            "video_name": os.path.basename(video_path),
                            "video_path": video_path,
                            "video_url": video_url,
                            "status": "",
                            "split_result": "",
                        }
                        split_videos.append(tem)
            return res200(
                data={"split_videos": split_videos, "folder_path": folder_path}
            )
        except Exception as e:
            logger.error(f"Error in get_folder_smart_video_split: {e} {folder_path}")
            return res500("Error in get_folder_smart_video_split")

    def smart_video_split_from_folder(self, payload):
        try:
            video_path = payload["input_path"]
            folder_path = payload.get("folder_path", "")
            threshold = float(payload.get("threshold", 27.0))
            save_folder = payload.get("save_folder", "")
            if not save_folder:
                save_folder = os.path.join(folder_path, "result")
            if not os.path.exists(save_folder):
                os.makedirs(save_folder, exist_ok=True)
            FFmpegTool().smart_video_split(video_path, str(save_folder), threshold)
            return res200(data={"output_dir": str(save_folder)})
        except Exception as e:
            logger.error(f"Error in smart video split from folder: {e}")
            return res500(f"Error in smart video split from folder: {e}")
