block_cipher = None
version = '0.1.6'
title = '灵象工具箱'

# 分析步骤，收集所需的文件和依赖项
a = Analysis(
    ["main.py"],
    pathex=[],  # 确保包含当前目录
    binaries=[
        ('./pngquant/pngquant', './pngquant')
    ],
    datas=[
        ('./web', 'web'),  # 收集 web 目录
        ('./assets', 'assets'),
        # 只包含必要的 paddleocr 文件
        ('./.venv/lib/python3.12/site-packages/paddleocr', 'paddleocr'),
        ('./api/*.so', 'api'),
        ('./ai_tools/*.so', 'ai_tools'),
        ('./conf/*.so', 'conf'),
        ('./utilities/*.so', 'utilities'),
    ],
    # 'api', 'ai_tools', 'conf', 'utilities',
    hiddenimports=[
        'api',
        'utilities.log',
        'utilities.response',
        'loguru', 'cryptography', 'boto3',
        'shapely', 'pyclipper', 'imghdr', 'imgaug', 'lmdb', 'beautifulsoup4',
        'albumentations', 'albucore', 'fire', 'fonttools', 'rapidfuzz', 'tqdm', 'docx', 
        'paddle',
        'paddleocr',
        'requests',
        'psd_tools',
        'pydantic_settings',
        'ffmpeg',
        'onnxruntime',
        'cryptography.fernet',
        'dns.resolver',
        'scenedetect',
        'scenedetect.detectors',
        'scenedetect.video_splitter'
       
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=['runtime_hook.py'],  # 直接在这里添加运行时钩子
    excludes=[
        'tkinter', 'PIL.ImageQt', 'PIL.ImageTk',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name=title,
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = BUNDLE(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=False,
    version=version,
    upx_exclude=[],
    name=title+'.app',
    icon='assets/logo.icns',
    bundle_identifier="top.linxiangtools",  # 建议改为反向域名
    info_plist={
        'CFBundleName': title,
        'CFBundleDisplayName': title,
        'CFBundleExecutable': title,
        'CFBundleVersion': version,  # 内部版本号
        'CFBundleShortVersionString': version,  # 用户可见版本号
        'NSPrincipalClass': 'NSApplication',
        'NSAppleScriptEnabled': False,
        'NSHumanReadableCopyright': 'Copyright © 2025 linxiangtools.top. All rights reserved.',
        'CFBundleGetInfoString': title+' '+version+', Copyright © 2025 linxiangtools.top',
        'CFBundleDocumentTypes': [{
            'CFBundleTypeIconFile': 'assets/logo.icns',
            'LSItemContentTypes': ['top.linxiangtools.project'],  # 建议标准化UTI
            'LSHandlerRank': 'Owner'
        }],
        'NSAppTransportSecurity': {'NSAllowsArbitraryLoads': True},  # 可选
    }
)