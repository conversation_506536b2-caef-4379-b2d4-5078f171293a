import os
import sys
import subprocess
from pathlib import Path
from typing import Optional, List, Union, Dict, Callable
from utilities.log import logger
import ffmpeg
import base64
import io
from PIL import Image
import numpy as np
import stat
import scenedetect

# from scenedetect import VideoManager, SceneManager
from scenedetect.detectors import ContentDetector
from scenedetect.video_splitter import split_video_ffmpeg
import threading
from functools import wraps
import contextlib

BASE_DIR = Path(__file__).resolve().parent.parent

try:
    from utilities.log import logger
    from conf.config import config
except ImportError:
    sys.path.append(str(BASE_DIR))
    from utilities.log import logger
    from conf.config import config


def windows_no_console(func):
    """
    装饰器：在Windows上隐藏控制台窗口
    这个装饰器会临时修改subprocess.Popen来隐藏Windows控制台窗口
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        if sys.platform.startswith("win"):
            return _with_hidden_console(func, *args, **kwargs)
        else:
            return func(*args, **kwargs)

    return wrapper


@contextlib.contextmanager
def _hidden_console_context():
    """
    上下文管理器：临时修改subprocess.Popen以隐藏Windows控制台窗口
    """
    if not sys.platform.startswith("win"):
        yield
        return

    original_popen = subprocess.Popen

    def patched_popen(*popen_args, **popen_kwargs):
        # 设置Windows特定的参数来隐藏控制台窗口
        if "startupinfo" not in popen_kwargs:
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            popen_kwargs["startupinfo"] = startupinfo

        if "creationflags" not in popen_kwargs:
            popen_kwargs["creationflags"] = subprocess.CREATE_NO_WINDOW

        return original_popen(*popen_args, **popen_kwargs)

    # 临时替换subprocess.Popen
    subprocess.Popen = patched_popen
    try:
        yield
    finally:
        # 恢复原始的subprocess.Popen
        subprocess.Popen = original_popen


def _with_hidden_console(func, *args, **kwargs):
    """
    在隐藏控制台的上下文中执行函数
    """
    with _hidden_console_context():
        return func(*args, **kwargs)


class FFmpegTool:
    _instance = None
    _initialized = False
    _lock = threading.Lock()  # For thread-safe initialization

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                # Double-check locking
                if cls._instance is None:
                    cls._instance = super(FFmpegTool, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """
        Initializes FFmpegTool using a thread-safe singleton pattern.
        The actual setup runs only once per process.
        """
        if not self._initialized:
            with self._lock:
                if not self._initialized:
                    self._initialize_once()
                    self._initialized = True

    def _initialize_once(self):
        """
        Private method for one-time initialization. Sets up paths and environment.
        """
        logger.info("First-time initialization of FFmpegTool...")

        # Get paths and set up the environment
        try:
            self.ffmpeg_path = self._get_ffmpeg_path()
            self.ffprobe_path = self._get_ffprobe_path()
        except (FileNotFoundError, OSError) as e:
            logger.error(f"Failed to find FFmpeg executables: {e}")
            raise

        self._ensure_executable_permissions(self.ffmpeg_path)
        self._ensure_executable_permissions(self.ffprobe_path)

        ffmpeg_dir = str(Path(self.ffmpeg_path).parent)
        current_path = os.environ.get("PATH", "")
        if ffmpeg_dir not in current_path.split(os.pathsep):
            os.environ["PATH"] = f"{ffmpeg_dir}{os.pathsep}{current_path}"
            logger.info(f"Added FFmpeg directory to PATH: {ffmpeg_dir}")

        logger.info(f"FFmpeg path: {self.ffmpeg_path}")
        logger.info(f"FFprobe path: {self.ffprobe_path}")

        # Verification
        self._verify_ffmpeg_access()
        self._verify_ffprobe_access()

    def _ensure_executable_permissions(self, path: str) -> None:
        """Ensures the file has execute permissions on non-Windows systems."""
        if sys.platform.startswith("win"):
            return
        try:
            current_mode = os.stat(path).st_mode
            new_mode = current_mode | stat.S_IXUSR | stat.S_IXGRP | stat.S_IXOTH
            if current_mode != new_mode:
                os.chmod(path, new_mode)
                logger.info(f"Set executable permissions for {path}")
        except Exception as e:
            logger.warning(f"Could not set executable permissions on {path}: {e}")

    @windows_no_console
    def _verify_ffmpeg_access(self) -> None:
        """Verifies that ffmpeg is accessible."""
        try:
            result = subprocess.run(
                ["ffmpeg", "-version"], capture_output=True, text=True
            )
            if result.returncode == 0:
                logger.info("FFmpeg is accessible from PATH.")
            else:
                logger.warning(
                    f"FFmpeg not accessible. Stderr: {result.stderr.strip()}"
                )
        except Exception as e:
            logger.error(f"Error checking FFmpeg accessibility: {e}")

    @windows_no_console
    def _verify_ffprobe_access(self) -> None:
        """Verifies that ffprobe is accessible."""
        try:
            result = subprocess.run(
                ["ffprobe", "-version"], capture_output=True, text=True
            )
            if result.returncode == 0:
                logger.info("FFprobe is accessible from PATH.")
            else:
                logger.warning(
                    f"FFprobe not accessible. Stderr: {result.stderr.strip()}"
                )
        except Exception as e:
            logger.error(f"Error checking FFprobe accessibility: {e}")

    def _get_ffmpeg_path(self) -> str:
        """根据操作系统获取ffmpeg可执行文件路径"""
        base_path = Path(str(config.get("model_dir")))

        if sys.platform.startswith("win"):
            ffmpeg_path = base_path / "win_ffmpeg" / "ffmpeg.exe"
        elif sys.platform.startswith("darwin"):
            ffmpeg_path = base_path / "mac_ffmpeg" / "ffmpeg"
        else:  # Linux
            ffmpeg_path = base_path / "linux_ffmpeg" / "ffmpeg"
            if not ffmpeg_path.exists():
                result = subprocess.run(
                    ["which", "ffmpeg"], capture_output=True, text=True
                )
                if result.returncode == 0:
                    return result.stdout.strip()
                raise OSError(
                    "FFmpeg not found. Please install it or place it in 'hub_model/linux_ffmpeg'."
                )

        if not ffmpeg_path.exists():
            raise FileNotFoundError(f"FFmpeg executable not found at {ffmpeg_path}")
        return str(ffmpeg_path)

    def _get_ffprobe_path(self) -> str:
        """Gets the path to the ffprobe executable based on the OS."""
        base_path = Path(str(config.get("model_dir")))

        if sys.platform.startswith("win"):
            ffprobe_path = base_path / "win_ffmpeg" / "ffprobe.exe"
        elif sys.platform.startswith("darwin"):
            ffprobe_path = base_path / "mac_ffmpeg" / "ffprobe"
        else:  # Linux
            ffprobe_path = base_path / "linux_ffmpeg" / "ffprobe"
            if not ffprobe_path.exists():
                result = subprocess.run(
                    ["which", "ffprobe"], capture_output=True, text=True
                )
                if result.returncode == 0:
                    return result.stdout.strip()
                raise OSError(
                    "FFprobe not found. Please install it or place it in 'hub_model/linux_ffmpeg'."
                )

        if not ffprobe_path.exists():
            raise FileNotFoundError(f"FFprobe executable not found at {ffprobe_path}")
        return str(ffprobe_path)

    @windows_no_console
    def get_frame_base64(
        self,
        input_path: str,
        time_position: float = 0,
        format: str = "PNG",
    ) -> str:
        """
        从视频中提取指定时间点的帧并转换为base64字符串

        Args:
            input_path: 输入视频文件路径
            time_position: 时间位置（秒）
            format: 输出图片格式 ('JPEG' 或 'PNG')
            quality: 图片质量 (1-100, 仅用于JPEG)

        Returns:
            str: base64编码的图片数据，格式为 "data:image/jpeg;base64,/9j/4AAQSkZ..."
        """
        try:
            # 检查输入文件是否存在
            if not os.path.exists(input_path):
                raise FileNotFoundError(f"Input video file not found: {input_path}")

            # 使用ffmpeg-python读取视频帧
            out, _ = (
                ffmpeg.input(input_path, ss=time_position)
                .output("pipe:", format="rawvideo", pix_fmt="rgb24", vframes=1)
                .run(capture_stdout=True, capture_stderr=True)
            )

            # 获取视频信息以确定帧的尺寸
            probe = ffmpeg.probe(input_path)
            video_info = next(s for s in probe["streams"] if s["codec_type"] == "video")
            width = int(video_info["width"])
            height = int(video_info["height"])

            # 将原始视频帧数据转换为numpy数组
            frame = np.frombuffer(out, np.uint8).reshape([height, width, 3])

            # 转换为PIL图像
            image = Image.fromarray(frame)

            # 将图像转换为base64
            buffer = io.BytesIO()
            image.save(buffer, format=format)
            img_str = base64.b64encode(buffer.getvalue()).decode("utf-8")

            # 添加data URI scheme前缀
            mime_type = "jpeg" if format.lower() == "jpeg" else format.lower()
            base64_data = f"data:image/{mime_type};base64,{img_str}"

            logger.info(
                f"Successfully extracted frame at {time_position}s and converted to base64"
            )
            return base64_data

        except Exception as e:
            logger.error(f"Error extracting frame to base64: {str(e)}")
            raise

    @windows_no_console
    def get_video_info(self, input_path: str) -> Dict:
        """
        获取视频文件的基本信息, 包括时长、分辨率，帧率等详细信息

        Args:
            input_path: 输入视频文件路径

        Returns:
            Dict: 包含视频信息的字典，包括:
                - duration: 视频时长（秒）
                - width: 视频宽度
                - height: 视频高度
                - fps: 视频帧率
                - bitrate: 视频比特率
                - codec_name: 视频编码格式名称
                - codec_long_name: 视频编码格式详细名称
                - resolution: 视频分辨率
                - size: 文件大小（字节）
                - format_name: 容器格式
                - format_long_name: 容器格式详细名称
                - start_time: 视频开始时间
                - nb_frames: 总帧数（如果可用）
                - display_aspect_ratio: 显示宽高比
                - pix_fmt: 像素格式
                - color_range: 色彩范围
                - color_space: 色彩空间
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Video file not found: {input_path}")

        try:
            probe = ffmpeg.probe(input_path)
            video_info = next(s for s in probe["streams"] if s["codec_type"] == "video")
            format_info = probe["format"]

            info = {
                # 基本信息
                "duration": float(format_info["duration"]),  # 时长（秒）
                "size": int(format_info["size"]),  # 文件大小（字节）
                "width": int(video_info["width"]),  # 宽度
                "height": int(video_info["height"]),  # 高度
                "resolution": f"{video_info['width']}x{video_info['height']}",  # 分辨率
                # 格式信息
                "format_name": format_info["format_name"],
                "format_long_name": format_info.get("format_long_name", ""),
                # 编码信息
                "codec_name": video_info["codec_name"],
                "codec_long_name": video_info.get("codec_long_name", ""),
                # 开始时间
                "start_time": float(video_info.get("start_time", 0)),
            }

            # 帧率信息
            if "r_frame_rate" in video_info:
                num, den = video_info["r_frame_rate"].split("/")
                info["fps"] = round(float(num) / float(den), 2)

            # 比特率信息
            if "bit_rate" in format_info:
                info["bitrate"] = int(format_info["bit_rate"])

            # 总帧数
            if "nb_frames" in video_info:
                info["nb_frames"] = int(video_info["nb_frames"])

            # 宽高比
            if "display_aspect_ratio" in video_info:
                info["display_aspect_ratio"] = video_info["display_aspect_ratio"]

            # 像素格式
            if "pix_fmt" in video_info:
                info["pix_fmt"] = video_info["pix_fmt"]

            # 色彩信息
            if "color_range" in video_info:
                info["color_range"] = video_info["color_range"]
            if "color_space" in video_info:
                info["color_space"] = video_info["color_space"]

            # 音频流信息（如果存在）
            audio_streams = [s for s in probe["streams"] if s["codec_type"] == "audio"]
            if audio_streams:
                audio_info = audio_streams[0]
                info["audio"] = {
                    "codec_name": audio_info["codec_name"],
                    "codec_long_name": audio_info.get("codec_long_name", ""),
                    "sample_rate": audio_info.get("sample_rate", ""),
                    "channels": audio_info.get("channels", 0),
                    "channel_layout": audio_info.get("channel_layout", ""),
                }

            # 添加人性化的文件大小显示
            info["size_readable"] = self._format_size(info["size"])

            logger.info(f"Video info: {info}")
            return info

        except Exception as e:
            logger.error(f"Error getting video info: {str(e)}")
            raise

    def _format_size(self, size_bytes: float | int) -> str:
        """
        将字节大小转换为人类可读的格式

        Args:
            size_bytes: 文件大小（字节）

        Returns:
            str: 格式化后的大小字符串（如：1.23 GB）
        """
        for unit in ["B", "KB", "MB", "GB", "TB"]:
            if size_bytes < 1024:
                return f"{size_bytes:.2f} {unit}"
            size_bytes /= 1024
        return f"{size_bytes:.2f} PB"

    @windows_no_console
    def extract_frames(self, input_path: str, output_dir: str) -> List[str]:
        """
        从视频中提取所有帧

        Args:
            input_path: 输入视频路径
            output_dir: 输出帧的目录

        Returns:
            List[str]: 所有帧的文件路径列表
        """
        try:
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 获取视频信息以确保正确的帧率
            video_info = self.get_video_info(input_path)
            fps = video_info.get("fps", 30)

            # 构建输出帧的文件名模式（使用6位数字保证顺序）
            output_pattern = os.path.join(output_dir, "frame_%06d.png")

            # 使用ffmpeg-python提取帧
            ffmpeg.input(input_path).output(output_pattern, vsync="vfr", qscale=2).run()

            # 获取所有生成的帧文件路径
            frame_files = sorted(
                [
                    os.path.join(output_dir, f)
                    for f in os.listdir(output_dir)
                    if f.startswith("frame_") and f.endswith(".png")
                ]
            )

            logger.info(f"Extracted {len(frame_files)} frames from video at {fps} fps")
            return frame_files

        except Exception as e:
            logger.error(f"Error extracting frames: {str(e)}")
            raise

    @windows_no_console
    def merge_frames_to_video(
        self, frames_pattern, output_path, video_info, source_video=None
    ):
        """
        将处理后的帧合并为视频，尽可能保持原视频的所有参数

        Args:
            frames_pattern: 帧文件名模式
            output_path: 输出视频路径
            video_info: 原视频信息字典
            source_video: 源视频路径
        """
        try:
            # 从video_info中获取原始视频的参数
            fps = video_info.get("fps", 30)
            width = video_info.get("width")
            height = video_info.get("height")
            video_bitrate = video_info.get("bitrate")
            pixel_format = video_info.get("pix_fmt", "yuv420p")

            # 创建视频流
            video_stream = ffmpeg.input(frames_pattern, framerate=fps)

            # 基本输出参数
            output_args = {
                "vcodec": video_info.get("codec_name", "libx264"),
                "video_bitrate": video_bitrate,
                "pix_fmt": pixel_format,
                "s": f"{width}x{height}",  # 设置分辨率
                "r": fps,  # 设置帧率
                "preset": "medium",  # 编码速度和质量的平衡
                "crf": 17,  # 控制视频质量（0-51，值越小质量越好，建议17-28）
            }

            if source_video:
                # 创建音频流
                audio_stream = ffmpeg.input(source_video).audio

                # 合并视频和音频流，保持原始音频编码
                audio_info = video_info.get("audio", {})
                output_args.update(
                    {
                        "acodec": audio_info.get("codec_name", "aac"),
                        "ar": audio_info.get("sample_rate"),  # 音频采样率
                        "ac": audio_info.get("channels"),  # 音频通道数
                    }
                )

                # 合并视频和音频流
                stream = ffmpeg.output(
                    video_stream,
                    audio_stream,
                    output_path,
                    **{k: v for k, v in output_args.items() if v is not None},
                )
            else:
                # 只输出视频流（无音频）
                stream = ffmpeg.output(
                    video_stream,
                    output_path,
                    **{k: v for k, v in output_args.items() if v is not None},
                )

            # 执行命令
            stream.overwrite_output().run(capture_stdout=True, capture_stderr=True)

            logger.info(f"Successfully merged frames to video: {output_path}")
            logger.info(f"Video parameters: {output_args}")

        except Exception as e:
            logger.error(f"Error merging frames to video: {e}")
            raise

    @windows_no_console
    def process_video_stream(
        self,
        input_path: str,
        output_path: str,
        process_frame_func: Callable,
        video_info: Dict,
        source_video: Optional[str] = None,
        notify_callback: Optional[Callable] = None,
    ):
        """
        Process a video frame by frame in memory using ffmpeg pipes.

        Args:
            input_path: Path to the input video.
            output_path: Path to the output video.
            process_frame_func: A function that takes a PIL Image and returns a processed PIL Image.
            video_info: Dictionary with video information from get_video_info.
            source_video: Optional path to source video to copy audio from.
            notify_callback: Optional callback for progress updates.
        """
        width = video_info["width"]
        height = video_info["height"]
        fps = video_info.get("fps", 30)
        total_frames = video_info.get("nb_frames")

        # Input ffmpeg process to decode video to raw frames
        input_process = (
            ffmpeg.input(input_path)
            .output("pipe:", format="rawvideo", pix_fmt="rgb24", r=fps)
            .run_async(pipe_stdout=True)
        )

        # Output ffmpeg process to encode raw frames into a video
        output_stream = ffmpeg.input(
            "pipe:",
            format="rawvideo",
            pix_fmt="rgb24",
            s=f"{width}x{height}",
            r=fps,
        )

        output_args = {
            "vcodec": video_info.get("codec_name", "libx264"),
            "pix_fmt": "yuv420p",
            "preset": "fast",  # Use fast preset for better speed
            "crf": 23,  # A good balance of quality and size. Lower is better quality.
        }

        # If source video is provided, copy audio stream
        if source_video and "audio" in video_info:
            audio_stream = ffmpeg.input(source_video).audio
            output_args.update(
                {
                    "acodec": "copy",  # Copy audio stream without re-encoding
                }
            )
            stream = ffmpeg.output(
                output_stream,
                audio_stream,
                output_path,
                **output_args,
            )
        else:
            stream = ffmpeg.output(
                output_stream,
                output_path,
                **output_args,
            )

        output_process = stream.overwrite_output().run_async(pipe_stdin=True)

        frame_count = 0
        while True:
            in_bytes = input_process.stdout.read(width * height * 3)
            if not in_bytes:
                break

            frame_count += 1

            in_frame_np = np.frombuffer(in_bytes, np.uint8).reshape([height, width, 3])
            in_frame_pil = Image.fromarray(in_frame_np, "RGB")

            # Process the frame
            out_frame_pil = process_frame_func(in_frame_pil)
            if isinstance(out_frame_pil, tuple):
                out_frame_pil, _ = out_frame_pil

            out_frame_np = np.array(out_frame_pil.convert("RGB"))
            output_process.stdin.write(out_frame_np.tobytes())

            # Progress notification
            if notify_callback:
                progress = (
                    10 + (frame_count / total_frames) * 80 if total_frames else 10
                )
                message = f"正在处理第 {frame_count} 帧..."
                if total_frames:
                    message = f"正在处理第 {frame_count}/{total_frames} 帧..."

                notify_callback(
                    {
                        "progress": progress,
                        "message": message,
                    }
                )

        # Finalize processes
        output_process.stdin.close()
        input_process.stdout.close()
        input_process.wait()
        output_process.wait()

        logger.info(f"Successfully processed video stream to {output_path}")

    @windows_no_console
    def smart_video_split(
        self, video_path: str, output_dir: str, threshold: float = 27.0
    ):
        """
        使用 PySceneDetect 进行视频镜头检测，并使用 ffmpeg-python 进行剪辑保存。

        :param video_path: 输入视频路径
        :param output_dir: 输出分割视频保存路径
        :param threshold: 内容变化阈值（越小越敏感，默认 30.0）
        """

        output_dir = str(Path(output_dir) / Path(video_path).stem)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        output_file_template = f"{output_dir}/$VIDEO_NAME-Scene-$SCENE_NUMBER.mp4"

        # Step 1: 场景检测
        video_manager = scenedetect.VideoManager([video_path])
        scene_manager = scenedetect.SceneManager()
        scene_manager.add_detector(ContentDetector(threshold=threshold))

        video_manager.set_downscale_factor()  # 自动降采样提升性能
        video_manager.start()
        scene_manager.detect_scenes(frame_source=video_manager)

        scene_list = scene_manager.get_scene_list()
        logger.info(f"共检测到 {len(scene_list)} 个场景。")
        split_video_ffmpeg(
            video_path,
            scene_list,
            output_file_template=output_file_template,
            show_output=False,
        )
        logger.info(f"所有场景已保存至：{output_dir}")
        return output_dir
