import sys
from pathlib import Path
from typing import Optional, Union
from io import BytesIO

import numpy as np
from PIL import Image
import traceback
from .base_model import BaseONNXModel

BASE_DIR = Path(__file__).resolve().parent.parent

try:
    from utilities.log import logger
    from utilities.utils import refine_foreground, read_image,refine_foreground_bak
    from conf.config import config
except ImportError:
    sys.path.append(str(BASE_DIR))
    from utilities.log import logger
    from utilities.utils import refine_foreground, read_image
    from conf.config import config


class ImageSegmentation(BaseONNXModel):
    _instance: Optional["ImageSegmentation"] = None
    _initialized: bool = False
    _reference_count: int = 0

    def __init__(
        self,
        model_path=str(Path(config.get("model_dir")) / "rmbg14.onnx"),
        model_input_size=[1024, 1024],
    ):
        super().__init__(model_path, model_input_size=model_input_size)

    def _init_specifics(self, model_input_size):
        if not isinstance(model_input_size, list) or len(model_input_size) != 2:
            raise ValueError("model_input_size must be a list with two elements")
        if any(not isinstance(size, int) or size <= 0 for size in model_input_size):
            raise ValueError("model_input_size elements must be positive integers")
        self.model_input_size = model_input_size

    def preprocess_image(self, im: np.ndarray) -> np.ndarray:
        # If the image is grayscale, add a dimension to make it a color image
        if len(im.shape) < 3:
            im = im[:, :, np.newaxis]
        # Resize the image to match the model input size
        try:
            im_resized = np.array(
                Image.fromarray(im).resize(self.model_input_size, Image.BILINEAR)
            )
        except Exception as e:
            raise RuntimeError(f"Error resizing image: {e}")
        # Normalize image pixel values to the [0, 1] range
        image = im_resized.astype(np.float32) / 255.0
        # Further normalize image data
        mean = np.array([0.5, 0.5, 0.5], dtype=np.float32)
        std = np.array([1.0, 1.0, 1.0], dtype=np.float32)
        image = (image - mean) / std
        # Convert the image to the required shape
        image = image.transpose(
            2, 0, 1
        )  # Change dimension order (channels, height, width)
        return np.expand_dims(image, axis=0)  # Add batch dimension

    def postprocess_image(self, result: np.ndarray, im_size) -> np.ndarray:
        if isinstance(result, tuple):
            im_size = list(im_size)
        # Resize the result image to match the original image size
        result = np.squeeze(result)
        try:
            result = np.array(Image.fromarray(result).resize(im_size, Image.BILINEAR))
        except Exception as e:
            raise RuntimeError(f"Error resizing result image: {e}")
        # Normalize the result image data
        ma = result.max()
        mi = result.min()
        result = (result - mi) / (ma - mi)
        # Convert to uint8 image
        im_array = (result * 255).astype(np.uint8)
        return im_array

    def segment_image(
        self,
        img_obj: Union[str, BytesIO, Image.Image],
        is_edge_optimization=False,
        edge_value=90,
        crop_to_content=False,
    ) -> Image.Image:
        if self.ort_session is None:
            self._load_model()

        # 读取图像
        orig_image, _ = read_image(img_obj)
        # 去除alpha通道
        input_image = orig_image.convert("RGB")
        image_array = np.array(input_image)
        image_size = (input_image.size[0], input_image.size[1])

        image_preprocessed = self.preprocess_image(image_array)

        ort_inputs = {self.ort_session.get_inputs()[0].name: image_preprocessed}
        try:
            ort_outs = self.ort_session.run(None, ort_inputs)
        except Exception as e:
            raise RuntimeError(f"ONNX inference failed: {e}")
        result = ort_outs[0]
        # 后处理
        # mask = (result > 0.5).astype(np.uint8) * 255
        # result_image = self.postprocess_image(mask, image_size)
        result_image = self.postprocess_image(result[0][0], image_size)

        pil_im = Image.fromarray(result_image)
        if is_edge_optimization:
            try:
                result = refine_foreground(orig_image, pil_im, r=edge_value)
            except Exception as e:
                try:
                    result = refine_foreground_bak(orig_image, pil_im, r=edge_value)
                except Exception as e:
                    logger.error(f"Error processing images: {traceback.format_exc()}")
                    result = Image.new("RGBA", pil_im.size, (0, 0, 0, 0))
                    result.paste(orig_image, mask=pil_im)
        else:
            result = Image.new("RGBA", pil_im.size, (0, 0, 0, 0))
            result.paste(orig_image, mask=pil_im)

        if crop_to_content:
            # 获取 alpha 通道
            alpha = result.split()[3]
            # 获取非透明区域的边界框
            bbox = alpha.getbbox()
            if bbox:
                # 裁剪图片到边界框
                result = result.crop(bbox)
        return result


if __name__ == "__main__":
    # 示例使用：
    segmentation = ImageSegmentation()
    base_path = BASE_DIR / "hub_model" / "test_images"
    test_imgs = [
        "test.jpg",
        "car.jpg",
        "input.jpg",
    ]
    for img_name in test_imgs:
        img_path = str(base_path / img_name)
        file_name = img_name.split(".")[0]
        no_bg_image = segmentation.segment_image(img_path)
        no_bg_image.save(str(base_path / f"no_bg_{file_name}.png"))
        logger.info(f"Image {img_name} has been processed successfully.")
