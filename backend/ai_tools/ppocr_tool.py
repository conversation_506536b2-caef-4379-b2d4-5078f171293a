from paddleocr import PaddleOCR
from typing import Optional
import time
from pathlib import Path
from PIL import Image, ImageDraw
import numpy as np
import sys
import os
import cv2

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from conf.config import config
from ai_tools.lama import LamaInpainting
from ai_tools.migan import MiganInpainting
from utilities.log import logger


class PPOCR:
    _instance: Optional["PPOCR"] = None
    _initialized: bool = False
    _reference_count: int = 0

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, model_path=Path(config.get("model_dir"))):
        det_model_dir = str(model_path / "det_model.onnx")
        rec_model_dir = str(model_path / "rec_model.onnx")
        cls_model_dir = str(model_path / "cls_model.onnx")
        # 确保初始化代码只运行一次
        if not PPOCR._initialized:
            try:
                start_time = time.time()
                self.ocr = PaddleOCR(
                    use_angle_cls=True,
                    use_gpu=False,
                    use_onnx=True,
                    det_model_dir=det_model_dir,
                    rec_model_dir=rec_model_dir,
                    cls_model_dir=cls_model_dir,
                    lang="ch",  # 支持中英文识别
                )
                print(f"OCR Model loaded in {time.time() - start_time:.2f} seconds")
                PPOCR._initialized = True
            except Exception as e:
                raise RuntimeError(f"Failed to load OCR model: {e}")
        PPOCR._reference_count += 1

    def ppocr_ocr(self, image_path, **kwargs):
        return self.ocr.ocr(image_path, **kwargs)

    def ppocr_mask_from_image(self, image: Image.Image, add_box: int = 0):
        """
        从内存中的图像生成遮罩
        :param image: PIL Image object
        :param add_box: 遮罩范围扩大的像素
        :return: 遮罩图像
        """
        # Convert PIL Image to a format suitable for PaddleOCR (numpy array)
        img_array = np.array(image.convert("RGB"))

        result = self.ocr.ocr(img_array, cls=True)

        # 创建一个和原始图片一样大小的纯黑遮罩
        mask = Image.new("L", image.size, 0)
        draw = ImageDraw.Draw(mask)

        if result and result[0] is not None:
            for item in result[0]:
                points = item[0]
                points = np.array(points).reshape(4, 2)

                x_min = int(np.min(points[:, 0])) - add_box
                y_min = int(np.min(points[:, 1])) - add_box
                x_max = int(np.max(points[:, 0])) + add_box
                y_max = int(np.max(points[:, 1])) + add_box

                # 确保坐标在图像范围内
                x_min = max(0, x_min)
                y_min = max(0, y_min)
                x_max = min(image.width, x_max)
                y_max = min(image.height, y_max)

                # 在遮罩上绘制白色矩形
                draw.rectangle([x_min, y_min, x_max, y_max], fill=255)

        return mask

    def ppocr_mask(self, img_path: str, add_box: int = 0):
        """
        识别图片中的文字并生成遮罩
        :param img_path: 图片路径
        :param add_box: 遮罩范围扩大的像素
        :return: 遮罩图片
        """
        # 打开图片
        try:
            img = Image.open(img_path)
        except Exception as e:
            logger.error(f"无法打开图片: {img_path}, 错误: {e}")
            return None

        return self.ppocr_mask_from_image(img, add_box)

    def get_text(self, img_path: str):
        """
        识别图片中的文字
        :param img_path: 图片路径
        :return: 文字内容
        """
        # 打开图片
        try:
            img = cv2.imread(img_path)
            if img is None:
                raise ValueError("图片加载失败")
        except Exception as e:
            logger.error(f"无法打开图片: {img_path}, 错误: {e}")
            return None

        result = self.ocr.ocr(img, cls=True)
        # 提取文字
        texts = [line[1][0] for line in result[0]] if result and result[0] else []
        return "\n".join(texts)

    def ppocr_draw_boxes(self, image_path, add_box=2, **kwargs):
        ocr_results = self.ocr.ocr(image_path, rec=False, **kwargs)

        # Load the original image
        image = Image.open(image_path)
        draw = ImageDraw.Draw(image)

        # If no results or empty list, return the original image
        if not ocr_results or len(ocr_results) == 0:
            return image

        # Process the first page of results
        boxes = ocr_results[0]

        # Draw boxes for each detected text region
        for box in boxes:
            # Calculate the center of the box
            center_x = sum(point[0] for point in box) / len(box)
            center_y = sum(point[1] for point in box) / len(box)

            # Expand each point by 2 pixels away from center
            expanded_points = []
            for point in box:
                x, y = point
                # Calculate direction from center
                dx = add_box if x >= center_x else -add_box
                dy = add_box if y >= center_y else -add_box
                # Expand point by 2 pixels
                new_x = x + (dx * 2)
                new_y = y + (dy * 2)
                expanded_points.append((new_x, new_y))

            # Draw a semi-transparent red polygon
            draw.polygon(expanded_points, fill=(255, 0, 0, 127), outline=(255, 0, 0))

        # Save the image with boxes
        output_path = str(Path(image_path).with_name("boxes_" + Path(image_path).name))
        image.save(output_path)
        print(f"Image with boxes saved to: {output_path}")

        return image

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()

    def release(self):
        """释放资源的方法"""
        PPOCR._reference_count -= 1
        if PPOCR._reference_count == 0:
            if hasattr(self, "ocr"):
                del self.ocr
            PPOCR._initialized = False
            PPOCR._instance = None

    def __del__(self):
        """析构函数"""
        self.release()


if __name__ == "__main__":
    ppocr = PPOCR()
    mask = ppocr.ppocr_mask("test.png", add_box=3)
    # # print(mask)
    # lama = LamaInpainting()
    # res = lama.inpaint(Image.open("test.png"), mask)
    migan = MiganInpainting()
    res = migan.inpaint(Image.open("test.png"), mask)
    res.save("res.png")

    ppocr.ppocr_draw_boxes("test.png", add_box=3)
