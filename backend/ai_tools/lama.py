import numpy as np
from PIL import Image
from pathlib import Path
import sys
from typing import Optional

from .base_model import BaseONNXModel

BASE_DIR = Path(__file__).resolve().parent.parent


try:
    from utilities.log import logger
    from conf.config import config
except ImportError:
    sys.path.append(str(BASE_DIR))
    from utilities.log import logger
    from conf.config import config


class LamaInpainting(BaseONNXModel):
    _instance: Optional["LamaInpainting"] = None
    _initialized: bool = False
    _reference_count: int = 0

    def __init__(
        self, model_path=str(Path(config.get("model_dir")) / "m_repair_normal.onnx")
    ):
        super().__init__(model_path)

    def get_image(self, image):
        if isinstance(image, Image.Image):
            img = np.array(image)
        elif isinstance(image, np.ndarray):
            img = image.copy()
        else:
            raise Exception("输入图像应为PIL图像或numpy数组!")

        if img.ndim == 3:
            img = np.transpose(img, (2, 0, 1))  # chw
        elif img.ndim == 2:
            img = img[np.newaxis, ...]

        assert img.ndim == 3

        img = img.astype(np.float32) / 255
        return img

    def ceil_modulo(self, x, mod):
        if x % mod == 0:
            return x
        return (x // mod + 1) * mod

    def pad_img_to_modulo(self, img, mod):
        channels, height, width = img.shape
        out_height = self.ceil_modulo(height, mod)
        out_width = self.ceil_modulo(width, mod)
        return np.pad(
            img,
            ((0, 0), (0, out_height - height), (0, out_width - width)),
            mode="symmetric",
        )

    def prepare_img_and_mask(self, image, mask, pad_out_to_modulo=8):
        out_image = self.get_image(image)
        out_mask = self.get_image(mask)
        # 填充到指定的倍数（例如 8）
        if pad_out_to_modulo > 1:
            out_image = self.pad_img_to_modulo(out_image, pad_out_to_modulo)
            out_mask = self.pad_img_to_modulo(out_mask, pad_out_to_modulo)

        out_mask = (out_mask > 0) * 1  # Binarize the mask
        # print(out_mask.shape, np.max(out_mask), np.min(out_mask))

        # 添加批次维度
        out_image = np.expand_dims(out_image, axis=0)  # shape: [1, C, H, W]
        out_mask = np.expand_dims(out_mask, axis=0)  # shape: [1, C, H, W]

        return out_image, out_mask

    def inpaint(self, original_image: Image.Image, mask: Image.Image) -> Image.Image:
        """
        Inpaint the image using the provided mask and only paste the inpainted region
        back to the original image
        """
        # Save original size and make a copy of original image
        original_size = original_image.size
        result_image = original_image.copy()

        # Ensure input image is RGB
        if original_image.mode != "RGB":
            original_image = original_image.convert("RGB")

        # Convert red mask to binary mask
        if mask.mode != "L":
            # Convert to RGB if not already
            mask = mask.convert("RGB")
            # Extract red channel and create binary mask
            r, g, b = mask.split()
            # Create binary mask where red pixels (255, 0, 0) become white (255)
            mask = Image.eval(r, lambda x: 255 if x > 200 else 0)

        # Prepare inputs
        image_tensor, mask_tensor = self.prepare_img_and_mask(
            original_image.resize((512, 512)), mask.resize((512, 512))
        )

        try:
            outputs = self.ort_session.run(
                None,
                {
                    "image": image_tensor.astype(np.float32),
                    "mask": mask_tensor.astype(np.float32),
                },
            )

            output = outputs[0][0]
            # Process output
            output = output.transpose(1, 2, 0)  # chw -> hwc
            output = output.astype(np.uint8)
            inpainted_region = Image.fromarray(output)

            # Resize inpainted region and mask to original size
            inpainted_region = inpainted_region.resize(original_size)
            mask = mask.resize(original_size)

            # Paste only the inpainted region using the mask
            result_image.paste(inpainted_region, mask=mask)

            return result_image
        except Exception as e:
            logger.error(f"Error during inference: {str(e)}")
            logger.error(
                f"Model inputs - Image shape: {image_tensor.shape}, Mask shape: {mask_tensor.shape}"
            )
            raise e


if __name__ == "__main__":
    lama = LamaInpainting()
    image = "test.png"
    mask = "mask.png"
    # Load image and mask
    image = Image.open(image)
    mask = Image.open(mask)

    logger.info(f"Input image size: {image.size}, mode: {image.mode}")
    logger.info(f"Input mask size: {mask.size}, mode: {mask.mode}")

    # Perform inpainting
    result = lama.inpaint(image, mask)

    result.save("output1.png")
