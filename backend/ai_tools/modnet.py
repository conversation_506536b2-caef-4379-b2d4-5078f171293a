import sys
from pathlib import Path
from typing import Optional, Union
from io import BytesIO

import numpy as np
from PIL import Image

from .base_model import BaseONNXModel

BASE_DIR = Path(__file__).resolve().parent.parent

try:
    from utilities.log import logger
    from utilities.utils import read_image, refine_foreground, refine_foreground_bak
    from conf.config import config
except ImportError:
    sys.path.append(str(BASE_DIR))
    from utilities.log import logger
    from conf.config import config


class MODNetSegmentation(BaseONNXModel):
    _instance: Optional["MODNetSegmentation"] = None
    _initialized: bool = False
    _reference_count: int = 0

    def __init__(
        self,
        model_path=str(Path(config.get("model_dir")) / "people_segmentation.onnx"),
        model_input_size=[512, 512],
    ):
        super().__init__(model_path, model_input_size=model_input_size)

    def _init_specifics(self, model_input_size):
        if not isinstance(model_input_size, list) or len(model_input_size) != 2:
            raise ValueError("model_input_size must be a list with two elements")
        if any(not isinstance(size, int) or size <= 0 for size in model_input_size):
            raise ValueError("model_input_size elements must be positive integers")
        self.model_input_size = model_input_size

    def preprocess_image(self, im: np.ndarray) -> np.ndarray:
        # Convert to RGB if grayscale
        if len(im.shape) < 3:
            im = im[:, :, np.newaxis]
            im = np.concatenate([im, im, im], axis=2)
        elif im.shape[2] == 4:
            im = im[:, :, 0:3]

        # Resize
        try:
            im_resized = np.array(
                Image.fromarray(im).resize(self.model_input_size, Image.BILINEAR)
            )
        except Exception as e:
            raise RuntimeError(f"Error resizing image: {e}")

        # MODNet specific normalization: normalize to [-1, 1]
        im_tensor = (im_resized.astype(np.float32) - 127.5) / 127.5

        # HWC to CHW format
        im_tensor = np.transpose(im_tensor, (2, 0, 1))

        # Add batch dimension and ensure float32 type
        im_tensor = np.expand_dims(im_tensor, axis=0).astype(np.float32)
        return im_tensor

    def postprocess_image(self, result: np.ndarray, im_size) -> np.ndarray:
        if isinstance(im_size, tuple):
            im_size = list(im_size)
        # Resize the result image to match the original image size
        result = np.squeeze(result)
        try:
            result = np.array(Image.fromarray(result).resize(im_size, Image.BILINEAR))
        except Exception as e:
            raise RuntimeError(f"Error resizing result image: {e}")
        # Normalize the result image data
        ma = result.max()
        mi = result.min()
        result = (result - mi) / (ma - mi)
        # Convert to uint8 image
        im_array = (result * 255).astype(np.uint8)
        return im_array

    def segment_image(
        self,
        img_obj: Union[str, BytesIO, Image.Image],
        is_edge_optimization=False,
        edge_value=90,
        crop_to_content=False,
    ) -> Image.Image:
        if self.ort_session is None:
            self._load_model()

        # Read image
        orig_image, _ = read_image(img_obj)
        input_image = orig_image.convert("RGB")
        image_array = np.array(input_image)
        image_size = input_image.size

        # Preprocess
        image_tensor = self.preprocess_image(image_array)

        # Run inference
        ort_inputs = {self.ort_session.get_inputs()[0].name: image_tensor}
        try:
            matte = self.ort_session.run(None, ort_inputs)[0]
        except Exception as e:
            raise RuntimeError(f"MODNet inference failed: {e}")

        # Postprocess
        matte = self.postprocess_image(matte, image_size)
        pil_im = Image.fromarray(matte)
        if is_edge_optimization:
            try:
                result = refine_foreground(orig_image, pil_im, r=edge_value)
            except Exception as e:
                try:
                    result = refine_foreground_bak(orig_image, pil_im, r=edge_value)
                except Exception as e:
                    logger.error(f"Error processing images: {e}")
                    result = Image.new("RGBA", image_size, (0, 0, 0, 0))
                    result.paste(orig_image, mask=pil_im)
        else:
            # Create final image with alpha channel
            result = Image.new("RGBA", image_size, (0, 0, 0, 0))
            result.paste(orig_image, mask=pil_im)
        if crop_to_content:
            # 获取 alpha 通道
            alpha = result.split()[3]
            # 获取非透明区域的边界框
            bbox = alpha.getbbox()
            if bbox:
                # 裁剪图片到边界框
                result = result.crop(bbox)

        return result


if __name__ == "__main__":
    # Example usage
    segmentation = MODNetSegmentation()
    base_path = BASE_DIR / "hub_model" / "test_images"
    test_imgs = [
        "test.jpg",
        "portrait.jpg",
    ]

    for img_name in test_imgs:
        img_path = str(base_path / img_name)
        file_name = img_name.split(".")[0]
        try:
            no_bg_image = segmentation.segment_image(img_path)
            no_bg_image.save(str(base_path / f"modnet_{file_name}.png"))
            logger.info(f"Image {img_name} has been processed successfully.")
        except Exception as e:
            logger.error(f"Failed to process {img_name}: {e}")
