import sys
from pathlib import Path
from typing import Optional

import numpy as np
from PIL import Image


BASE_DIR = Path(__file__).resolve().parent.parent

try:
    from .base_model import BaseONNXModel
    from utilities.log import logger
    from conf.config import config
    from utilities.utils import read_image
except ImportError:
    sys.path.append(str(BASE_DIR))
    from utilities.log import logger
    from conf.config import config
    from utilities.utils import read_image
    from ai_tools.base_model import BaseONNXModel


class RealEsrGanBase(BaseONNXModel):
    _instance: Optional["RealEsrGanBase"] = None
    _initialized: bool = False
    _reference_count: int = 0

    def __init__(self, model_path=None):
        super().__init__(model_path)

    def upscale_image_custom_scale(self, img: Image.Image, scale: int = 1):
        # 如果目标是2倍，但模型是x4，那么要先缩小0.5倍
        model_scale = 4  # 你的模型是x4
        resize_factor = scale / model_scale  # 如 2/4 = 0.5

        if resize_factor != 1:
            new_w = int(img.width * resize_factor)
            new_h = int(img.height * resize_factor)
            img = img.resize((new_w, new_h), resample=Image.BICUBIC)

        return img

    def preprocess_image(self, im: np.ndarray) -> np.ndarray:
        # Convert to RGB if grayscale
        if len(im.shape) < 3:
            im = im[:, :, np.newaxis]
        try:
            im_resized = np.array(Image.fromarray(im))
        except Exception as e:
            raise RuntimeError(f"Error resizing image: {e}")
        # Normalize image pixel values to the [0, 1] range
        im_tensor = im_resized.astype(np.float32) / 255.0
        # HWC to CHW format
        im_tensor = np.transpose(im_tensor, (2, 0, 1))
        # Add batch dimension and ensure float32 type
        im_tensor = np.expand_dims(im_tensor, axis=0).astype(np.float32)
        return im_tensor

    def postprocess_image(self, output_tensor, target_size=None):
        output = (np.clip(np.squeeze(output_tensor), 0, 1) * 255).astype(
            np.uint8
        )  # 去归一化并转uint8
        output = np.transpose(output, (1, 2, 0))  # CHW → HWC
        if target_size:
            return Image.fromarray(output).resize(target_size, Image.BILINEAR)
        else:
            return Image.fromarray(output)

    def predict(self, img_obj: str, scale=4) -> Image.Image:
        orig_image, _ = read_image(img_obj)
        input_data = self.preprocess_image(np.array(orig_image.convert("RGB")))
        try:
            output = self.ort_session.run(
                [self.ort_session.get_outputs()[0].name],
                {self.ort_session.get_inputs()[0].name: input_data},
            )[0]
        except Exception as e:
            raise RuntimeError(f"ONNX inference failed: {e}")
        if isinstance(scale, str):
            scale = int(scale)
        if scale != 4:
            target_size = (orig_image.width * scale, orig_image.height * scale)
            result = self.postprocess_image(output, target_size)
        else:
            result = self.postprocess_image(output)
        return result


class RealEsrGanGeneral(RealEsrGanBase):
    _instance: Optional["RealEsrGanGeneral"] = None
    _initialized: bool = False
    _reference_count: int = 0

    def __init__(self, **kwargs):
        model_path = str(Path(config.get("model_dir")) / "real_general.onnx")
        super().__init__(model_path=model_path, **kwargs)


class RealEsrGanAnime(RealEsrGanBase):
    _instance: Optional["RealEsrGanAnime"] = None
    _initialized: bool = False
    _reference_count: int = 0

    def __init__(self, **kwargs):
        model_path = str(Path(config.get("model_dir")) / "real_anime.onnx")
        super().__init__(model_path=model_path, **kwargs)


_MODEL_CLASSES = {
    "gereral": RealEsrGanGeneral,
    "anime": RealEsrGanAnime,
}


def RealEsrGanAPP(model_type="gereral", **kwargs):
    model_class = _MODEL_CLASSES.get(model_type)
    if not model_class:
        raise ValueError(
            f"Invalid model_type. Must be one of {list(_MODEL_CLASSES.keys())}"
        )
    return model_class(**kwargs)


if __name__ == "__main__":
    test_imgs = ["0014.jpg"]
    base_path = BASE_DIR / "ai_tools"
    for model_name in _MODEL_CLASSES.keys():
        logger.info(f"--- Testing model: {model_name} ---")
        try:
            segmentation = RealEsrGanAPP(model_type=model_name)
            for img_name in test_imgs:
                img_path = str(base_path / img_name)
                if not Path(img_path).exists():
                    logger.warning(f"Test image not found: {img_path}")
                    continue
                file_name = img_name.split(".")[0]
                no_bg_image = segmentation.predict(img_path, scale=4)
                output_path = base_path / f"brn_{model_name}_{file_name}.jpg"
                no_bg_image.save(output_path)
                logger.info(
                    f"Image {img_name} processed successfully with {model_name}. Saved to {output_path}"
                )
        except Exception as e:
            logger.error(f"Failed to process with {model_name}: {e}", exc_info=True)
