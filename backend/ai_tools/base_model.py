import onnxruntime
import time
from pathlib import Path
import sys
import gc

BASE_DIR = Path(__file__).resolve().parent.parent

try:
    from utilities.log import logger
except ImportError:
    sys.path.append(str(BASE_DIR))
    from utilities.log import logger


class BaseONNXModel:
    def __new__(cls, *args, **kwargs):
        if not hasattr(cls, "_instance") or cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, model_path: str, **kwargs):
        if not self.__class__._initialized:
            self.model_path = model_path
            self._init_specifics(**kwargs)
            self.providers = self.get_available_providers()
            self.ort_session = None
            self._load_model()
            self.__class__._initialized = True
        self.__class__._reference_count += 1

    def _init_specifics(self, **kwargs):
        if kwargs:
            raise TypeError(f"Unsupported keyword arguments: {list(kwargs.keys())}")

    def get_available_providers(self):
        """获取可用的执行设备"""
        available_providers = onnxruntime.get_available_providers()
        if "CUDAExecutionProvider" in available_providers:
            return ["CUDAExecutionProvider", "CPUExecutionProvider"]
        if "DmlExecutionProvider" in available_providers:
            return ["DmlExecutionProvider", "CPUExecutionProvider"]
        # if "CoreMLExecutionProvider" in available_providers:
        #     return ["CoreMLExecutionProvider", "CPUExecutionProvider"]
        else:
            return ["CPUExecutionProvider"]

    def _load_model(self):
        """加载ONNX模型"""
        if self.ort_session is None:
            try:
                logger.info(f"Loading model: {self.model_path}")
                start_time = time.time()
                self.ort_session = onnxruntime.InferenceSession(
                    self.model_path, providers=self.providers
                )
                logger.info(
                    f"{self.model_path} Model loaded in {time.time() - start_time:.2f} seconds"
                )
            except Exception as e:
                raise RuntimeError("请先在设置中下载模型") from e

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.release()

    def release(self):
        """释放资源的方法"""
        try:
            # 防止引用计数变为负数
            if self.__class__._reference_count > 0:
                self.__class__._reference_count -= 1

            # 强制释放模式或引用计数为0时释放资源
            if self.__class__._reference_count == 0:
                logger.info(f"释放 {self.__class__.__name__} 模型资源")

                if self.ort_session is not None:
                    try:
                        # 尝试显式关闭session（如果有的话）
                        if hasattr(self.ort_session, 'end_profiling'):
                            self.ort_session.end_profiling()
                    except:
                        pass

                    # 删除session引用
                    del self.ort_session
                    self.ort_session = None

                # 重置类状态
                self.__class__._initialized = False
                self.__class__._instance = None

                # 强制垃圾回收
                gc.collect()

                logger.info(f"{self.__class__.__name__} 模型资源释放完成")

        except Exception as e:
            logger.error(f"释放 {self.__class__.__name__} 资源时出错: {e}")

    def force_release(self):
        """强制释放资源，忽略引用计数"""
        try:
            logger.info(f"强制释放 {self.__class__.__name__} 模型资源")

            if self.ort_session is not None:
                try:
                    if hasattr(self.ort_session, 'end_profiling'):
                        self.ort_session.end_profiling()
                except:
                    pass

                del self.ort_session
                self.ort_session = None

            # 重置所有状态
            self.__class__._reference_count = 0
            self.__class__._initialized = False
            self.__class__._instance = None

            # 强制垃圾回收
            gc.collect()

            logger.info(f"{self.__class__.__name__} 模型资源强制释放完成")

        except Exception as e:
            logger.error(f"强制释放 {self.__class__.__name__} 资源时出错: {e}")

    def __del__(self):
        """析构函数"""
        try:
            self.release()
        except:
            pass  # 析构函数中不应该抛出异常
