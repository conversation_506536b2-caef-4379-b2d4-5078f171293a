import sys
from pathlib import Path
from typing import Optional, Union
from io import BytesIO

import numpy as np
from PIL import Image

from .base_model import BaseONNXModel


BASE_DIR = Path(__file__).resolve().parent.parent


BASE_DIR = Path(__file__).resolve().parent.parent

try:
    from utilities.log import logger
    from conf.config import config
    from utilities.utils import refine_foreground, read_image, refine_foreground_bak
except ImportError:
    sys.path.append(str(BASE_DIR))
    from utilities.log import logger
    from conf.config import config
    from utilities.utils import refine_foreground, read_image


class BiRefNetBase(BaseONNXModel):
    _instance: Optional["BiRefNetBase"] = None
    _initialized: bool = False
    _reference_count: int = 0

    def __init__(self, model_path=None, model_input_size=(1024, 1024)):
        super().__init__(model_path, model_input_size=model_input_size)

    def _init_specifics(self, model_input_size):
        self.model_input_size = model_input_size

    def preprocess_image(self, im: np.ndarray) -> np.ndarray:
        if len(im.shape) < 3:
            im = im[:, :, np.newaxis]
        try:
            im_resized = np.array(
                Image.fromarray(im).resize(self.model_input_size, Image.BILINEAR)
            )
        except Exception as e:
            raise RuntimeError(f"Error resizing image: {e}")
        img_array = im_resized.astype(np.float32) / 255.0
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
        img_array = (img_array - mean) / std
        img_array = img_array.transpose(2, 0, 1)
        img_array = np.expand_dims(img_array, axis=0)
        return img_array.astype(np.float32)

    def process_mask_sharp(self, pred: np.ndarray, image_size: tuple) -> Image.Image:
        pred = np.squeeze(pred)
        mask = (pred > 0.5).astype(np.uint8) * 255
        mask_image = Image.fromarray(mask).resize(image_size, Image.NEAREST)
        return mask_image

    def process_mask_soft(self, pred: np.ndarray, image_size: tuple) -> Image.Image:
        pred = np.squeeze(pred)
        result = 1 / (1 + np.exp(-pred))  # Sigmoid activation
        im_array = (result * 255).astype(np.uint8)
        mask_image = Image.fromarray(im_array, mode="L").resize(
            image_size, Image.BILINEAR
        )
        return mask_image

    def segment_image(
        self,
        img_obj: Union[str, BytesIO, Image.Image],
        is_edge_optimization=False,
        edge_value=90,
        crop_to_content=False,
        edge_mode="soft",
    ) -> Image.Image:
        orig_image, _ = read_image(img_obj)
        input_image = orig_image.convert("RGB")
        image_array = np.array(input_image)
        image_size = (input_image.size[0], input_image.size[1])

        input_data = self.preprocess_image(image_array)

        input_name = self.ort_session.get_inputs()[0].name
        output_name = self.ort_session.get_outputs()[0].name
        pred = self.ort_session.run([output_name], {input_name: input_data})[0]

        if edge_mode == "sharp":
            pil_im = self.process_mask_sharp(pred, image_size)
        else:  # default to soft
            pil_im = self.process_mask_soft(pred, image_size)

        base_result = Image.new("RGBA", orig_image.size, (0, 0, 0, 0))
        base_result.paste(orig_image, mask=pil_im)

        if is_edge_optimization:
            try:
                logger.info("Attempting edge optimization...")
                result = refine_foreground(orig_image, pil_im, r=edge_value)
                logger.info("Edge optimization completed successfully")
            except Exception as e:
                try:
                    result = refine_foreground_bak(orig_image, pil_im, r=edge_value)
                except Exception as e:
                    logger.warning(
                        f"Edge optimization failed: {e}. Falling back to basic result."
                    )
                    result = base_result
        else:
            result = base_result

        if crop_to_content:
            alpha = result.split()[3]
            bbox = alpha.getbbox()
            if bbox:
                result = result.crop(bbox)

        return result


class BiRefNetEpoch100(BiRefNetBase):
    _instance: Optional["BiRefNetEpoch100"] = None
    _initialized: bool = False
    _reference_count: int = 0

    def __init__(self, **kwargs):
        model_path = str(Path(config.get("model_dir")) / "segmentation_high.onnx")
        super().__init__(model_path=model_path, **kwargs)


class BiRefNetTiny232(BiRefNetBase):
    _instance: Optional["BiRefNetTiny232"] = None
    _initialized: bool = False
    _reference_count: int = 0

    def __init__(self, **kwargs):
        model_path = str(Path(config.get("model_dir")) / "segmentation_normal.onnx")
        super().__init__(model_path=model_path, **kwargs)


class BiRefNetRMBG2(BiRefNetBase):
    _instance: Optional["BiRefNetRMBG2"] = None
    _initialized: bool = False
    _reference_count: int = 0

    def __init__(self, **kwargs):
        model_path = str(Path(config.get("model_dir")) / "rmbg2.onnx")
        super().__init__(model_path=model_path, **kwargs)

    def process_mask_soft(self, pred: np.ndarray, image_size: tuple) -> Image.Image:
        pred = np.squeeze(pred)
        result = pred  # rmbg2 model output is already normalized
        im_array = (result * 255).astype(np.uint8)
        mask_image = Image.fromarray(im_array, mode="L").resize(
            image_size, Image.BILINEAR
        )
        return mask_image


_MODEL_CLASSES = {
    "epoch_100": BiRefNetEpoch100,
    "tiny_232": BiRefNetTiny232,
    "rmbg2": BiRefNetRMBG2,
}


def BiRefNetAPP(model_type="epoch_100", **kwargs):
    model_class = _MODEL_CLASSES.get(model_type)
    if not model_class:
        raise ValueError(
            f"Invalid model_type. Must be one of {list(_MODEL_CLASSES.keys())}"
        )
    return model_class(**kwargs)


if __name__ == "__main__":
    test_imgs = ["test.jpg", "car.jpg"]
    base_path = BASE_DIR.parent / "hub_model" / "test_images"
    for model_name in _MODEL_CLASSES.keys():
        logger.info(f"--- Testing model: {model_name} ---")
        try:
            segmentation = BiRefNetAPP(model_type=model_name)
            for img_name in test_imgs:
                img_path = str(base_path / img_name)
                if not Path(img_path).exists():
                    logger.warning(f"Test image not found: {img_path}")
                    continue
                file_name = img_name.split(".")[0]
                no_bg_image = segmentation.segment_image(img_path, crop_to_content=True)
                output_path = base_path / f"brn_{model_name}_{file_name}.png"
                no_bg_image.save(output_path)
                logger.info(
                    f"Image {img_name} processed successfully with {model_name}. Saved to {output_path}"
                )
        except Exception as e:
            logger.error(f"Failed to process with {model_name}: {e}", exc_info=True)
