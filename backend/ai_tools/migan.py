import numpy as np
import onnxruntime
from PIL import Image
from pathlib import Path
import time
import sys
from typing import Optional

from .base_model import BaseONNXModel

BASE_DIR = Path(__file__).resolve().parent.parent

try:
    from utilities.log import logger
    from conf.config import config
except ImportError:
    sys.path.append(str(BASE_DIR))
    from utilities.log import logger
    from conf.config import config


def resize(image, max_size, interpolation=Image.BICUBIC):
    w, h = image.size
    if w > max_size or h > max_size:
        resize_ratio = max_size / w if w > h else max_size / h
        image = image.resize(
            (int(w * resize_ratio), int(h * resize_ratio)), interpolation
        )
    return image


def read_mask(mask_image: Image, invert=False):
    mask = resize(mask_image, max_size=512, interpolation=Image.NEAREST)
    mask = np.array(mask)
    if len(mask.shape) == 3:
        if mask.shape[2] == 4:
            _r, _g, _b, _a = np.rollaxis(mask, axis=-1)
            mask = np.dstack([_a, _a, _a])
        elif mask.shape[2] == 2:
            _l, _a = np.rollaxis(mask, axis=-1)
            mask = np.dstack([_a, _a, _a])
        elif mask.shape[2] == 3:
            _r, _g, _b = np.rollaxis(mask, axis=-1)
            mask = np.dstack([_r, _r, _r])
    else:
        mask = np.dstack([mask, mask, mask])  # 将mask扩展为三通道
    if invert:
        mask = 255 - mask
    mask[mask < 255] = 0
    return Image.fromarray(mask).convert("L")


def preprocess(img: Image, mask: Image, resolution: int) -> np.ndarray:
    """
    Preprocesses the image and mask, resizing them and normalizing pixel values.
    """
    # Resize images and masks to the required resolution
    img = img.resize((resolution, resolution), Image.BICUBIC)
    mask = mask.resize((resolution, resolution), Image.NEAREST)

    # Convert to numpy arrays (uint8) as required by ONNX model
    img = np.array(img).astype(np.uint8)  # Image should be uint8
    mask = np.array(mask).astype(np.uint8)  # Mask should be uint8

    # Ensure mask has shape [batch_size, 1, height, width]
    mask = np.expand_dims(mask, axis=0)  # Add batch dimension
    mask = np.expand_dims(mask, axis=0)  # Add channel dimension for mask

    # Ensure image has shape [batch_size, 3, height, width]
    img = np.expand_dims(img, axis=0)  # Add batch dimension
    img = np.transpose(img, (0, 3, 1, 2))  # HWC to NCHW format

    # Return a dictionary with both image and mask for model input
    return img, mask


class MiganInpainting(BaseONNXModel):
    _instance: Optional["MiganInpainting"] = None
    _initialized: bool = False
    _reference_count: int = 0

    def __init__(
        self,
        model_path: str = str(Path(config.get("model_dir")) / "m_repair_tiny.onnx"),
    ):
        super().__init__(model_path)

    def inpaint(self, img: Image, mask: Image, resolution: int = 512) -> Image:
        """处理图像的方法"""
        if self.ort_session is None:
            self._load_model()

        # 保存原始尺寸并创建结果图像副本
        original_size = img.size
        result_image = img.copy()

        # 确保输入图像为RGB
        img = img.convert("RGB")
        mask = read_mask(mask, invert=True)
        pil_mask = read_mask(mask, invert=True)
        # 预处理输入图像和mask
        img_input, mask_input = preprocess(img, mask, resolution=resolution)

        # 运行推理
        result = self.ort_session.run(
            None,
            {
                self.ort_session.get_inputs()[0].name: img_input.astype(
                    np.uint8
                ),  # Image input
                self.ort_session.get_inputs()[1].name: mask_input.astype(
                    np.uint8
                ),  # Mask input
            },
        )[0]

        # 后处理结果图像
        result = np.clip(result, 0, 255).astype(np.uint8)
        result = np.transpose(result, (0, 2, 3, 1))  # NCHW to NHWC
        result = result[0]  # Remove batch dimension
        inpainted_region = Image.fromarray(result)

        # 调整修复区域和mask到原始尺寸
        inpainted_region = inpainted_region.resize(original_size, Image.BICUBIC)
        pil_mask = pil_mask.resize(original_size)
        # 只将修复区域粘贴到原图的mask位置
        result_image.paste(inpainted_region, mask=pil_mask)

        return result_image

    def __del__(self):
        """析构函数"""
        self.release()


if __name__ == "__main__":
    model = MiganInpainting()
    image = Image.open("test.png").convert("RGB")
    mask = read_mask(Image.open("mask.png").convert("L"), invert=True)
    result = model.inpaint(image, mask, 512)
    result.save("output_migan.png")
