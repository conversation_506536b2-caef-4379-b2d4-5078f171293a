# backend

## 运行

- 开发环境
  - python 3.12.4
  - node v20.11.0

- 安装依赖
  - pdm 安装 参考 <https://pdm-project.org/zh-cn/latest/>
  - pnpm 安装 参考 <https://pnpm.io/zh/installation>

- 开发模式下运行
  - 安装后端依赖

    ```shell
    cd backend
    pdm install
    # onnxruntime cuda 支持     cuda12 cnn9
    pip install onnxruntime-gpu
    # directml
    pip install onnxruntime-directml
    ```

  - 安装前端依赖

    ```shell
    cd frontend
    pnpm install
    ```

  - 下载模型
  
    - mac

      ```shell
      # 激活虚拟环境
      cd backend
      source .venv/bin/activate
      ```

    - windows

      ```shell
      # windows
      # 激活虚拟环境
      cd backend
      .venv\Scripts\activate
      ```

  - 启动服务

    ```shell
    # 运行前端
    cd frontend && npm run dev
    # 运行后端
    cd backend && pmd dev
    ```

### 打包

- 构建前端

```shell
# 打包前端
python setup.py build_ext --inplace

pdm build-front
```

- 构建后端

```shell
# 打包后端,可执行文件
pdm build-win

# 构建mac 应用
pdm build-mac
```

### 遇见的问题和解决方案

- 1. windows打包后报病毒 和 windows 打包 [pyinstaller] Cannot read properties of undefined (reading 'api')
  - 解决方案：修改pywebview 版本为 5.3.1
  - 解决方案：需要把pyinstaller == 6.11.0 使用pyinstaller 6.9.0 会导致undefined (reading 'api') 使用6.12.0 版本会报病毒
