# This file tells git to ignore certain files or directories when committing changes.

# python virtual environment
.venv/

# compiled Python files
*.pyc

# temporary files
*.tmp

# data files

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
__pycache__/

# backend virtual environment

backend/.venv/  


# pdm virtual environment

# backend/pdm.lock
backend/.pdm.pycache/
backend/.pdm-python

# logs
backend/logs/

*.log

frontend/node_modules/
frontend/dist/

# compiled JavaScript files

frontend/build/
frontend/.vscode/


backend/build/
backend/.vscode/
backend/dist/

backend/libs/ai_models/

backend/web/

# pnpm-lock.yaml
frontend/pnpm-lock.yaml

.cache/

**/.vitepress/cache

node_modules/

.vscode/

simple-license/build/
simple-license/dist/

backend/pdm.lock
**/.DS_Store

*.dmg